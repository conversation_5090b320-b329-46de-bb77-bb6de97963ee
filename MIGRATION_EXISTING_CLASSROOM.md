# Migration untuk Sistem yang Sudah Memiliki Ruang Kelas

## Situasi Anda
✅ **Sudah memiliki ruang kelas**  
✅ **Sudah memasukkan siswa ke ruang kelas**  
🔄 **Perlu migrasi sistem lama ke sistem baru**

## Yang Akan Dilakukan Migration

### ✅ **AMAN - Tidak akan menimpa data Anda:**
- **SKIP** pembuatan data ruang_kelas (karena sudah ada)
- **SKIP** assignment siswa ke ruang kelas (karena sudah ada)
- **PRESERVE** semua data ruang_kelas dan siswa_ruang_kelas yang sudah ada

### 🔄 **Yang Akan Diupdate:**
- **Menambahkan** kolom `ruang_kelas_id` ke tabel yang belum ada
- **Mengupdate** record tugas lama untuk menggunakan `ruang_kelas_id`
- **Mengupdate** record absensi lama untuk menggunakan `ruang_kelas_id`
- **Mengupdate** record nilai lama untuk menggunakan `ruang_kelas_id`
- **Backup** data `kelas_id` lama di kolom `kelas_id_backup`

## File yang Sudah Disesuaikan

### 1. **`database/complete_classroom_migration.sql`**
- ✅ Dimodifikasi untuk skip pembuatan ruang_kelas
- ✅ Dimodifikasi untuk skip assignment siswa
- ✅ Fokus pada update record yang sudah ada

### 2. **`migrate_existing_classroom_data.php`**
- ✅ Script PHP khusus untuk situasi Anda
- ✅ Aman untuk data yang sudah ada
- ✅ Menampilkan status data sebelum migrasi

## Cara Menjalankan Migration

### **Langkah 1: Backup Database**
```sql
-- Backup database Anda terlebih dahulu!
mysqldump -u root -p db_absensi > backup_before_migration.sql
```

### **Langkah 2: Jalankan Migration**
Buka browser dan akses:
```
http://localhost/absen/migrate_existing_classroom_data.php
```

### **Langkah 3: Konfirmasi Migration**
- Sistem akan menampilkan status data Anda saat ini
- Klik "Proceed with Migration" jika sudah yakin

### **Langkah 4: Verifikasi Hasil**
```
http://localhost/absen/test_classroom_migration.php
```

## Apa yang Terjadi Saat Migration

### **Before Migration:**
```
tugas table:
- id: 1, kelas_id: 5, ruang_kelas_id: NULL
- id: 2, kelas_id: 3, ruang_kelas_id: NULL

absensi table:
- id: 1, kelas_id: 5, ruang_kelas_id: NULL
- id: 2, kelas_id: 3, ruang_kelas_id: NULL
```

### **After Migration:**
```
tugas table:
- id: 1, kelas_id: 5, ruang_kelas_id: 12
- id: 2, kelas_id: 3, ruang_kelas_id: 8

absensi table:
- id: 1, kelas_id: 5, ruang_kelas_id: 12
- id: 2, kelas_id: 3, ruang_kelas_id: 8
```

## Mapping Logic

Migration akan mencocokkan data lama dengan data baru berdasarkan:

### **Untuk Tugas & Absensi:**
```sql
-- Mencocokkan berdasarkan nama kelas, semester, dan tahun ajaran
kelas.nama_kelas = ruang_kelas.nama_ruang_kelas
AND tugas.semester = ruang_kelas.semester  
AND tugas.tahun_ajaran = ruang_kelas.tahun_ajaran
```

### **Untuk Nilai:**
```sql
-- Mencocokkan berdasarkan assignment siswa yang aktif
siswa -> siswa_ruang_kelas (aktif) -> ruang_kelas
```

## Setelah Migration

### **Form Tugas Baru:**
- ✅ Hanya menampilkan pilihan ruang kelas
- ✅ Tidak ada lagi pilihan kelas lama
- ✅ Data terfilter per semester/tahun ajaran

### **Form Absensi Baru:**
- ✅ Hanya menampilkan pilihan ruang kelas
- ✅ Siswa dimuat berdasarkan assignment ruang kelas
- ✅ Data terfilter per semester/tahun ajaran

### **Data Lama:**
- ✅ Tetap bisa diakses dan ditampilkan
- ✅ Sudah terhubung dengan sistem ruang kelas baru
- ✅ Backup data lama tersimpan di `kelas_id_backup`

## Troubleshooting

### **Jika Ada Record yang Tidak Termigrasikan:**
1. **Cek nama kelas** - pastikan nama di `kelas` sama dengan `ruang_kelas`
2. **Cek semester/tahun ajaran** - pastikan data konsisten
3. **Cek status ruang kelas** - pastikan status = 'aktif'

### **Jika Migration Gagal:**
1. **Database otomatis rollback** - data Anda aman
2. **Cek error message** - biasanya masalah permission atau data
3. **Hubungi support** jika perlu bantuan

## Keuntungan Setelah Migration

### **Sistem Lebih Terorganisir:**
- ✅ Data terpisah per semester/tahun ajaran
- ✅ Siswa bisa dipindah antar ruang kelas
- ✅ History assignment siswa tersimpan

### **Performa Lebih Baik:**
- ✅ Query lebih efisien dengan foreign key yang tepat
- ✅ Data loading lebih cepat
- ✅ Validasi data lebih ketat

### **Fitur Lebih Lengkap:**
- ✅ Manajemen kapasitas ruang kelas
- ✅ Status tracking siswa dan ruang kelas
- ✅ Fleksibilitas assignment siswa

## Rollback Plan

Jika perlu rollback (sangat jarang diperlukan):

```sql
-- Kembalikan ruang_kelas_id ke NULL
UPDATE tugas SET ruang_kelas_id = NULL;
UPDATE absensi SET ruang_kelas_id = NULL;
UPDATE nilai SET ruang_kelas_id = NULL;

-- Kembalikan kelas_id dari backup
UPDATE siswa SET kelas_id = kelas_id_backup WHERE kelas_id_backup IS NOT NULL;
```

---

**Status:** Ready for Migration  
**Safety Level:** HIGH (Preserves existing data)  
**Estimated Time:** 2-5 minutes  
**Rollback:** Available if needed
