<?php
require_once '../template/header.php';
require_once '../models/Absensi.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Siswa.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';

$absensi = new Absensi();
$kelas = new Kelas();
$mapel = new MataPelajaran();
$siswa = new Siswa();
$jadwal = new JadwalPelajaran();
$user = new User();

// Get jadwal_id from URL if coming from dashboard
$jadwal_id = isset($_GET['jadwal_id']) ? $_GET['jadwal_id'] : null;
$jadwal_info = null;

if ($jadwal_id) {
    // Get schedule info
    $jadwal->id = $jadwal_id;
    if ($jadwal->getOne()) {
        // For teachers, check if they teach this subject in any class
        if ($_SESSION['role'] === 'guru') {
            $guru_id = $user->getGuruId($_SESSION['user_id']);
            $authorized = false;
            $stmt = $jadwal->getByMapelAndGuru($jadwal->mapel_id, $guru_id);
            if ($stmt->rowCount() > 0) {
                $authorized = true;
            }
            
            if (!$authorized) {
                echo "<script>alert('Anda tidak memiliki akses untuk mengisi absensi mata pelajaran ini!'); window.location.href='index.php';</script>";
                exit;
            }
        }
        
        // Get custom date from URL if provided
        $custom_date = isset($_GET['tanggal']) ? $_GET['tanggal'] : date('Y-m-d');
        
        $jadwal_info = [
            'kelas_id' => $jadwal->kelas_id,
            'mapel_id' => $jadwal->mapel_id,
            'tanggal' => $custom_date
        ];
    }
}

$error = '';
$success = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate required fields
    $errors = [];

    if (empty($_POST['tanggal'])) {
        $errors[] = "Tanggal harus diisi";
    }

    if (empty($_POST['mapel_id'])) {
        $errors[] = "Mata pelajaran harus dipilih";
    }

    // Check if either ruang_kelas_id or kelas_id is provided
    $has_classroom = isset($_POST['ruang_kelas_id']) && !empty($_POST['ruang_kelas_id']);
    $has_class = isset($_POST['kelas_id']) && !empty($_POST['kelas_id']);

    if (!$has_classroom && !$has_class) {
        $errors[] = "Kelas atau Ruang Kelas harus dipilih";
    }

    // Check if student status data is provided
    if (!isset($_POST['status']) || empty($_POST['status'])) {
        $errors[] = "Data kehadiran siswa harus diisi";
    }

    if (empty($errors)) {
        $absensi->tanggal = $_POST['tanggal'];

        // Support both classroom and legacy class system
        if ($has_classroom) {
            $absensi->ruang_kelas_id = $_POST['ruang_kelas_id'];
        } else {
            $absensi->kelas_id = $_POST['kelas_id'];
        }

        $absensi->mapel_id = $_POST['mapel_id'];

        if ($absensi->create()) {
            $absensi_id = $absensi->id;
            $success = true;

            // Insert attendance details
            foreach ($_POST['status'] as $siswa_id => $status) {
                if (!$absensi->createDetail($absensi_id, $siswa_id, $status)) {
                    $success = false;
                    break;
                }
            }

            if ($success) {
                echo "<script>
                    alert('Absensi berhasil disimpan!');
                    window.location.href = 'index.php';
                </script>";
                exit;
            } else {
                $error = "Gagal menyimpan detail absensi";
            }
        } else {
            $error = "Gagal menyimpan data absensi. Pastikan kelas dan mata pelajaran yang dipilih valid.";
        }
    } else {
        $error = implode("<br>", $errors);
    }
}

// Get lists for dropdowns
$kelas_list = $kelas->getAll();
$mapel_list = $mapel->getAll();
?>

<style>
/* Override browser's default validation styling */
input:invalid, select:invalid {
    box-shadow: none !important;
    background-color: white !important;
}

/* Custom validation styling */
.is-invalid {
    border-color: #dc3545;
    background-color: white !important;
}
</style>

<div class="container-fluid">
    <h2 class="mb-4">Input Absensi</h2>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Form Input Absensi</h5>
        </div>
        <div class="card-body">
            <?php if (!empty($error)): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <form method="post" id="absensiForm">
                <div class="row g-3 mb-4">
                    <div class="col-md-4">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" 
                               value="<?php echo $jadwal_info ? $jadwal_info['tanggal'] : date('Y-m-d'); ?>">
                    </div>
                    <div class="col-md-4">
                        <?php if ($absensi->isClassroomSystemAvailable()): ?>
                            <label for="ruang_kelas_id" class="form-label">Ruang Kelas</label>
                            <select name="ruang_kelas_id" id="ruang_kelas_id" class="form-select">
                                <option value="">Pilih Ruang Kelas</option>
                                <?php
                                $classroom_list = $absensi->getActiveClassrooms();
                                if ($classroom_list):
                                    while ($row = $classroom_list->fetch(PDO::FETCH_ASSOC)):
                                ?>
                                    <option value="<?php echo $row['id']; ?>">
                                        <?php echo htmlspecialchars($row['nama_ruang_kelas']); ?>
                                        (<?php echo $row['jumlah_siswa']; ?> siswa)
                                    </option>
                                <?php
                                    endwhile;
                                endif;
                                ?>
                            </select>
                            <small class="form-text text-muted">Sistem ruang kelas baru - data terpisah per semester/tahun ajaran</small>
                        <?php else: ?>
                            <label for="kelas_id" class="form-label">Kelas</label>
                            <?php if ($_SESSION['role'] === 'guru'): ?>
                                <input type="hidden" name="kelas_id" value="<?php echo $jadwal_info ? $jadwal_info['kelas_id'] : ''; ?>">
                            <?php endif; ?>
                            <select name="<?php echo ($_SESSION['role'] === 'guru' ? 'kelas_id_display' : 'kelas_id'); ?>" id="kelas_id" class="form-select" <?php echo ($_SESSION['role'] === 'guru') ? 'disabled' : ''; ?>>
                                <option value="">Pilih Kelas</option>
                                <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                    <option value="<?php echo $row['id']; ?>"
                                            <?php echo ($jadwal_info && $jadwal_info['kelas_id'] == $row['id']) ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                            <small class="form-text text-muted">Sistem kelas lama - <a href="/absen/maintenance/classroom_migration.php">upgrade ke sistem ruang kelas</a></small>
                        <?php endif; ?>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                            <?php if ($_SESSION['role'] === 'guru' && isset($jadwal_info)): ?>
                            <input type="hidden" name="mapel_id" value="<?php echo $jadwal_info['mapel_id']; ?>">
                            <select name="mapel_id_display" id="mapel_id" class="form-select" disabled>
                                <option value="">Pilih Mata Pelajaran</option>
                            </select>
                            <?php else: ?>
                            <select name="mapel_id" id="mapel_id" class="form-select">
                                <option value="">Pilih Mata Pelajaran</option>
                            </select>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>

                <div id="studentList" class="table-responsive">
                    <!-- Student list will be loaded here -->
                </div>

                <div class="mt-3">
                    <button type="submit" class="btn btn-primary" id="submitBtn">
                        <i class="fas fa-save"></i> Simpan
                    </button>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Load students when class or classroom is selected
    $('#kelas_id, #ruang_kelas_id').change(function() {
        loadStudents();
        loadMapel();
    });

    // Initial load if class is pre-selected
    if ($('#kelas_id').val() || $('#ruang_kelas_id').val()) {
        loadStudents();
        loadMapel();
    }

    function loadMapel() {
        var kelas_id = $('#kelas_id').val() || $('#kelas_id_display').val();
        var ruang_kelas_id = $('#ruang_kelas_id').val();

        if (kelas_id || ruang_kelas_id) {
            var params = {};
            if (ruang_kelas_id) {
                params.ruang_kelas_id = ruang_kelas_id;
            } else {
                params.kelas_id = kelas_id;
            }

            $.get('get_mapel.php', params, function(data) {
                $('#mapel_id').html(data);
                <?php if ($jadwal_info): ?>
                $('#mapel_id').val('<?php echo $jadwal_info['mapel_id']; ?>');
                <?php endif; ?>
            });
        } else {
            $('#mapel_id').html('<option value="">Pilih Mata Pelajaran</option>');
        }
    }

    function loadStudents() {
        var kelas_id = $('#kelas_id').val() || $('#kelas_id_display').val();
        var ruang_kelas_id = $('#ruang_kelas_id').val();

        if (kelas_id || ruang_kelas_id) {
            var params = {};
            if (ruang_kelas_id) {
                params.ruang_kelas_id = ruang_kelas_id;
            } else {
                params.kelas_id = kelas_id;
            }

            $.get('get_students.php', params, function(data) {
                $('#studentList').html(data);
            });
        } else {
            $('#studentList').html('');
        }
    }

    // Quick select buttons
    $(document).on('click', '.quick-select', function(e) {
        e.preventDefault();
        var status = $(this).data('status');
        $('.status-select').val(status);
    });

    // Form validation
    $('#absensiForm').on('submit', function(e) {
        e.preventDefault(); // Prevent form submission

        let isValid = true;
        let errors = [];

        // Validate tanggal
        if (!$('#tanggal').val()) {
            isValid = false;
            errors.push('Tanggal harus diisi');
        }

        // Validate kelas/ruang kelas
        const kelasId = $('#kelas_id').val();
        const kelasIdDisplay = $('#kelas_id_display').val();
        const ruangKelasId = $('#ruang_kelas_id').val();

        if (!kelasId && !kelasIdDisplay && !ruangKelasId) {
            isValid = false;
            errors.push('Kelas harus dipilih');
        }

        // Validate mapel
        const mapelId = $('#mapel_id').val();
        const mapelIdDisplay = $('#mapel_id_display').val();

        if (!mapelId && !mapelIdDisplay) {
            isValid = false;
            errors.push('Mata Pelajaran harus dipilih');
        }

        // Validate student attendance data
        const statusInputs = $('input[name^="status["]');
        if (statusInputs.length === 0) {
            isValid = false;
            errors.push('Data siswa belum dimuat. Silakan pilih kelas dan mata pelajaran terlebih dahulu');
        }

        if (!isValid) {
            alert('Mohon perbaiki kesalahan berikut:\n• ' + errors.join('\n• '));
            return false;
        }

        // If valid, submit the form
        this.submit();
    });
});
</script>

<?php
require_once '../template/footer.php';
?>
