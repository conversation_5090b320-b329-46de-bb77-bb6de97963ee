<?php
require_once '../models/MataPelajaran.php';
require_once '../models/User.php';
require_once '../models/JadwalPelajaran.php';
session_start();

if (!isset($_GET['kelas_id'])) {
    exit('Kelas ID tidak ditemukan');
}

$mapel = new MataPelajaran();
$user = new User();
$jadwal = new JadwalPelajaran();
$kelas_id = $_GET['kelas_id'];

// Get guru_id if user is guru
$guru_id = null;
if ($_SESSION['role'] === 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);
}

// Get mata pelajaran for the selected kelas
$mapel_list = $mapel->getByKelas($kelas_id);

// Output options
echo '<option value="">Pilih <PERSON> Pelajaran</option>';
while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
    if ($_SESSION['role'] === 'guru') {
        // For guru role, check if they teach this subject in this class
        $stmt = $jadwal->getByMapelAndGuru($row['id'], $guru_id);
        $authorized = false;
        while ($jadwal_row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            if ($jadwal_row['kelas_id'] == $kelas_id) {
                $authorized = true;
                break;
            }
        }
        if (!$authorized) continue;
    }
    
    echo '<option value="' . $row['id'] . '">' . htmlspecialchars($row['nama_mapel']) . '</option>';
}
