<?php
require_once '../models/Siswa.php';
require_once '../models/Absensi.php';

// Support both classroom and legacy systems
$absensi = new Absensi();

if (isset($_GET['ruang_kelas_id']) && !empty($_GET['ruang_kelas_id'])) {
    // New classroom system
    $ruang_kelas_id = $_GET['ruang_kelas_id'];
    $result = $absensi->getStudentsByRuangKelas($ruang_kelas_id);

    if (!$result || $result->rowCount() == 0) {
        echo '<div class="text-center p-3">Tidak ada siswa di ruang kelas ini</div>';
        exit;
    }
} elseif (isset($_GET['kelas_id']) && !empty($_GET['kelas_id'])) {
    // Legacy class system
    $kelas_id = $_GET['kelas_id'];
    $siswa = new Siswa();
    $result = $siswa->getByKelas($kelas_id);

    if (!$result || $result->rowCount() == 0) {
        echo '<div class="text-center p-3">Tidak ada data siswa untuk kelas ini</div>';
        exit;
    }
} else {
    echo '<div class="text-center p-3">Silakan pilih kelas atau ruang kelas terlebih dahulu</div>';
    exit;
}
?>

<div class="mb-3">
    <div class="btn-group">
        <button type="button" class="btn btn-success btn-sm quick-select" data-status="hadir">
            <i class="fas fa-check"></i> Set Semua Hadir
        </button>
        <button type="button" class="btn btn-warning btn-sm quick-select" data-status="sakit">
            <i class="fas fa-procedures"></i> Set Semua Sakit
        </button>
        <button type="button" class="btn btn-info btn-sm quick-select" data-status="izin">
            <i class="fas fa-envelope"></i> Set Semua Izin
        </button>
        <button type="button" class="btn btn-danger btn-sm quick-select" data-status="alpha">
            <i class="fas fa-times"></i> Set Semua Alpha
        </button>
    </div>
</div>

<table class="table table-striped">
    <thead>
        <tr>
            <th width="5%">No</th>
            <th width="15%">NIS</th>
            <th>Nama Siswa</th>
            <th width="20%">Status Kehadiran</th>
        </tr>
    </thead>
    <tbody>
        <?php
        $no = 1;
        while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
        ?>
            <tr>
                <td><?php echo $no++; ?></td>
                <td><?php echo htmlspecialchars($row['nis']); ?></td>
                <td><?php echo htmlspecialchars($row['nama_siswa']); ?></td>
                <td>
                    <select name="status[<?php echo $row['id']; ?>]" class="form-select form-select-sm status-select">
                        <option value="">Pilih Status</option>
                        <option value="hadir">Hadir</option>
                        <option value="sakit">Sakit</option>
                        <option value="izin">Izin</option>
                        <option value="alpha">Alpha</option>
                    </select>
                </td>
            </tr>
        <?php
        }
        ?>
    </tbody>
</table>
