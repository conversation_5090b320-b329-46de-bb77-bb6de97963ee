<?php
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/Absensi.php';
require_once __DIR__ . '/../models/Kelas.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/MataPelajaran.php';

$absensi = new Absensi();
$kelas = new Kelas();
$mapel = new MataPelajaran();

// Get guru_id if user is a teacher
$guru_id = null;
if (isset($_SESSION['role']) && $_SESSION['role'] == 'guru') {
    $user = new User();
    $guru_id = $user->getGuruId($_SESSION['user_id']);
}

// Get list of classes for filter
$kelas_list = $kelas->getAll();

// Set default date range to current month if not provided
$start_date = isset($_GET['start_date']) ? $_GET['start_date'] : date('Y-m-01');
$end_date = isset($_GET['end_date']) ? $_GET['end_date'] : date('Y-m-t');
$selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';

// Get absensi data
if ($guru_id) {
    // Get teacher's subjects first
    $mapel_list = $mapel->getByGuru($guru_id);
    $mapel_ids = [];
    while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
        $mapel_ids[] = $row['id'];
    }
    
    if (!empty($mapel_ids)) {
        $result = $absensi->getByDateRangeAndMapel($start_date, $end_date, $selected_kelas, $mapel_ids);
    } else {
        $result = false;
    }
} else {
    $result = $absensi->getByDateRange($start_date, $end_date, $selected_kelas);
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2 class="mb-0">Data Absensi</h2>
        <?php if ($_SESSION['role'] === 'admin'): ?>
        <a href="create.php" class="btn btn-primary">
            <i class="fas fa-plus"></i> Tambah Absensi
        </a>
        <?php endif; ?>
    </div>

    <!-- Filter Form -->
    <div class="card mb-4">
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="start_date" class="form-label">Tanggal Mulai</label>
                    <input type="date" class="form-control" id="start_date" name="start_date" 
                           value="<?php echo $start_date; ?>">
                </div>
                <div class="col-md-3">
                    <label for="end_date" class="form-label">Tanggal Selesai</label>
                    <input type="date" class="form-control" id="end_date" name="end_date" 
                           value="<?php echo $end_date; ?>">
                </div>
                <div class="col-md-4">
                    <label for="kelas_id" class="form-label">Kelas</label>
                    <select name="kelas_id" id="kelas_id" class="form-select">
                        <option value="">Semua Kelas</option>
                        <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                            <option value="<?php echo $row['id']; ?>" 
                                    <?php echo ($selected_kelas == $row['id']) ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($row['nama_kelas']); ?>
                            </option>
                        <?php endwhile; ?>
                    </select>
                </div>
                <div class="col-md-2 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary w-100">
                        <i class="fas fa-search"></i> Filter
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Data Table -->
    <div class="card">
        <div class="card-body">
            <?php if (!$result || $result->rowCount() == 0): ?>
                <div class="alert alert-info">
                    <?php if ($_SESSION['role'] === 'guru'): ?>
                        <?php if (empty($mapel_ids)): ?>
                            Anda belum memiliki jadwal mengajar untuk periode ini.
                        <?php else: ?>
                            Tidak ada data absensi untuk periode yang dipilih.
                        <?php endif; ?>
                    <?php else: ?>
                        Tidak ada data absensi yang tersedia.
                    <?php endif; ?>
                </div>
            <?php endif; ?>

            <div class="table-responsive">
                <table class="table table-striped table-hover" id="absensiTable" <?php echo (!$result || $result->rowCount() == 0) ? 'style="display: none;"' : ''; ?>>
                    <thead>
                        <tr>
                            <th width="5%">No</th>
                            <th width="20%">Tanggal</th>
                            <th width="25%">Kelas</th>
                            <th width="35%">Mata Pelajaran</th>
                            <th width="15%">Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if ($result && $result->rowCount() > 0):
                            $no = 1;
                            while ($row = $result->fetch(PDO::FETCH_ASSOC)):
                        ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo date('d/m/Y', strtotime($row['tanggal'])); ?></td>
                                <td><?php echo htmlspecialchars($row['nama_kelas']); ?></td>
                                <td><?php echo htmlspecialchars($row['nama_mapel']); ?></td>
                                <td>
                                    <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">
                                        <i class="fas fa-edit"></i> Edit
                                    </a>
                                </td>
                            </tr>
                        <?php 
                            endwhile;
                        endif; 
                        ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    <?php if ($result && $result->rowCount() > 0): ?>
    $('#absensiTable').DataTable({
        "pageLength": 25,
        "order": [[1, "desc"]], // Sort by date column descending
        "responsive": true,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
    <?php endif; ?>
});
</script>

<?php
require_once __DIR__ . '/../template/footer.php';
?>
