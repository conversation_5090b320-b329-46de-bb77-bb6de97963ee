<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../models/Guru.php';
require_once '../template/header.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

$user = new User();
$users = $user->getAllUsers();

$success_msg = '';
$error_msg = '';

if (isset($_SESSION['message'])) {
    $success_msg = $_SESSION['message'];
    unset($_SESSION['message']);
}

if (isset($_SESSION['error'])) {
    $error_msg = $_SESSION['error'];
    unset($_SESSION['error']);
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Manajemen Akun</h5>
                <div>
                    <a href="ubah_password_admin.php" class="btn btn-warning">
                        <i class="fas fa-key"></i> Ubah Password Admin
                    </a>
                    <a href="create.php" class="btn btn-primary ms-2">
                        <i class="fas fa-plus"></i> Tambah Akun
                    </a>
                </div>
            </div>
            <div class="card-body">
                <?php if ($success_msg): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php echo $success_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                
                <?php if ($error_msg): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php echo $error_msg; ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="accountTable">
                        <thead>
                            <tr>
                                <th>No.</th>
                                <th>Username</th>
                                <th>Nama Lengkap</th>
                                <th>Role</th>
                                <th>Status</th>
                                <th>Terakhir Login</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            $no = 1;
                            foreach ($users as $row): 
                                // Skip admin account from being displayed
                                if ($row['username'] === 'admin') continue;
                            ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo $row['username'] === 'admin' ? '-' : htmlspecialchars($row['username']); ?></td>
                                    <td><?php echo htmlspecialchars($row['nama_lengkap']); ?></td>
                                    <td><?php echo ucfirst($row['role']); ?></td>
                                    <td>
                                        <?php if ($row['role'] === 'guru'): 
                                            $guru = new Guru();
                                            $guru_status = $guru->getStatusByNama($row['nama_lengkap']);
                                            echo $guru_status ? '<span class="badge bg-success">Aktif</span>' : '<span class="badge bg-danger">Nonaktif</span>';
                                        else:
                                            echo '<span class="badge bg-success">Aktif</span>';
                                        endif; ?>
                                    </td>
                                    <td><?php echo isset($row['last_login']) && $row['last_login'] ? date('d/m/Y H:i', strtotime($row['last_login'])) : '-'; ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <?php if ($row['username'] !== 'admin'): ?>
                                                <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm" title="Edit">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="reset_password.php?id=<?php echo $row['id']; ?>" class="btn btn-info btn-sm" title="Reset Password" onclick="return confirm('Reset password untuk akun ini?')">
                                                    <i class="fas fa-key"></i>
                                                </a>
                                                <a href="delete.php?id=<?php echo $row['id']; ?>" class="btn btn-danger btn-sm" title="Hapus" onclick="return confirm('Yakin ingin menghapus akun ini?')">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            <?php else: ?>
                                                <a href="../profil/ubah_password_admin.php" class="btn btn-info btn-sm" title="Ubah Password">
                                                    <i class="fas fa-key"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#accountTable').DataTable({
        "pageLength": 25,
        "order": [[0, "asc"]],
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
