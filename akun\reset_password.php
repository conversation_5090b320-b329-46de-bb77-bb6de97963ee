<?php
require_once '../config/database.php';
require_once '../models/User.php';
require_once '../template/header.php';

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header("Location: ../index.php");
    exit();
}

if (!isset($_GET['id'])) {
    header("Location: index.php");
    exit();
}

$user = new User();
$user->id = $_GET['id'];

if (!$user->getOne()) {
    header("Location: index.php");
    exit();
}

// Generate random password
$new_password = substr(str_shuffle('abcdefghjkmnpqrstuvwxyzABCDEFGHJKMNPQRSTUVWXYZ23456789'), 0, 8);
$user->password = $new_password;

if ($user->updatePassword()) {
    $_SESSION['message'] = sprintf(
        "Password berhasil direset! Password baru untuk akun %s: %s", 
        htmlspecialchars($user->username), 
        $new_password
    );
} else {
    $_SESSION['error'] = "Gagal mereset password!";
}

header("Location: index.php");
exit();
?>
