<?php
session_start();
require_once '../config/database.php';
require_once '../models/Alumni.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

$alumniModel = new Alumni();
$alumni_list = $alumniModel->getAllAlumni();
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Data Alumni - SIHADIR</title>
    <?php include '../template/header.php'; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Data Alumni</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-bordered" id="dataTable">
                                <thead>
                                    <tr>
                                        <th>NIS</th>
                                        <th>Nama Alumni</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Kelas Terakhir</th>
                                        <th>Tahun Lulus</th>
                                        <th>No. Telp</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($alumni_list as $alumni): ?>
                                        <tr>
                                            <td><?php echo $alumni['nis']; ?></td>
                                            <td><?php echo $alumni['nama_siswa']; ?></td>
                                            <td><?php echo $alumni['jenis_kelamin']; ?></td>
                                            <td><?php echo $alumni['kelas_terakhir']; ?></td>
                                            <td><?php echo $alumni['tahun_lulus']; ?></td>
                                            <td><?php echo $alumni['no_telp']; ?></td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include '../template/footer.php'; ?>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                order: [[4, 'desc'], [1, 'asc']]
            });
        });
    </script>
</body>
</html>
