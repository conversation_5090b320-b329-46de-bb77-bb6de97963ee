<?php
require_once '../config/database.php';
require_once '../vendor/autoload.php'; // Pastikan Anda telah menginstal PhpSpreadsheet melalui composer

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Cek autentikasi
if (!isset($_SESSION['user_id'])) {
    header("Location: ../login.php");
    exit();
}

// Fungsi untuk mendapatkan data kehadiran
function getDataKehadiran($conn, $startDate, $endDate, $kelasId = null) {
    $where = "";
    if ($kelasId) {
        $where = " AND s.id_kelas = " . intval($kelasId);
    }

    $query = "SELECT s.nama as nama_siswa, k.nama_kelas, 
              COUNT(CASE WHEN a.status = 'Hadir' THEN 1 END) as hadir,
              COUNT(CASE WHEN a.status = 'Izin' THEN 1 END) as izin,
              COUNT(CASE WHEN a.status = 'Sakit' THEN 1 END) as sakit,
              COUNT(CASE WHEN a.status = 'Alpha' THEN 1 END) as alpha
              FROM siswa s
              JOIN kelas k ON s.id_kelas = k.id
              LEFT JOIN absensi a ON s.id = a.id_siswa
              WHERE a.tanggal BETWEEN ? AND ? $where
              GROUP BY s.id
              ORDER BY k.nama_kelas, s.nama";
    
    $stmt = mysqli_prepare($conn, $query);
    mysqli_stmt_bind_param($stmt, "ss", $startDate, $endDate);
    mysqli_stmt_execute($stmt);
    return mysqli_stmt_get_result($stmt);
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $startDate = $_POST['start_date'] ?? date('Y-m-d', strtotime('-30 days'));
    $endDate = $_POST['end_date'] ?? date('Y-m-d');
    $kelasId = $_POST['kelas_id'] ?? null;
    $format = $_POST['format'] ?? 'xlsx';

    $data = getDataKehadiran($conn, $startDate, $endDate, $kelasId);

    if ($format === 'xlsx') {
        // Buat spreadsheet baru
        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set judul
        $sheet->setCellValue('A1', 'Laporan Kehadiran Siswa');
        $sheet->setCellValue('A2', 'Periode: ' . $startDate . ' s/d ' . $endDate);

        // Set header kolom
        $sheet->setCellValue('A4', 'No');
        $sheet->setCellValue('B4', 'Nama Siswa');
        $sheet->setCellValue('C4', 'Kelas');
        $sheet->setCellValue('D4', 'Hadir');
        $sheet->setCellValue('E4', 'Izin');
        $sheet->setCellValue('F4', 'Sakit');
        $sheet->setCellValue('G4', 'Alpha');
        $sheet->setCellValue('H4', 'Total');
        $sheet->setCellValue('I4', 'Persentase Kehadiran');

        // Style header
        $headerStyle = [
            'font' => ['bold' => true],
            'fill' => [
                'fillType' => Fill::FILL_SOLID,
                'startColor' => ['rgb' => 'E2EFDA']
            ],
            'borders' => [
                'allBorders' => ['borderStyle' => Border::BORDER_THIN]
            ]
        ];
        $sheet->getStyle('A4:I4')->applyFromArray($headerStyle);

        // Isi data
        $row = 5;
        $no = 1;
        while ($record = mysqli_fetch_assoc($data)) {
            $total = $record['hadir'] + $record['izin'] + $record['sakit'] + $record['alpha'];
            $persentase = $total > 0 ? round(($record['hadir'] / $total) * 100, 2) : 0;

            $sheet->setCellValue('A' . $row, $no);
            $sheet->setCellValue('B' . $row, $record['nama_siswa']);
            $sheet->setCellValue('C' . $row, $record['nama_kelas']);
            $sheet->setCellValue('D' . $row, $record['hadir']);
            $sheet->setCellValue('E' . $row, $record['izin']);
            $sheet->setCellValue('F' . $row, $record['sakit']);
            $sheet->setCellValue('G' . $row, $record['alpha']);
            $sheet->setCellValue('H' . $row, $total);
            $sheet->setCellValue('I' . $row, $persentase . '%');

            $row++;
            $no++;
        }

        // Auto-size kolom
        foreach (range('A', 'I') as $col) {
            $sheet->getColumnDimension($col)->setAutoSize(true);
        }

        // Set header untuk download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment;filename="Laporan_Kehadiran_' . date('Y-m-d') . '.xlsx"');
        header('Cache-Control: max-age=0');

        $writer = new Xlsx($spreadsheet);
        $writer->save('php://output');
        exit;
    } else if ($format === 'csv') {
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment;filename="Laporan_Kehadiran_' . date('Y-m-d') . '.csv"');

        $output = fopen('php://output', 'w');
        
        // Add BOM for Excel UTF-8 compatibility
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
        
        // Header
        fputcsv($output, ['Nama Siswa', 'Kelas', 'Hadir', 'Izin', 'Sakit', 'Alpha', 'Total', 'Persentase Kehadiran']);
        
        // Data
        while ($record = mysqli_fetch_assoc($data)) {
            $total = $record['hadir'] + $record['izin'] + $record['sakit'] + $record['alpha'];
            $persentase = $total > 0 ? round(($record['hadir'] / $total) * 100, 2) : 0;
            
            fputcsv($output, [
                $record['nama_siswa'],
                $record['nama_kelas'],
                $record['hadir'],
                $record['izin'],
                $record['sakit'],
                $record['alpha'],
                $total,
                $persentase . '%'
            ]);
        }
        
        fclose($output);
        exit;
    }
}
?>

<!DOCTYPE html>
<html>
<head>
    <title>Export Laporan Kehadiran</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <style>
        .export-container {
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-control {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
    </style>
</head>
<body>
    <div class="export-container">
        <h2>Export Laporan Kehadiran</h2>
        <form method="POST">
            <div class="form-group">
                <label>Periode Awal:</label>
                <input type="text" name="start_date" class="form-control datepicker" required
                       value="<?php echo date('Y-m-d', strtotime('-30 days')); ?>">
            </div>
            
            <div class="form-group">
                <label>Periode Akhir:</label>
                <input type="text" name="end_date" class="form-control datepicker" required
                       value="<?php echo date('Y-m-d'); ?>">
            </div>
            
            <div class="form-group">
                <label>Kelas:</label>
                <select name="kelas_id" class="form-control">
                    <option value="">Semua Kelas</option>
                    <?php
                    $query = "SELECT id, nama_kelas FROM kelas ORDER BY nama_kelas";
                    $result = mysqli_query($conn, $query);
                    while ($row = mysqli_fetch_assoc($result)) {
                        echo "<option value='" . $row['id'] . "'>" . $row['nama_kelas'] . "</option>";
                    }
                    ?>
                </select>
            </div>
            
            <div class="form-group">
                <label>Format:</label>
                <select name="format" class="form-control">
                    <option value="xlsx">Excel (XLSX)</option>
                    <option value="csv">CSV</option>
                </select>
            </div>
            
            <button type="submit" class="btn btn-primary">Export</button>
        </form>
    </div>

    <script>
        flatpickr(".datepicker", {
            dateFormat: "Y-m-d"
        });
    </script>
</body>
</html>
