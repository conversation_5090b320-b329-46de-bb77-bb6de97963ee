/* Custom styles */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.footer {
    margin-top: auto;
}

/* Card styles */
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    margin-bottom: 1.5rem;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0,0,0,.125);
}

/* Table styles */
.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

/* Button styles */
.btn {
    margin-right: 0.5rem;
}

.btn i {
    margin-right: 0.5rem;
}

/* Form styles */
.form-group {
    margin-bottom: 1rem;
}

/* Alert styles */
.alert {
    margin-bottom: 1rem;
}

/* Navigation active state */
.nav-link.active {
    background-color: rgba(255,255,255,0.1);
    border-radius: 0.25rem;
}

/* Custom spacing */
.mt-4 {
    margin-top: 2rem !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
