// Initialize DataTable
$(document).ready(function() {
    if ($.fn.DataTable) {
        $('#tableKelas').DataTable({
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
            },
            "responsive": true,
            "order": [[1, 'asc']]
        });
    } else {
        console.error('DataTable plugin not loaded');
    }
});

// Handle delete confirmation
function confirmDelete(id, namaKelas) {
    if (confirm('Apakah Anda yakin ingin menghapus kelas "' + namaKelas + '"?\nSemua data terkait kelas ini (jadwal, absensi) juga akan dihapus.')) {
        window.location.href = 'delete.php?id=' + id;
        return true;
    }
    return false;
}
