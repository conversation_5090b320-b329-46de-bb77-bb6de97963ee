<?php
require_once '../template/header.php';
require_once '../models/KomentarBerita.php';

if (!isset($_SESSION['user_id'])) {
    header("Location: ../index.php");
    exit();
}

$komentar = new KomentarBerita();
$success_msg = "";
$error_msg = "";

if (isset($_GET['id'])) {
    $komentar->id = $_GET['id'];
    if (!$komentar->getOne()) {
        header("Location: ../index.php");
        exit();
    }

    // Check if user owns the comment or is admin
    if ($komentar->user_id != $_SESSION['user_id'] && $_SESSION['role'] != 'admin') {
        header("Location: ../index.php");
        exit();
    }
} else {
    header("Location: ../index.php");
    exit();
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['komentar'])) {
        $komentar->komentar = $_POST['komentar'];
        if ($komentar->update()) {
            header("Location: view.php?id=" . $komentar->berita_id . "#komentar-" . $komentar->id);
            exit();
        } else {
            $error_msg = "Gagal mengupdate komentar";
        }
    }
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Edit Komentar</h5>
                    <a href="view.php?id=<?php echo $komentar->berita_id; ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($error_msg): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_msg; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form method="post">
                        <div class="mb-3">
                            <label for="komentar" class="form-label">Komentar</label>
                            <textarea name="komentar" id="komentar" class="form-control" rows="3" required><?php echo htmlspecialchars($komentar->komentar); ?></textarea>
                        </div>
                        <button type="submit" class="btn btn-primary">Simpan Perubahan</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
