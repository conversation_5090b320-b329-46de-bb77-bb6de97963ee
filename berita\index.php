<?php
require_once __DIR__ . '/../middleware/auth.php';
if (!isset($_SESSION['user_id']) || !in_array($_SESSION['role'], ['guru', 'admin'])) {
    header("Location: ../403.php");
    exit();
}

require_once '../template/header.php';
require_once '../models/Berita.php';
require_once '../config/database.php';

$berita = new Berita();
$result = $berita->getAll();

// Handle success message
$success_msg = isset($_GET['success']) ? "Data berhasil disimpan" : "";
$error_msg = isset($_GET['error']) ? "Terjadi kesalahan" : "";
?>

<div class="content-wrapper">
    <div class="content-header">
        <div class="container-fluid">
            <div class="row mb-2">
                <div class="col-sm-6">
                    <h1 class="m-0">Berita</h1>
                </div>
                <div class="col-sm-6">
                    <ol class="breadcrumb float-sm-right">
                        <li class="breadcrumb-item"><a href="../dashboard">Dashboard</a></li>
                        <li class="breadcrumb-item active">Berita</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>

    <section class="content">
        <div class="container-fluid">
            <?php if ($success_msg): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?php echo $success_msg; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if ($error_msg): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?php echo $error_msg; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Daftar Berita</h5>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Berita
                    </a>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="tableBerita">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Judul</th>
                                    <th>Pembuat</th>
                                    <th>Tanggal</th>
                                    <th>Komentar</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                $hasData = false;
                                while ($row = $result->fetch(PDO::FETCH_ASSOC)) {
                                    $hasData = true;
                                ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo htmlspecialchars($row['judul']); ?></td>
                                    <td><?php echo htmlspecialchars($row['nama_pembuat']); ?></td>
                                    <td><?php echo date('d/m/Y H:i', strtotime($row['created_at'])); ?></td>
                                    <td>
                                        <?php
                                        $comment_count = $row['comment_count'] ?? 0;
                                        echo $comment_count . ' komentar';
                                        if ($row['last_comment_at']) {
                                            echo '<br><small class="text-muted">Terakhir: ' . date('d/m/Y H:i', strtotime($row['last_comment_at'])) . '</small>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <a href="view.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-info">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <?php if ($_SESSION['user_id'] == $row['created_by'] || $_SESSION['role'] == 'admin'): ?>
                                            <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-warning">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $row['id']; ?>)"
                                               class="btn btn-sm btn-danger">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                        <?php if (!$hasData): ?>
                            <p class="text-center mt-3">Tidak ada data berita</p>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

<script>
$(document).ready(function() {
    $('#tableBerita').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data berita",
            "zeroRecords": "Tidak ditemukan data yang sesuai"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[3, "desc"]], // Urutkan berdasarkan tanggal terbaru
        "columnDefs": [
            {"orderable": false, "targets": 5}, // Kolom aksi tidak bisa diurutkan
            {"width": "150px", "targets": 5} // Atur lebar kolom aksi
        ],
        "initComplete": function(settings, json) {
            // Tambahkan margin atas pada info dan pagination
            $('.dataTables_info, .dataTables_paginate').addClass('mt-3');
        }
    });
});

function confirmDelete(id) {
    if (confirm('Apakah Anda yakin ingin menghapus berita ini?')) {
        window.location.href = 'delete.php?id=' + id;
    }
}
</script>

<?php
require_once '../template/footer.php';
?>
