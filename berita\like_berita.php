<?php
session_start();
require_once '../models/Like.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if (!isset($_POST['berita_id']) || !isset($_POST['is_dislike'])) {
    echo json_encode(['success' => false, 'message' => 'Missing parameters']);
    exit;
}

$berita_id = $_POST['berita_id'];
$user_id = $_SESSION['user_id'];
$is_dislike = filter_var($_POST['is_dislike'], FILTER_VALIDATE_BOOLEAN);

$like = new Like();
$result = $like->likeBerita($berita_id, $user_id, $is_dislike);

if ($result) {
    $status = $like->isBeritaLiked($berita_id, $user_id);
    $counts = $like->getBeritaLikeCount($berita_id);
    echo json_encode([
        'success' => true,
        'status' => $status,
        'likes' => $counts['likes'] ?? 0,
        'dislikes' => $counts['dislikes'] ?? 0
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to process like/dislike']);
}
