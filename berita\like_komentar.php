<?php
session_start();
require_once '../models/Like.php';

header('Content-Type: application/json');

if (!isset($_SESSION['user_id'])) {
    echo json_encode(['success' => false, 'message' => 'Unauthorized']);
    exit;
}

if (!isset($_POST['komentar_id']) || !isset($_POST['is_dislike'])) {
    echo json_encode(['success' => false, 'message' => 'Missing parameters']);
    exit;
}

$komentar_id = $_POST['komentar_id'];
$user_id = $_SESSION['user_id'];
$is_dislike = filter_var($_POST['is_dislike'], FILTER_VALIDATE_BOOLEAN);

$like = new Like();
$result = $like->likeKomentar($komentar_id, $user_id, $is_dislike);

if ($result) {
    $status = $like->isKomentarLiked($komentar_id, $user_id);
    $counts = $like->getKomentarLikeCount($komentar_id);
    echo json_encode([
        'success' => true,
        'status' => $status,
        'likes' => $counts['likes'] ?? 0,
        'dislikes' => $counts['dislikes'] ?? 0
    ]);
} else {
    echo json_encode(['success' => false, 'message' => 'Failed to process like/dislike']);
}
