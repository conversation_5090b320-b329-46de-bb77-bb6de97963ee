<?php
require_once '../template/header.php';
require_once '../models/Berita.php';

$berita = new Berita();
$error_msg = "";

// Pagination
$page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
$per_page = 10;
$offset = ($page - 1) * $per_page;

// Filter berita berdasarkan role dan parameter
$my_berita = isset($_GET['my']) && $_GET['my'] == 1;
$where_clause = "";
$params = [];

if ($_SESSION['role'] === 'guru' && $my_berita) {
    $where_clause = "WHERE b.created_by = :user_id";
    $params[':user_id'] = $_SESSION['user_id'];
}

// Get total for pagination
$total_berita = $berita->getCount($where_clause, $params);
$total_pages = ceil($total_berita / $per_page);

// Get berita list
$berita_list = $berita->getAllPaginated($offset, $per_page, $where_clause, $params);

// Handle success/error messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : "";
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : "";
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <?php 
                        if ($_SESSION['role'] === 'guru' && $my_berita) {
                            echo "Berita Saya";
                        } else {
                            echo "Daftar Berita";
                        }
                        ?>
                    </h5>
                    <div>
                        <?php if ($_SESSION['role'] === 'admin' || $_SESSION['role'] === 'guru'): ?>
                            <a href="create.php" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Tambah Berita
                            </a>
                        <?php endif; ?>
                    </div>
                </div>
                <div class="card-body">
                    <?php if ($success_msg): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_msg; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error_msg): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_msg; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <div class="list-group">
                        <?php while ($row = $berita_list->fetch(PDO::FETCH_ASSOC)): ?>
                            <div class="list-group-item">
                                <div class="d-flex w-100 justify-content-between align-items-center">
                                    <div>
                                        <h6 class="mb-1">
                                            <a href="view.php?id=<?php echo $row['id']; ?>" class="text-decoration-none">
                                                <?php echo htmlspecialchars($row['judul']); ?>
                                                <?php 
                                                $created_at = new DateTime($row['created_at']);
                                                $three_days_ago = new DateTime('-3 days');
                                                if ($created_at > $three_days_ago): 
                                                ?>
                                                    <span class="badge bg-danger">NEW</span>
                                                <?php endif; ?>
                                            </a>
                                        </h6>
                                        <small class="text-muted">
                                            Oleh: <?php echo htmlspecialchars($row['nama_pembuat']); ?> | 
                                            <?php echo date('d/m/Y', strtotime($row['created_at'])); ?>
                                        </small>
                                    </div>
                                    <?php if ($_SESSION['role'] === 'admin' || ($_SESSION['role'] === 'guru' && $row['created_by'] == $_SESSION['user_id'])): ?>
                                        <div>
                                            <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-warning btn-sm">
                                                <i class="fas fa-edit"></i> Edit
                                            </a>
                                            <a href="delete.php?id=<?php echo $row['id']; ?>" 
                                               class="btn btn-danger btn-sm"
                                               onclick="return confirm('Apakah Anda yakin ingin menghapus berita ini?');">
                                                <i class="fas fa-trash"></i> Hapus
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>

                    <?php if ($total_pages > 1): ?>
                        <nav aria-label="Berita navigation" class="mt-4">
                            <ul class="pagination justify-content-center">
                                <?php if ($page > 1): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo ($page - 1); ?><?php echo $my_berita ? '&my=1' : ''; ?>">Previous</a>
                                    </li>
                                <?php endif; ?>
                                
                                <?php for ($i = 1; $i <= $total_pages; $i++): ?>
                                    <li class="page-item <?php echo $i === $page ? 'active' : ''; ?>">
                                        <a class="page-link" href="?page=<?php echo $i; ?><?php echo $my_berita ? '&my=1' : ''; ?>"><?php echo $i; ?></a>
                                    </li>
                                <?php endfor; ?>
                                
                                <?php if ($page < $total_pages): ?>
                                    <li class="page-item">
                                        <a class="page-link" href="?page=<?php echo ($page + 1); ?><?php echo $my_berita ? '&my=1' : ''; ?>">Next</a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </nav>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
