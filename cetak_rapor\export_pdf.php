<?php
// First line: turn off output buffering
ob_end_clean();

// Start new output buffering
ob_start();

require_once '../vendor/autoload.php';
require_once '../config/database.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Guru.php';
require_once '../models/User.php';
require_once '../models/Siswa.php';
require_once '../models/Rapor.php';
require_once '../models/TahunAjaran.php';
require_once '../models/MataPelajaran.php';

use Dompdf\Dompdf;
use Dompdf\Options;

session_start();

// Check if user is logged in and is a guru or admin
if (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'guru' && $_SESSION['role'] !== 'admin')) {
    header("Location: /absen/403.php");
    exit();
}

try {
    // Check required parameters
    if (!isset($_GET['kelas_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
        die("Parameter tidak lengkap.");
    }

    // Initialize objects
    $kelas = new Kelas();
    $siswa = new Siswa();
    $rapor = new Rapor();
    $guru = new Guru();
    $user = new User();
    $tahunAjaran = new TahunAjaran();

    // Get parameters
    $kelas_id = $_GET['kelas_id'];
    $semester = $_GET['semester'];
    $tahun_ajaran = $_GET['tahun_ajaran'];
    $all_students = isset($_GET['all']) && $_GET['all'] == 1;
    $siswa_id = isset($_GET['siswa_id']) ? $_GET['siswa_id'] : null;

    // Verify access if user is a teacher
    if ($_SESSION['role'] === 'guru') {
        $guru_id = $user->getGuruId($_SESSION['user_id']);

        if (!$guru_id) {
            die("Data guru tidak ditemukan.");
        }

        // Check if teacher is a homeroom teacher for this class
        $is_wali_kelas = false;
        $kelas_list = $guru->getKelasAsWaliKelas($guru_id);

        while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
            if ($row['id'] == $kelas_id) {
                $is_wali_kelas = true;
                break;
            }
        }

        if (!$is_wali_kelas) {
            die("Anda tidak memiliki akses ke kelas ini.");
        }
    }

    // Get class data
    $kelas->id = $kelas_id;
    if (!$kelas->getOne()) {
        die("Kelas tidak ditemukan.");
    }

    // Get wali kelas
    $wali_kelas = null;
    if ($kelas->guru_id) {
        $guru->id = $kelas->guru_id;
        $guru->getOne();
        $wali_kelas = $guru;
    }

    // Get tahun ajaran data
    $ta_stmt = $tahunAjaran->getByTahunAjaran($tahun_ajaran);
    $ta_data = $ta_stmt->fetch(PDO::FETCH_ASSOC);

    if (!$ta_data) {
        die("Tahun ajaran tidak ditemukan.");
    }

    // Get students
    $students = [];
    if ($all_students) {
        $siswa_list = $siswa->getByKelas($kelas_id);
        while ($row = $siswa_list->fetch(PDO::FETCH_ASSOC)) {
            $students[] = $row;
        }
    } else if ($siswa_id) {
        $siswa->id = $siswa_id;
        if ($siswa->getOne()) {
            $students[] = [
                'id' => $siswa->id,
                'nis' => $siswa->nis,
                'nama_siswa' => $siswa->nama_siswa,
                'jenis_kelamin' => $siswa->jenis_kelamin,
                'kelas_id' => $siswa->kelas_id
            ];
        } else {
            die("Siswa tidak ditemukan.");
        }
    } else {
        die("Parameter siswa tidak valid.");
    }

    // Initialize PDF options
    $options = new Options();
    $options->set('isHtml5ParserEnabled', true);
    $options->set('isPhpEnabled', true);
    $options->set('defaultFont', 'DejaVu Sans');

    $dompdf = new Dompdf($options);
    $dompdf->setPaper('A4', 'portrait');

    // Generate PDF for each student
    $html = '';
    foreach ($students as $student) {
        // Get student data
        $siswa_id = $student['id'];

        // Get nilai rapor
        $nilai_list = $rapor->getNilaiRapor($siswa_id, $semester, $tahun_ajaran);

        // Get attendance data
        $kehadiran = $rapor->getKehadiran($siswa_id, $semester, $tahun_ajaran);
        $kehadiran_per_mapel = $rapor->getKehadiranPerMapel($siswa_id, $semester, $tahun_ajaran);

        // Get average grade
        $rata_rata = $rapor->getRataRataNilai($siswa_id, $semester, $tahun_ajaran);

        // Get class rank
        $peringkat = $rapor->getPeringkatKelas($siswa_id, $kelas_id, $semester, $tahun_ajaran);

        // Get behavior assessment
        $sikap = $rapor->getPenilaianSikap($siswa_id, $semester, $tahun_ajaran);

        // Get extracurricular activities
        $ekstrakurikuler = $rapor->getEkstrakurikuler($siswa_id, $semester, $tahun_ajaran);

        // Get achievements
        $prestasi = $rapor->getPrestasi($siswa_id, $semester, $tahun_ajaran);

        // Get teacher's notes
        $catatan = $rapor->getCatatanWaliKelas($siswa_id, $semester, $tahun_ajaran);

        // Get promotion status (only for semester 2)
        $status_kenaikan = '';
        if ($semester == '2') {
            $status_kenaikan = $rapor->getStatusKenaikanKelas($siswa_id, $semester, $tahun_ajaran);
        }

        // Start building HTML
        $html .= '
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Rapor Siswa</title>
            <style>
                body {
                    font-family: "DejaVu Sans", sans-serif;
                    font-size: 12px;
                    line-height: 1.4;
                    color: #333;
                }
                .header {
                    text-align: center;
                    margin-bottom: 20px;
                }
                .header h2, .header h3 {
                    margin: 5px 0;
                }
                h5 {
                    font-size: 14px;
                    margin: 10px 0 5px 0;
                }
                .school-info {
                    text-align: center;
                    margin-bottom: 20px;
                }
                .student-info {
                    width: 100%;
                    margin-bottom: 20px;
                }
                .student-info td {
                    padding: 5px;
                }
                .grades-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                .grades-table th, .grades-table td {
                    border: 1px solid #000;
                    padding: 5px;
                    text-align: center;
                }
                .grades-table th {
                    background-color: #f2f2f2;
                }
                .attendance-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                .attendance-table th, .attendance-table td {
                    border: 1px solid #000;
                    padding: 5px;
                    text-align: center;
                }
                .attendance-table th {
                    background-color: #f2f2f2;
                }
                .extracurricular-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                .extracurricular-table th, .extracurricular-table td {
                    border: 1px solid #000;
                    padding: 5px;
                }
                .extracurricular-table th {
                    background-color: #f2f2f2;
                    text-align: center;
                }
                .achievement-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                .achievement-table th, .achievement-table td {
                    border: 1px solid #000;
                    padding: 5px;
                }
                .achievement-table th {
                    background-color: #f2f2f2;
                    text-align: center;
                }
                .notes {
                    margin-bottom: 20px;
                }
                .notes p {
                    border: 1px solid #000;
                    padding: 10px;
                    min-height: 50px;
                }
                .signature {
                    width: 100%;
                    margin-top: 30px;
                }
                .signature td {
                    width: 33.33%;
                    text-align: center;
                    vertical-align: top;
                    padding: 10px;
                }
                .page-break {
                    page-break-after: always;
                }
                .text-center {
                    text-align: center;
                }
                .text-left {
                    text-align: left;
                }
                .text-right {
                    text-align: right;
                }
                .text-bold {
                    font-weight: bold;
                }
                .behavior-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 20px;
                }
                .behavior-table th, .behavior-table td {
                    border: 1px solid #000;
                    padding: 5px;
                }
                .behavior-table th {
                    background-color: #f2f2f2;
                    text-align: center;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>LAPORAN HASIL BELAJAR SISWA</h2>
                <h3>TAHUN PELAJARAN ' . $tahun_ajaran . '</h3>
            </div>

            <table class="student-info">
                <tr>
                    <td width="20%">Nama Siswa</td>
                    <td width="3%">:</td>
                    <td width="27%">' . $student['nama_siswa'] . '</td>
                    <td width="20%">Kelas</td>
                    <td width="3%">:</td>
                    <td width="27%">' . $kelas->nama_kelas . '</td>
                </tr>
                <tr>
                    <td>NIS</td>
                    <td>:</td>
                    <td>' . $student['nis'] . '</td>
                    <td>Semester</td>
                    <td>:</td>
                    <td>' . $semester . '</td>
                </tr>
            </table>

            <h4>A. NILAI AKADEMIK</h4>
            <table class="grades-table">
                <thead>
                    <tr>
                        <th rowspan="2" width="5%">No</th>
                        <th rowspan="2" width="30%">Mata Pelajaran</th>
                        <th rowspan="2" width="10%">KKM</th>
                        <th colspan="4">Nilai</th>
                        <th rowspan="2" width="15%">Nilai Akhir</th>
                        <th rowspan="2" width="10%">Keterangan</th>
                    </tr>
                    <tr>
                        <th width="10%">Tugas</th>
                        <th width="10%">UTS</th>
                        <th width="10%">UAS</th>
                        <th width="10%">Absensi</th>
                    </tr>
                </thead>
                <tbody>';

        $no = 1;
        $total_mapel = count($nilai_list);
        $total_nilai = 0;
        $total_tuntas = 0;

        foreach ($nilai_list as $nilai) {
            $nilai_akhir = $nilai['nilai_akhir'] ?? 0;
            $status = $nilai_akhir >= $nilai['kkm'] ? 'Tuntas' : 'Belum Tuntas';

            if ($nilai_akhir >= $nilai['kkm']) {
                $total_tuntas++;
            }

            $html .= '
                    <tr>
                        <td class="text-center">' . $no++ . '</td>
                        <td class="text-left">' . $nilai['nama_mapel'] . '</td>
                        <td class="text-center">' . $nilai['kkm'] . '</td>
                        <td class="text-center">' . ($nilai['nilai_tugas'] ?? '-') . '</td>
                        <td class="text-center">' . ($nilai['nilai_uts'] ?? '-') . '</td>
                        <td class="text-center">' . ($nilai['nilai_uas'] ?? '-') . '</td>
                        <td class="text-center">' . ($nilai['nilai_absen'] ?? '-') . '</td>
                        <td class="text-center text-bold">' . $nilai_akhir . '</td>
                        <td class="text-center">' . $status . '</td>
                    </tr>';
        }

        $html .= '
                </tbody>
                <tfoot>
                    <tr>
                        <td colspan="7" class="text-right text-bold">Rata-rata Nilai</td>
                        <td class="text-center text-bold">' . $rata_rata . '</td>
                        <td></td>
                    </tr>
                </tfoot>
            </table>

            <h4>B. PENILAIAN SIKAP</h4>
            <table class="behavior-table">
                <thead>
                    <tr>
                        <th width="50%">Sikap Spiritual</th>
                        <th width="50%">Sikap Sosial</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">' . $sikap['sikap_spiritual'] . '</td>
                        <td class="text-center">' . $sikap['sikap_sosial'] . '</td>
                    </tr>
                </tbody>
            </table>

            <h4>C. KEHADIRAN</h4>
            <h5>Rekapitulasi Kehadiran</h5>
            <p style="font-size: 11px; margin-top: 0; margin-bottom: 5px;"><i>Dihitung berdasarkan total kehadiran di semua mata pelajaran</i></p>
            <table class="attendance-table">
                <thead>
                    <tr>
                        <th width="33%">Sakit</th>
                        <th width="33%">Izin</th>
                        <th width="34%">Tanpa Keterangan</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td class="text-center">' . $kehadiran['sakit'] . ' kali</td>
                        <td class="text-center">' . $kehadiran['izin'] . ' kali</td>
                        <td class="text-center">' . $kehadiran['alpha'] . ' kali</td>
                    </tr>
                </tbody>
            </table>

            <h5 style="margin-top: 15px;">Kehadiran Per Mata Pelajaran</h5>
            <p style="font-size: 11px; margin-top: 0; margin-bottom: 5px;"><i>Rincian kehadiran berdasarkan masing-masing mata pelajaran</i></p>
            <table class="attendance-table">
                <thead>
                    <tr>
                        <th width="5%">No</th>
                        <th width="30%">Mata Pelajaran</th>
                        <th width="13%">Hadir</th>
                        <th width="13%">Sakit</th>
                        <th width="13%">Izin</th>
                        <th width="13%">Alpha</th>
                        <th width="13%">Total</th>
                    </tr>
                </thead>
                <tbody>';

        if (!empty($kehadiran_per_mapel)) {
            $no = 1;
            foreach ($kehadiran_per_mapel as $k) {
                $html .= '
                    <tr>
                        <td class="text-center">' . $no++ . '</td>
                        <td>' . $k['nama_mapel'] . '</td>
                        <td class="text-center">' . $k['hadir'] . '</td>
                        <td class="text-center">' . $k['sakit'] . '</td>
                        <td class="text-center">' . $k['izin'] . '</td>
                        <td class="text-center">' . $k['alpha'] . '</td>
                        <td class="text-center">' . $k['total_pertemuan'] . '</td>
                    </tr>';
            }
        } else {
            $html .= '
                <tr>
                    <td colspan="7" class="text-center">Tidak ada data kehadiran</td>
                </tr>';
        }

        $html .= '
                </tbody>
            </table>';

        // Add extracurricular section if data exists
        if (!empty($ekstrakurikuler)) {
            $html .= '
            <h4>D. EKSTRAKURIKULER</h4>
            <table class="extracurricular-table">
                <thead>
                    <tr>
                        <th width="5%">No</th>
                        <th width="40%">Kegiatan Ekstrakurikuler</th>
                        <th width="15%">Nilai</th>
                        <th width="40%">Keterangan</th>
                    </tr>
                </thead>
                <tbody>';

            $no = 1;
            foreach ($ekstrakurikuler as $ekskul) {
                $html .= '
                    <tr>
                        <td class="text-center">' . $no++ . '</td>
                        <td>' . $ekskul['nama_ekstrakurikuler'] . '</td>
                        <td class="text-center">' . $ekskul['nilai'] . '</td>
                        <td>' . $ekskul['keterangan'] . '</td>
                    </tr>';
            }

            $html .= '
                </tbody>
            </table>';
        }

        // Add achievement section if data exists
        if (!empty($prestasi)) {
            $html .= '
            <h4>' . (empty($ekstrakurikuler) ? 'D' : 'E') . '. PRESTASI</h4>
            <table class="achievement-table">
                <thead>
                    <tr>
                        <th width="5%">No</th>
                        <th width="40%">Jenis Prestasi</th>
                        <th width="55%">Keterangan</th>
                    </tr>
                </thead>
                <tbody>';

            $no = 1;
            foreach ($prestasi as $p) {
                $html .= '
                    <tr>
                        <td class="text-center">' . $no++ . '</td>
                        <td>' . $p['jenis_prestasi'] . '</td>
                        <td>' . $p['keterangan'] . '</td>
                    </tr>';
            }

            $html .= '
                </tbody>
            </table>';
        }

        // Add teacher's notes
        $html .= '
            <h4>' . (empty($ekstrakurikuler) && empty($prestasi) ? 'D' : (empty($ekstrakurikuler) || empty($prestasi) ? 'E' : 'F')) . '. CATATAN WALI KELAS</h4>
            <div class="notes">
                <p>' . ($catatan ?: '') . '</p>
            </div>';

        // Add promotion status for semester 2
        if ($semester == '2') {
            $html .= '
            <h4>' . (empty($ekstrakurikuler) && empty($prestasi) ? 'E' : (empty($ekstrakurikuler) || empty($prestasi) ? 'F' : 'G')) . '. KEPUTUSAN</h4>
            <div class="notes">
                <p class="text-center text-bold">' . $status_kenaikan . '</p>
            </div>';
        }

        // Add signature section
        $html .= '
            <table class="signature">
                <tr>
                    <td>
                        Mengetahui,<br>
                        Orang Tua/Wali
                        <br><br><br><br><br>
                        ______________________
                    </td>
                    <td>

                    </td>
                    <td>
                        ' . date('d F Y') . '<br>
                        Wali Kelas
                        <br><br><br><br><br>
                        ' . ($wali_kelas ? $wali_kelas->nama_lengkap : '______________________') . '
                    </td>
                </tr>
                <tr>
                    <td colspan="3" class="text-center">
                        Mengetahui,<br>
                        Kepala Sekolah
                        <br><br><br><br><br>
                        ______________________
                    </td>
                </tr>
            </table>';

        // Add page break if not the last student
        if ($student !== end($students)) {
            $html .= '<div class="page-break"></div>';
        }

        $html .= '
        </body>
        </html>';
    }

    // Load HTML to DomPDF
    $dompdf->loadHtml($html);

    // Render the PDF
    $dompdf->render();

    // Output the generated PDF
    $filename = 'Rapor_';
    if (count($students) == 1) {
        $filename .= $students[0]['nama_siswa'] . '_';
    } else {
        $filename .= $kelas->nama_kelas . '_';
    }
    $filename .= 'Semester_' . $semester . '_' . $tahun_ajaran . '.pdf';

    $dompdf->stream($filename, [
        'Attachment' => true
    ]);

    exit(0);
} catch (Exception $e) {
    error_log('PDF Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file PDF: " . $e->getMessage());
}