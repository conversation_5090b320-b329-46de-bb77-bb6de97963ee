<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once '../template/header.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/Guru.php';
require_once '../models/User.php';
require_once '../models/Siswa.php';
require_once '../models/TahunAjaran.php';
require_once '../models/KenaikanKelas.php';

// Check if user is logged in and is a guru or admin
if (!isset($_SESSION['user_id']) || ($_SESSION['role'] !== 'guru' && $_SESSION['role'] !== 'admin')) {
    header("Location: /absen/403.php");
    exit();
}

// Initialize objects
$kelas = new Kelas();
$periodeAktif = new PeriodeAktif();
$guru = new Guru();
$user = new User();
$siswa = new Siswa();
$tahunAjaran = new TahunAjaran();

// Initialize KenaikanKelas to ensure table and columns exist
$kenaikanKelas = new KenaikanKelas();

// Get active period
$periodeAktif->getActive();

// Get semester and tahun_ajaran from GET or use active period
$semester = isset($_GET['semester']) ? $_GET['semester'] : $periodeAktif->semester;
$tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $periodeAktif->tahun_ajaran;

// Get all tahun ajaran for dropdown
$tahun_ajaran_list = $tahunAjaran->getAll();

// Get guru_id if user is a teacher
$guru_id = null;
$is_wali_kelas = false;
$kelas_list = null;

if ($_SESSION['role'] === 'guru') {
    $guru_id = $user->getGuruId($_SESSION['user_id']);

    if (!$guru_id) {
        $_SESSION['error'] = "Data guru tidak ditemukan.";
        header("Location: /absen/index.php");
        exit();
    }

    // Check if teacher is a homeroom teacher
    $is_wali_kelas = $guru->isWaliKelas($guru_id);

    if ($is_wali_kelas) {
        // Get classes where the teacher is a homeroom teacher
        $kelas_list = $guru->getKelasAsWaliKelas($guru_id);
    } else {
        $_SESSION['error'] = "Anda tidak terdaftar sebagai wali kelas.";
        header("Location: /absen/index.php");
        exit();
    }
} else {
    // Admin can see all classes
    $kelas_list = $kelas->getAll();
}

// Get selected class
$selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';

// Get students in selected class
$siswa_list = null;
if ($selected_kelas) {
    // Verify access if user is a teacher
    if ($_SESSION['role'] === 'guru') {
        $valid_kelas = false;
        while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
            if ($row['id'] == $selected_kelas) {
                $valid_kelas = true;
                break;
            }
        }

        if (!$valid_kelas) {
            $_SESSION['error'] = "Anda tidak memiliki akses ke kelas ini.";
            header("Location: /absen/index.php");
            exit();
        }

        // Reset pointer
        $kelas_list->execute();
    }

    $siswa_list = $siswa->getByKelas($selected_kelas);
}
?>

<div class="container-fluid">
    <div class="d-sm-flex align-items-center justify-content-between mb-4">
        <h1 class="h3 mb-0 text-gray-800">Cetak Rapor Siswa</h1>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Filter Data</h5>
                </div>
                <div class="card-body">
                    <form method="GET" action="">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label for="tahun_ajaran">Tahun Ajaran</label>
                                <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                                    <?php while ($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?php echo $row['tahun_ajaran']; ?>" <?php echo ($tahun_ajaran == $row['tahun_ajaran']) ? 'selected' : ''; ?>>
                                            <?php echo $row['tahun_ajaran']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-3 mb-3">
                                <label for="semester">Semester</label>
                                <select class="form-select" id="semester" name="semester" required>
                                    <option value="1" <?php echo ($semester == '1') ? 'selected' : ''; ?>>Semester 1</option>
                                    <option value="2" <?php echo ($semester == '2') ? 'selected' : ''; ?>>Semester 2</option>
                                </select>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label for="kelas_id">Kelas</label>
                                <select class="form-select" id="kelas_id" name="kelas_id" required>
                                    <option value="">Pilih Kelas</option>
                                    <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?php echo $row['id']; ?>" <?php echo ($selected_kelas == $row['id']) ? 'selected' : ''; ?>>
                                            <?php echo $row['nama_kelas']; ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3 d-flex align-items-end">
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-filter"></i> Filter
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <?php if ($siswa_list && $siswa_list->rowCount() > 0): ?>
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Daftar Siswa</h5>
                    <div>
                        <a href="export_pdf.php?kelas_id=<?php echo $selected_kelas; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>&all=1" class="btn btn-danger" target="_blank">
                            <i class="fas fa-file-pdf"></i> Cetak Semua Rapor
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped" id="dataTable">
                            <thead class="table-primary">
                                <tr>
                                    <th>No</th>
                                    <th>NIS</th>
                                    <th>Nama Siswa</th>
                                    <th>Jenis Kelamin</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                while ($row = $siswa_list->fetch(PDO::FETCH_ASSOC)):
                                ?>
                                <tr>
                                    <td><?php echo $no++; ?></td>
                                    <td><?php echo $row['nis']; ?></td>
                                    <td><?php echo $row['nama_siswa']; ?></td>
                                    <td><?php echo ($row['jenis_kelamin'] == 'L') ? 'Laki-laki' : 'Perempuan'; ?></td>
                                    <td>
                                        <?php if ($semester == '2'): ?>
                                        <button type="button" class="btn btn-sm btn-primary edit-keputusan" data-siswa="<?php echo $row['id']; ?>" data-kelas="<?php echo $selected_kelas; ?>" data-tahun="<?php echo $tahun_ajaran; ?>" data-semester="<?php echo $semester; ?>">
                                            <i class="fas fa-edit"></i> Edit Keputusan
                                        </button>
                                        <?php endif; ?>
                                        <a href="export_pdf.php?siswa_id=<?php echo $row['id']; ?>&kelas_id=<?php echo $selected_kelas; ?>&semester=<?php echo $semester; ?>&tahun_ajaran=<?php echo $tahun_ajaran; ?>" class="btn btn-sm btn-danger" target="_blank">
                                            <i class="fas fa-file-pdf"></i> Cetak Rapor
                                        </a>
                                    </td>
                                </tr>
                                <?php endwhile; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php elseif ($selected_kelas): ?>
    <div class="alert alert-warning">
        <i class="fas fa-exclamation-triangle"></i> Tidak ada data siswa di kelas yang dipilih.
    </div>
    <?php endif; ?>
</div>

<!-- Modal Keputusan -->
<div class="modal fade" id="modalKeputusan" tabindex="-1" aria-labelledby="modalKeputusanLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <!-- Modal content will be loaded here -->
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "language": {
            "lengthMenu": "Tampilkan _MENU_ data per halaman",
            "zeroRecords": "Data tidak ditemukan",
            "info": "Menampilkan halaman _PAGE_ dari _PAGES_",
            "infoEmpty": "Tidak ada data tersedia",
            "infoFiltered": "(difilter dari _MAX_ total data)",
            "search": "Cari:",
            "paginate": {
                "first": "Pertama",
                "last": "Terakhir",
                "next": "Selanjutnya",
                "previous": "Sebelumnya"
            }
        }
    });

    // Handle modal keputusan
    $(document).on('click', '.edit-keputusan', function() {
        var siswa_id = $(this).data('siswa');
        var kelas_id = $(this).data('kelas');
        var tahun_ajaran = $(this).data('tahun');
        var semester = $(this).data('semester');

        // Load modal content
        $.get('modal_keputusan.php', {
            siswa_id: siswa_id,
            kelas_id: kelas_id,
            tahun_ajaran: tahun_ajaran,
            semester: semester
        }, function(data) {
            $('#modalKeputusan .modal-content').html(data);
            var myModal = new bootstrap.Modal(document.getElementById('modalKeputusan'));
            myModal.show();
        });
    });
});
</script>

<?php require_once '../template/footer.php'; ?>