-- Sample data untuk testing sistem absensi
-- Jalankan query ini jika tidak ada data di database

-- 1. Insert sample kelas
INSERT INTO kelas (nama_kelas, tahun_ajaran, created_at) VALUES 
('X IPA 1', '2024/2025', NOW()),
('X IPA 2', '2024/2025', NOW()),
('XI IPA 1', '2024/2025', NOW()),
('XI IPA 2', '2024/2025', NOW()),
('XII IPA 1', '2024/2025', NOW());

-- 2. Insert sample mata pelajaran
INSERT INTO mata_pelajaran (kode_mapel, nama_mapel, kkm, created_at) VALUES 
('MTK001', 'Matematika', 75.00, NOW()),
('BIN001', 'Bahasa Indonesia', 75.00, NOW()),
('ING001', 'Bahasa Inggris', 75.00, NOW()),
('FIS001', '<PERSON><PERSON><PERSON>', 75.00, NOW()),
('KIM001', '<PERSON><PERSON>', 75.00, NOW()),
('BIO001', 'Biologi', 75.00, NOW());

-- 3. Insert tahun ajaran (jika tabel ada)
INSERT INTO tahun_ajaran (tahun_ajaran, semester_1_mulai, semester_1_selesai, semester_2_mulai, semester_2_selesai, is_active, created_at) VALUES 
('2024/2025', '2024-07-01', '2024-12-31', '2025-01-01', '2025-06-30', 1, NOW());

-- 4. Insert periode aktif (jika tabel ada)
-- Sesuaikan tahun_ajaran_id dengan ID yang baru dibuat
INSERT INTO periode_aktif (tahun_ajaran_id, semester, is_active, created_at) VALUES 
(1, 1, 1, NOW());

-- 5. Verifikasi data
SELECT 'Kelas' as tabel, COUNT(*) as jumlah FROM kelas
UNION ALL
SELECT 'Mata Pelajaran' as tabel, COUNT(*) as jumlah FROM mata_pelajaran
UNION ALL
SELECT 'Tahun Ajaran' as tabel, COUNT(*) as jumlah FROM tahun_ajaran
UNION ALL
SELECT 'Periode Aktif' as tabel, COUNT(*) as jumlah FROM periode_aktif WHERE is_active = 1;
