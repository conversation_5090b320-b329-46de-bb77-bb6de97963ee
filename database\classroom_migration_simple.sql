-- Simple Classroom Module Migration Script
-- This script creates the new classroom-based structure to fix data consistency issues
-- 
-- IMPORTANT: This script should be run through the maintenance module interface
-- Make sure to backup your database before running this migration!

SET FOREIGN_KEY_CHECKS=0;

-- =====================================================
-- 1. CREATE NEW CLASSROOM TABLES
-- =====================================================

-- Main classroom table - each classroom is tied to specific semester and academic year
CREATE TABLE IF NOT EXISTS `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `tingkat_id` int(11) DEFAULT NULL,
  `jurusan_id` int(11) DEFAULT NULL,
  `guru_wali_id` int(11) DEFAULT NULL,
  `kapasitas_maksimal` int(11) DEFAULT 40,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_ruang_kelas_tingkat` (`tingkat_id`),
  KEY `fk_ruang_kelas_jurusan` (`jurusan_id`),
  KEY `fk_ruang_kelas_guru_wali` (`guru_wali_id`),
  KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Student-classroom assignment table - manages which students are in which classrooms
CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `tanggal_keluar` date DEFAULT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_siswa_ruang_aktif` (`siswa_id`, `ruang_kelas_id`),
  KEY `fk_siswa_ruang_siswa` (`siswa_id`),
  KEY `fk_siswa_ruang_kelas` (`ruang_kelas_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 2. CREATE MIGRATION TRACKING TABLE
-- =====================================================

CREATE TABLE IF NOT EXISTS `classroom_migration_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration_step` varchar(100) NOT NULL,
  `status` enum('pending','running','completed','failed') DEFAULT 'pending',
  `records_processed` int(11) DEFAULT 0,
  `total_records` int(11) DEFAULT 0,
  `error_message` text DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT NULL,
  `completed_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- 3. ADD FOREIGN KEY CONSTRAINTS (if tables exist)
-- =====================================================

-- Add foreign key constraints for ruang_kelas table
-- These will be added only if the referenced tables exist

-- Add foreign key constraints for siswa_ruang_kelas table
-- These will be added only if the referenced tables exist

SET FOREIGN_KEY_CHECKS=1;

-- =====================================================
-- 4. INSERT INITIAL MIGRATION LOG ENTRIES
-- =====================================================

INSERT IGNORE INTO `classroom_migration_log` (`migration_step`, `status`) VALUES
('create_tables', 'completed'),
('migrate_classrooms', 'pending'),
('migrate_student_assignments', 'pending'),
('migrate_attendance_data', 'pending'),
('migrate_assignment_data', 'pending'),
('migrate_grade_data', 'pending'),
('cleanup_old_structure', 'pending'),
('verify_data_integrity', 'pending');

-- =====================================================
-- NOTES FOR MIGRATION PROCESS:
-- =====================================================
-- 
-- This script only creates the new structure. The actual data migration
-- will be handled by the PHP migration interface which will:
-- 
-- 1. Create classrooms from existing kelas data for each semester/year combination
-- 2. Assign students to appropriate classrooms based on their current class and active periods
-- 3. Update attendance records to reference the new classroom structure
-- 4. Update assignment records to reference the new classroom structure  
-- 5. Ensure grade data can be accessed through the new classroom relationships
-- 6. Remove old foreign key constraints and unused columns
-- 7. Verify data integrity across all modules
--
-- The migration will preserve all historical data while fixing the consistency issues.
--
-- NEW WORKFLOW AFTER MIGRATION:
-- 1. Admin changes semester/academic year settings
-- 2. Admin creates new classrooms for the new period
-- 3. Admin assigns students to classrooms (no more grade promotion needed)
-- 4. All modules (attendance, assignments, grades) work with classroom assignments
-- 5. Historical data remains accessible by selecting previous periods
