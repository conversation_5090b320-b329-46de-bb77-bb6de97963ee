-- Complete Migration from Old Class System to New Classroom System
-- This script will completely migrate the database structure and data

-- =====================================================
-- STEP 1: CREATE NEW CLASSROOM TABLES
-- =====================================================

-- Create ruang_kelas table
CREATE TABLE IF NOT EXISTS `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `tingkat_id` int(11) DEFAULT NULL,
  `jurusan_id` int(11) DEFAULT NULL,
  `guru_wali_id` int(11) DEFAULT NULL,
  `kapasitas_maksimal` int(11) DEFAULT 40,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`),
  KEY `fk_ruang_kelas_tingkat` (`tingkat_id`),
  KEY `fk_ruang_kelas_jurusan` (`jurusan_id`),
  KEY `fk_ruang_kelas_guru_wali` (`guru_wali_id`),
  CONSTRAINT `fk_ruang_kelas_tingkat` FOREIGN KEY (`tingkat_id`) REFERENCES `tingkat` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ruang_kelas_jurusan` FOREIGN KEY (`jurusan_id`) REFERENCES `jurusan` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ruang_kelas_guru_wali` FOREIGN KEY (`guru_wali_id`) REFERENCES `guru` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Create siswa_ruang_kelas junction table
CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `tanggal_keluar` date DEFAULT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_siswa_ruang_aktif` (`siswa_id`, `ruang_kelas_id`),
  KEY `fk_siswa_ruang_siswa` (`siswa_id`),
  KEY `fk_siswa_ruang_kelas` (`ruang_kelas_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_siswa_ruang_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_siswa_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- STEP 2: ADD RUANG_KELAS_ID TO EXISTING TABLES
-- =====================================================

-- Add ruang_kelas_id to existing tables
ALTER TABLE `absensi` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `tugas` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `nilai` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `nilai_sikap` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `nilai_tugas` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `detail_absensi` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `jadwal_pelajaran` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;
ALTER TABLE `tugas_tambahan` ADD COLUMN IF NOT EXISTS `ruang_kelas_id` int(11) DEFAULT NULL;

-- Add backup column for kelas_id in siswa table
ALTER TABLE `siswa` ADD COLUMN IF NOT EXISTS `kelas_id_backup` int(11) DEFAULT NULL;

-- =====================================================
-- STEP 3: ADD INDEXES FOR NEW COLUMNS
-- =====================================================

-- Add indexes for ruang_kelas_id columns
ALTER TABLE `absensi` ADD KEY IF NOT EXISTS `fk_absensi_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `tugas` ADD KEY IF NOT EXISTS `fk_tugas_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `nilai` ADD KEY IF NOT EXISTS `fk_nilai_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `nilai_sikap` ADD KEY IF NOT EXISTS `fk_nilai_sikap_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `nilai_tugas` ADD KEY IF NOT EXISTS `fk_nilai_tugas_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `detail_absensi` ADD KEY IF NOT EXISTS `fk_detail_absensi_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `jadwal_pelajaran` ADD KEY IF NOT EXISTS `fk_jadwal_ruang_kelas` (`ruang_kelas_id`);
ALTER TABLE `tugas_tambahan` ADD KEY IF NOT EXISTS `fk_tugas_tambahan_ruang_kelas` (`ruang_kelas_id`);

-- =====================================================
-- STEP 4: MIGRATE DATA FROM OLD SYSTEM TO NEW SYSTEM
-- =====================================================

-- Backup existing kelas_id in siswa table
UPDATE `siswa` SET `kelas_id_backup` = `kelas_id` WHERE `kelas_id` IS NOT NULL AND `kelas_id_backup` IS NULL;

-- Migrate kelas data to ruang_kelas
-- This creates classroom entries for current academic year
INSERT INTO `ruang_kelas` (
    `nama_ruang_kelas`, 
    `semester`, 
    `tahun_ajaran`, 
    `tingkat_id`, 
    `jurusan_id`, 
    `guru_wali_id`, 
    `status`, 
    `created_at`
)
SELECT 
    k.nama_kelas as nama_ruang_kelas,
    '1' as semester, -- Default to semester 1
    COALESCE(k.tahun_ajaran, '2024/2025') as tahun_ajaran,
    k.tingkat_id,
    k.jurusan_id,
    k.guru_id as guru_wali_id,
    'aktif' as status,
    NOW() as created_at
FROM kelas k
WHERE NOT EXISTS (
    SELECT 1 FROM ruang_kelas rk 
    WHERE rk.nama_ruang_kelas = k.nama_kelas 
    AND rk.tahun_ajaran = COALESCE(k.tahun_ajaran, '2024/2025')
    AND rk.semester = '1'
);

-- Create semester 2 classrooms as well
INSERT INTO `ruang_kelas` (
    `nama_ruang_kelas`, 
    `semester`, 
    `tahun_ajaran`, 
    `tingkat_id`, 
    `jurusan_id`, 
    `guru_wali_id`, 
    `status`, 
    `created_at`
)
SELECT 
    k.nama_kelas as nama_ruang_kelas,
    '2' as semester,
    COALESCE(k.tahun_ajaran, '2024/2025') as tahun_ajaran,
    k.tingkat_id,
    k.jurusan_id,
    k.guru_id as guru_wali_id,
    'aktif' as status,
    NOW() as created_at
FROM kelas k
WHERE NOT EXISTS (
    SELECT 1 FROM ruang_kelas rk 
    WHERE rk.nama_ruang_kelas = k.nama_kelas 
    AND rk.tahun_ajaran = COALESCE(k.tahun_ajaran, '2024/2025')
    AND rk.semester = '2'
);

-- =====================================================
-- STEP 5: ASSIGN STUDENTS TO CLASSROOMS
-- =====================================================

-- Assign students to semester 1 classrooms
INSERT INTO `siswa_ruang_kelas` (
    `siswa_id`, 
    `ruang_kelas_id`, 
    `tanggal_masuk`, 
    `status`, 
    `created_at`
)
SELECT 
    s.id as siswa_id,
    rk.id as ruang_kelas_id,
    COALESCE(DATE(s.created_at), CURDATE()) as tanggal_masuk,
    'aktif' as status,
    NOW() as created_at
FROM siswa s
INNER JOIN kelas k ON s.kelas_id = k.id
INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
    AND rk.tahun_ajaran = COALESCE(k.tahun_ajaran, '2024/2025')
    AND rk.semester = '1'
WHERE NOT EXISTS (
    SELECT 1 FROM siswa_ruang_kelas srk 
    WHERE srk.siswa_id = s.id AND srk.ruang_kelas_id = rk.id
);

-- Assign students to semester 2 classrooms
INSERT INTO `siswa_ruang_kelas` (
    `siswa_id`, 
    `ruang_kelas_id`, 
    `tanggal_masuk`, 
    `status`, 
    `created_at`
)
SELECT 
    s.id as siswa_id,
    rk.id as ruang_kelas_id,
    COALESCE(DATE(s.created_at), CURDATE()) as tanggal_masuk,
    'aktif' as status,
    NOW() as created_at
FROM siswa s
INNER JOIN kelas k ON s.kelas_id = k.id
INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
    AND rk.tahun_ajaran = COALESCE(k.tahun_ajaran, '2024/2025')
    AND rk.semester = '2'
WHERE NOT EXISTS (
    SELECT 1 FROM siswa_ruang_kelas srk 
    WHERE srk.siswa_id = s.id AND srk.ruang_kelas_id = rk.id
);

-- =====================================================
-- STEP 6: UPDATE EXISTING RECORDS TO USE RUANG_KELAS_ID
-- =====================================================

-- Update tugas table
UPDATE tugas t
INNER JOIN kelas k ON t.kelas_id = k.id
INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
    AND rk.tahun_ajaran = t.tahun_ajaran 
    AND rk.semester = t.semester
SET t.ruang_kelas_id = rk.id
WHERE t.ruang_kelas_id IS NULL;

-- Update absensi table
UPDATE absensi a
INNER JOIN kelas k ON a.kelas_id = k.id
INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
    AND rk.tahun_ajaran = a.tahun_ajaran 
    AND rk.semester = a.semester
SET a.ruang_kelas_id = rk.id
WHERE a.ruang_kelas_id IS NULL;

-- Update nilai table
UPDATE nilai n
INNER JOIN siswa s ON n.siswa_id = s.id
INNER JOIN kelas k ON s.kelas_id = k.id
INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
    AND rk.tahun_ajaran = n.tahun_ajaran 
    AND rk.semester = n.semester
SET n.ruang_kelas_id = rk.id
WHERE n.ruang_kelas_id IS NULL;

-- =====================================================
-- STEP 7: CREATE MIGRATION LOG
-- =====================================================

CREATE TABLE IF NOT EXISTS `classroom_migration_log` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `migration_step` varchar(100) NOT NULL,
  `status` enum('pending','running','completed','failed') DEFAULT 'completed',
  `records_processed` int(11) DEFAULT 0,
  `total_records` int(11) DEFAULT 0,
  `error_message` text DEFAULT NULL,
  `started_at` timestamp NULL DEFAULT current_timestamp(),
  `completed_at` timestamp NULL DEFAULT current_timestamp(),
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Log migration completion
INSERT INTO `classroom_migration_log` (`migration_step`, `status`, `completed_at`) VALUES
('create_tables', 'completed', NOW()),
('migrate_classrooms', 'completed', NOW()),
('migrate_student_assignments', 'completed', NOW()),
('migrate_existing_records', 'completed', NOW());

-- =====================================================
-- STEP 8: VERIFICATION QUERIES
-- =====================================================

-- Verify migration results
SELECT 'ruang_kelas' as table_name, COUNT(*) as record_count FROM ruang_kelas
UNION ALL
SELECT 'siswa_ruang_kelas' as table_name, COUNT(*) as record_count FROM siswa_ruang_kelas
UNION ALL
SELECT 'tugas_with_ruang_kelas' as table_name, COUNT(*) as record_count FROM tugas WHERE ruang_kelas_id IS NOT NULL
UNION ALL
SELECT 'absensi_with_ruang_kelas' as table_name, COUNT(*) as record_count FROM absensi WHERE ruang_kelas_id IS NOT NULL;
