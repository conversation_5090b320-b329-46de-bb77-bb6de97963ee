-- Fresh Database Setup for Modern Classroom System
-- Run this SQL in phpMyAdmin for a clean start

-- =====================================================
-- CORE TABLES
-- =====================================================

-- Tingkat table
CREATE TABLE `tingkat` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_tingkat` varchar(50) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Jurusan table
CREATE TABLE `jurusan` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_jurusan` varchar(100) NOT NULL,
  `kode_jurusan` varchar(10) NOT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_jurusan` (`kode_jurusan`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Guru table
CREATE TABLE `guru` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nip` varchar(20) DEFAULT NULL,
  `nama_guru` varchar(100) NOT NULL,
  `email` varchar(100) DEFAULT NULL,
  `no_hp` varchar(15) DEFAULT NULL,
  `alamat` text DEFAULT NULL,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nip` (`nip`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Mata pelajaran table
CREATE TABLE `mata_pelajaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_mapel` varchar(10) NOT NULL,
  `nama_mapel` varchar(100) NOT NULL,
  `kkm` decimal(5,2) DEFAULT 75.00,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_mapel` (`kode_mapel`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- ACADEMIC SYSTEM TABLES
-- =====================================================

-- Tahun ajaran table
CREATE TABLE `tahun_ajaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tahun_ajaran` varchar(9) NOT NULL,
  `semester_1_mulai` date NOT NULL,
  `semester_1_selesai` date NOT NULL,
  `semester_2_mulai` date NOT NULL,
  `semester_2_selesai` date NOT NULL,
  `is_active` tinyint(1) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `tahun_ajaran` (`tahun_ajaran`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Periode aktif table
CREATE TABLE `periode_aktif` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tahun_ajaran_id` int(11) NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `tahun_ajaran_id` (`tahun_ajaran_id`),
  CONSTRAINT `periode_aktif_ibfk_1` FOREIGN KEY (`tahun_ajaran_id`) REFERENCES `tahun_ajaran` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- MODERN CLASSROOM SYSTEM TABLES
-- =====================================================

-- Ruang kelas table (NEW CLASSROOM SYSTEM)
CREATE TABLE `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `tingkat_id` int(11) DEFAULT NULL,
  `jurusan_id` int(11) DEFAULT NULL,
  `guru_wali_id` int(11) DEFAULT NULL,
  `kapasitas_maksimal` int(11) DEFAULT 40,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`),
  KEY `fk_ruang_kelas_tingkat` (`tingkat_id`),
  KEY `fk_ruang_kelas_jurusan` (`jurusan_id`),
  KEY `fk_ruang_kelas_guru_wali` (`guru_wali_id`),
  CONSTRAINT `fk_ruang_kelas_tingkat` FOREIGN KEY (`tingkat_id`) REFERENCES `tingkat` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ruang_kelas_jurusan` FOREIGN KEY (`jurusan_id`) REFERENCES `jurusan` (`id`) ON DELETE SET NULL,
  CONSTRAINT `fk_ruang_kelas_guru_wali` FOREIGN KEY (`guru_wali_id`) REFERENCES `guru` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Siswa table
CREATE TABLE `siswa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nis` varchar(20) NOT NULL,
  `nama_siswa` varchar(100) NOT NULL,
  `jenis_kelamin` enum('L','P') NOT NULL,
  `tempat_lahir` varchar(50) DEFAULT NULL,
  `tanggal_lahir` date DEFAULT NULL,
  `alamat` text DEFAULT NULL,
  `no_hp` varchar(15) DEFAULT NULL,
  `email` varchar(100) DEFAULT NULL,
  `nama_ortu` varchar(100) DEFAULT NULL,
  `no_hp_ortu` varchar(15) DEFAULT NULL,
  `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nis` (`nis`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Siswa ruang kelas junction table (NEW CLASSROOM SYSTEM)
CREATE TABLE `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `tanggal_keluar` date DEFAULT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_siswa_ruang_aktif` (`siswa_id`, `ruang_kelas_id`),
  KEY `fk_siswa_ruang_siswa` (`siswa_id`),
  KEY `fk_siswa_ruang_kelas` (`ruang_kelas_id`),
  KEY `idx_status` (`status`),
  CONSTRAINT `fk_siswa_ruang_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_siswa_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- LEARNING SYSTEM TABLES (CLASSROOM-BASED)
-- =====================================================

-- Tugas table (MODERN CLASSROOM SYSTEM)
CREATE TABLE `tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mapel_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `judul` varchar(255) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `tanggal` date NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_tugas_mapel` (`mapel_id`),
  KEY `fk_tugas_ruang_kelas` (`ruang_kelas_id`),
  KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`),
  CONSTRAINT `fk_tugas_mapel` FOREIGN KEY (`mapel_id`) REFERENCES `mata_pelajaran` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_tugas_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Absensi table (MODERN CLASSROOM SYSTEM)
CREATE TABLE `absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tanggal` date NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `mapel_id` int(11) NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `fk_absensi_ruang_kelas` (`ruang_kelas_id`),
  KEY `fk_absensi_mapel` (`mapel_id`),
  KEY `idx_tanggal` (`tanggal`),
  CONSTRAINT `fk_absensi_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_absensi_mapel` FOREIGN KEY (`mapel_id`) REFERENCES `mata_pelajaran` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Detail absensi table
CREATE TABLE `detail_absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `absensi_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `status` enum('hadir','sakit','izin','alpha') NOT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_absensi_siswa` (`absensi_id`, `siswa_id`),
  KEY `fk_detail_absensi_siswa` (`siswa_id`),
  CONSTRAINT `fk_detail_absensi_absensi` FOREIGN KEY (`absensi_id`) REFERENCES `absensi` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_detail_absensi_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- Nilai tugas table
CREATE TABLE `nilai_tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tugas_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `nilai` decimal(5,2) DEFAULT NULL,
  `keterangan` text DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_tugas_siswa` (`tugas_id`, `siswa_id`),
  KEY `fk_nilai_tugas_siswa` (`siswa_id`),
  CONSTRAINT `fk_nilai_tugas_tugas` FOREIGN KEY (`tugas_id`) REFERENCES `tugas` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_nilai_tugas_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Insert tingkat
INSERT INTO `tingkat` (`nama_tingkat`, `keterangan`) VALUES 
('X', 'Kelas 10'),
('XI', 'Kelas 11'), 
('XII', 'Kelas 12');

-- Insert jurusan
INSERT INTO `jurusan` (`nama_jurusan`, `kode_jurusan`, `keterangan`) VALUES 
('Ilmu Pengetahuan Alam', 'IPA', 'Jurusan IPA'),
('Ilmu Pengetahuan Sosial', 'IPS', 'Jurusan IPS');

-- Insert guru
INSERT INTO `guru` (`nip`, `nama_guru`, `email`, `status`) VALUES 
('196801011990031001', 'Drs. Ahmad Wijaya', '<EMAIL>', 'aktif'),
('197205151995122002', 'Siti Nurhaliza, S.Pd', '<EMAIL>', 'aktif'),
('198003201999031003', 'Budi Santoso, M.Pd', '<EMAIL>', 'aktif');

-- Insert mata pelajaran
INSERT INTO `mata_pelajaran` (`kode_mapel`, `nama_mapel`, `kkm`) VALUES 
('MTK001', 'Matematika', 75.00),
('BIN001', 'Bahasa Indonesia', 75.00),
('ING001', 'Bahasa Inggris', 75.00),
('FIS001', 'Fisika', 75.00),
('KIM001', 'Kimia', 75.00),
('BIO001', 'Biologi', 75.00),
('SEJ001', 'Sejarah', 75.00),
('GEO001', 'Geografi', 75.00),
('EKO001', 'Ekonomi', 75.00),
('SOC001', 'Sosiologi', 75.00);

-- Insert tahun ajaran
INSERT INTO `tahun_ajaran` (`tahun_ajaran`, `semester_1_mulai`, `semester_1_selesai`, `semester_2_mulai`, `semester_2_selesai`, `is_active`) VALUES 
('2024/2025', '2024-07-01', '2024-12-31', '2025-01-01', '2025-06-30', 1);

-- Insert periode aktif
INSERT INTO `periode_aktif` (`tahun_ajaran_id`, `semester`, `is_active`) VALUES (1, '1', 1);

-- Insert ruang kelas (MODERN CLASSROOM SYSTEM)
INSERT INTO `ruang_kelas` (`nama_ruang_kelas`, `semester`, `tahun_ajaran`, `tingkat_id`, `jurusan_id`, `guru_wali_id`, `status`) VALUES 
('X IPA 1', '1', '2024/2025', 1, 1, 1, 'aktif'),
('X IPA 2', '1', '2024/2025', 1, 1, 2, 'aktif'),
('X IPS 1', '1', '2024/2025', 1, 2, 3, 'aktif'),
('XI IPA 1', '1', '2024/2025', 2, 1, 1, 'aktif'),
('XI IPA 2', '1', '2024/2025', 2, 1, 2, 'aktif'),
('XI IPS 1', '1', '2024/2025', 2, 2, 3, 'aktif'),
('XII IPA 1', '1', '2024/2025', 3, 1, 1, 'aktif'),
('XII IPA 2', '1', '2024/2025', 3, 1, 2, 'aktif'),
('XII IPS 1', '1', '2024/2025', 3, 2, 3, 'aktif');

-- Insert sample siswa
INSERT INTO `siswa` (`nis`, `nama_siswa`, `jenis_kelamin`, `status`) VALUES 
('2024001', 'Andi Pratama', 'L', 'aktif'),
('2024002', 'Sari Dewi', 'P', 'aktif'),
('2024003', 'Budi Setiawan', 'L', 'aktif'),
('2024004', 'Rina Sari', 'P', 'aktif'),
('2024005', 'Doni Kurniawan', 'L', 'aktif'),
('2024006', 'Maya Putri', 'P', 'aktif'),
('2024007', 'Rudi Hartono', 'L', 'aktif'),
('2024008', 'Lina Marlina', 'P', 'aktif'),
('2024009', 'Agus Salim', 'L', 'aktif'),
('2024010', 'Fitri Handayani', 'P', 'aktif');

-- Assign students to classrooms (MODERN CLASSROOM SYSTEM)
INSERT INTO `siswa_ruang_kelas` (`siswa_id`, `ruang_kelas_id`, `tanggal_masuk`, `status`) VALUES 
(1, 1, '2024-07-01', 'aktif'),
(2, 1, '2024-07-01', 'aktif'),
(3, 1, '2024-07-01', 'aktif'),
(4, 2, '2024-07-01', 'aktif'),
(5, 2, '2024-07-01', 'aktif'),
(6, 2, '2024-07-01', 'aktif'),
(7, 3, '2024-07-01', 'aktif'),
(8, 3, '2024-07-01', 'aktif'),
(9, 4, '2024-07-01', 'aktif'),
(10, 4, '2024-07-01', 'aktif');

-- =====================================================
-- VERIFICATION QUERY
-- =====================================================

-- Check the setup
SELECT 'Setup Complete!' as status,
       (SELECT COUNT(*) FROM ruang_kelas) as ruang_kelas,
       (SELECT COUNT(*) FROM siswa) as siswa,
       (SELECT COUNT(*) FROM siswa_ruang_kelas) as assignments,
       (SELECT COUNT(*) FROM mata_pelajaran) as subjects;
