<?php
require_once '../config/database.php';
require_once '../models/User.php';

// Create initial admin user
$user = new User();
$user->username = 'admin';
$user->password = 'admin123'; // This will be hashed in the create method
$user->nama_lengkap = 'Administrator';
$user->role = 'admin';

if($user->create()) {
    echo "Admin user created successfully!\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    echo "\nPlease change the password after first login.";
} else {
    echo "Failed to create admin user. User might already exist.";
}
