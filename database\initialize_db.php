<?php
require_once '../config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // Create users table
    $sql = file_get_contents('users.sql');
    $conn->exec($sql);
    echo "Users table created successfully!\n\n";

    // Create admin user
    require_once '../models/User.php';
    $user = new User();
    $user->username = 'admin';
    $user->password = 'admin123';
    $user->nama_lengkap = 'Administrator';
    $user->role = 'admin';

    if($user->create()) {
        echo "Admin user created successfully!\n";
        echo "----------------------------------------\n";
        echo "Login credentials:\n";
        echo "Username: admin\n";
        echo "Password: admin123\n";
        echo "----------------------------------------\n";
        echo "IMPORTANT: Please change the password after first login.\n";
    } else {
        echo "Note: Admin user already exists.\n";
    }
} catch(PDOException $e) {
    echo "Error: " . $e->getMessage();
}
