-- Create table for tracking grade replacements
CREATE TABLE IF NOT EXISTS `nilai_pengganti` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nilai_id` int(11) NOT NULL,
  `tugas_tambahan_siswa_id` int(11) NOT NULL,
  `jenis_nilai` enum('nilai_tugas','nilai_uts','nilai_uas','nilai_absen') NOT NULL,
  `is_average` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_nilai_pengganti` (`nilai_id`,`jenis_nilai`),
  KEY `tugas_tambahan_siswa_id` (`tugas_tambahan_siswa_id`),
  CONSTRAINT `nilai_pengganti_ibfk_1` FOREIGN KEY (`nilai_id`) REFERENCES `nilai` (`id`) ON DELETE CASCADE,
  CONSTRAINT `nilai_pengganti_ibfk_2` FOREIGN KEY (`tugas_tambahan_siswa_id`) REFERENCES `tugas_tambahan_siswa` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
