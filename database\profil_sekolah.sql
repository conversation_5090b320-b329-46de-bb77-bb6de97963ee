CREATE TABLE IF NOT EXISTS `profil_sekolah` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_sekolah` varchar(100) NOT NULL,
  `npsn` varchar(20) NOT NULL,
  `status_sekolah` enum('<PERSON><PERSON><PERSON>','Swasta') NOT NULL,
  `jenjang_pendidikan` varchar(50) NOT NULL,
  `alamat_jalan` text NOT NULL,
  `desa_kel<PERSON>han` varchar(100) NOT NULL,
  `kecamatan` varchar(100) NOT NULL,
  `kabupaten_kota` varchar(100) NOT NULL,
  `provinsi` varchar(100) NOT NULL,
  `kode_pos` varchar(10) NOT NULL,
  `no_telepon` varchar(20) NOT NULL,
  `email` varchar(100) NOT NULL,
  `website` varchar(100) NOT NULL,
  `nama_kepala_sekolah` varchar(100) NOT NULL,
  `nip_kepala_sekolah` varchar(20) NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `visi` text DEFAULT NULL,
  `misi` text DEFAULT NULL,
  `kode_provinsi` varchar(10) DEFAULT NULL,
  `kode_kabupaten` varchar(10) DEFAULT NULL,
  `kode_kecamatan` varchar(10) DEFAULT NULL,
  `kode_desa` varchar(10) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
