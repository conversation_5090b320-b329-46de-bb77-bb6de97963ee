-- Safe Database Setup - Handles Existing Tables
-- Run this in phpMyAdmin - safe for existing data

-- =====================================================
-- CREATE TABLES (IF NOT EXISTS)
-- =====================================================

-- Mata pelajaran table
CREATE TABLE IF NOT EXISTS `mata_pelajaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_mapel` varchar(10) NOT NULL,
  `nama_mapel` varchar(100) NOT NULL,
  `kkm` decimal(5,2) DEFAULT 75.00,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Ruang kelas table
CREATE TABLE IF NOT EXISTS `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Siswa table
CREATE TABLE IF NOT EXISTS `siswa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nis` varchar(20) NOT NULL,
  `nama_siswa` varchar(100) NOT NULL,
  `jenis_kelamin` enum('L','P') NOT NULL,
  `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Siswa ruang kelas junction table
CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tugas table
CREATE TABLE IF NOT EXISTS `tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mapel_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `judul` varchar(255) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `tanggal` date NOT NULL,
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Absensi table
CREATE TABLE IF NOT EXISTS `absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tanggal` date NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `mapel_id` int(11) NOT NULL,
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Detail absensi table
CREATE TABLE IF NOT EXISTS `detail_absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `absensi_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `status` enum('hadir','sakit','izin','alpha') NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Nilai tugas table
CREATE TABLE IF NOT EXISTS `nilai_tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tugas_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `nilai` decimal(5,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- ADD MISSING COLUMNS (IF NOT EXISTS)
-- =====================================================

-- Add ruang_kelas_id to existing tables if missing
-- Note: MySQL doesn't support IF NOT EXISTS for columns, so we use a different approach

-- For tugas table
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'tugas' 
   AND column_name = 'ruang_kelas_id' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE tugas ADD COLUMN ruang_kelas_id int(11) DEFAULT NULL',
  'SELECT "ruang_kelas_id already exists in tugas" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- For absensi table
SET @sql = (SELECT IF(
  (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
   WHERE table_name = 'absensi' 
   AND column_name = 'ruang_kelas_id' 
   AND table_schema = DATABASE()) = 0,
  'ALTER TABLE absensi ADD COLUMN ruang_kelas_id int(11) DEFAULT NULL',
  'SELECT "ruang_kelas_id already exists in absensi" as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =====================================================
-- INSERT SAMPLE DATA (IGNORE DUPLICATES)
-- =====================================================

-- Insert mata pelajaran
INSERT IGNORE INTO `mata_pelajaran` (`kode_mapel`, `nama_mapel`, `kkm`) VALUES 
('MTK001', 'Matematika', 75.00),
('BIN001', 'Bahasa Indonesia', 75.00),
('ING001', 'Bahasa Inggris', 75.00),
('FIS001', 'Fisika', 75.00),
('KIM001', 'Kimia', 75.00),
('BIO001', 'Biologi', 75.00),
('SEJ001', 'Sejarah', 75.00),
('GEO001', 'Geografi', 75.00);

-- Insert ruang kelas
INSERT IGNORE INTO `ruang_kelas` (`nama_ruang_kelas`, `semester`, `tahun_ajaran`, `status`) VALUES 
('X IPA 1', '1', '2024/2025', 'aktif'),
('X IPA 2', '1', '2024/2025', 'aktif'),
('X IPS 1', '1', '2024/2025', 'aktif'),
('XI IPA 1', '1', '2024/2025', 'aktif'),
('XI IPA 2', '1', '2024/2025', 'aktif'),
('XI IPS 1', '1', '2024/2025', 'aktif'),
('XII IPA 1', '1', '2024/2025', 'aktif'),
('XII IPA 2', '1', '2024/2025', 'aktif'),
('XII IPS 1', '1', '2024/2025', 'aktif');

-- Insert siswa
INSERT IGNORE INTO `siswa` (`nis`, `nama_siswa`, `jenis_kelamin`, `status`) VALUES 
('2024001', 'Andi Pratama', 'L', 'aktif'),
('2024002', 'Sari Dewi', 'P', 'aktif'),
('2024003', 'Budi Setiawan', 'L', 'aktif'),
('2024004', 'Rina Sari', 'P', 'aktif'),
('2024005', 'Doni Kurniawan', 'L', 'aktif'),
('2024006', 'Maya Putri', 'P', 'aktif'),
('2024007', 'Rudi Hartono', 'L', 'aktif'),
('2024008', 'Lina Marlina', 'P', 'aktif'),
('2024009', 'Agus Salim', 'L', 'aktif'),
('2024010', 'Fitri Handayani', 'P', 'aktif');

-- Assign students to classrooms (only if both exist)
INSERT IGNORE INTO `siswa_ruang_kelas` (`siswa_id`, `ruang_kelas_id`, `tanggal_masuk`, `status`) VALUES 
(1, 1, '2024-07-01', 'aktif'),
(2, 1, '2024-07-01', 'aktif'),
(3, 2, '2024-07-01', 'aktif'),
(4, 2, '2024-07-01', 'aktif'),
(5, 3, '2024-07-01', 'aktif'),
(6, 3, '2024-07-01', 'aktif'),
(7, 4, '2024-07-01', 'aktif'),
(8, 4, '2024-07-01', 'aktif'),
(9, 5, '2024-07-01', 'aktif'),
(10, 5, '2024-07-01', 'aktif');

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Show setup status
SELECT 'Database Setup Complete!' as message;

-- Count records in each table
SELECT 'mata_pelajaran' as tabel, COUNT(*) as jumlah FROM mata_pelajaran
UNION ALL
SELECT 'ruang_kelas' as tabel, COUNT(*) as jumlah FROM ruang_kelas WHERE status = 'aktif'
UNION ALL
SELECT 'siswa' as tabel, COUNT(*) as jumlah FROM siswa WHERE status = 'aktif'
UNION ALL
SELECT 'siswa_ruang_kelas' as tabel, COUNT(*) as jumlah FROM siswa_ruang_kelas WHERE status = 'aktif'
UNION ALL
SELECT 'tugas' as tabel, COUNT(*) as jumlah FROM tugas
UNION ALL
SELECT 'absensi' as tabel, COUNT(*) as jumlah FROM absensi;

-- Show sample student assignments
SELECT 
    s.nama_siswa as 'Nama Siswa',
    rk.nama_ruang_kelas as 'Ruang Kelas',
    srk.tanggal_masuk as 'Tanggal Masuk'
FROM siswa s
JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id
JOIN ruang_kelas rk ON srk.ruang_kelas_id = rk.id
WHERE srk.status = 'aktif'
ORDER BY rk.nama_ruang_kelas, s.nama_siswa;

-- Show available subjects
SELECT 
    kode_mapel as 'Kode',
    nama_mapel as 'Mata Pelajaran',
    kkm as 'KKM'
FROM mata_pelajaran 
ORDER BY nama_mapel;

-- Show active classrooms
SELECT 
    id as 'ID',
    nama_ruang_kelas as 'Nama Ruang Kelas',
    semester as 'Semester',
    tahun_ajaran as 'Tahun Ajaran',
    status as 'Status'
FROM ruang_kelas 
WHERE status = 'aktif' 
ORDER BY nama_ruang_kelas;
