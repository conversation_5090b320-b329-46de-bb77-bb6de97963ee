-- Simple Classroom Database Setup
-- Safe for empty database - no complex constraints

-- =====================================================
-- ESSENTIAL TABLES ONLY
-- =====================================================

-- Mata pela<PERSON>an table
CREATE TABLE IF NOT EXISTS `mata_pelajaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_mapel` varchar(10) NOT NULL,
  `nama_mapel` varchar(100) NOT NULL,
  `kkm` decimal(5,2) DEFAULT 75.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `kode_mapel` (`kode_mapel`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Ruang kelas table (<PERSON><PERSON><PERSON>N SYSTEM)
CREATE TABLE IF NOT EXISTS `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Siswa table
CREATE TABLE IF NOT EXISTS `siswa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nis` varchar(20) NOT NULL,
  `nama_siswa` varchar(100) NOT NULL,
  `jenis_kelamin` enum('L','P') NOT NULL,
  `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `nis` (`nis`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Siswa ruang kelas junction table (SIMPLE VERSION)
CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `siswa_id` (`siswa_id`),
  KEY `ruang_kelas_id` (`ruang_kelas_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tugas table (CLASSROOM SYSTEM)
CREATE TABLE IF NOT EXISTS `tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mapel_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `judul` varchar(255) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `tanggal` date NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `mapel_id` (`mapel_id`),
  KEY `ruang_kelas_id` (`ruang_kelas_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Absensi table (CLASSROOM SYSTEM)
CREATE TABLE IF NOT EXISTS `absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tanggal` date NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `mapel_id` int(11) NOT NULL,
  `semester` enum('1','2') NOT NULL,
  `tahun_ajaran` varchar(9) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `ruang_kelas_id` (`ruang_kelas_id`),
  KEY `mapel_id` (`mapel_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Detail absensi table
CREATE TABLE IF NOT EXISTS `detail_absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `absensi_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `status` enum('hadir','sakit','izin','alpha') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `absensi_id` (`absensi_id`),
  KEY `siswa_id` (`siswa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Nilai tugas table
CREATE TABLE IF NOT EXISTS `nilai_tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tugas_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `nilai` decimal(5,2) DEFAULT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `tugas_id` (`tugas_id`),
  KEY `siswa_id` (`siswa_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- SAMPLE DATA
-- =====================================================

-- Insert mata pelajaran
INSERT IGNORE INTO `mata_pelajaran` (`kode_mapel`, `nama_mapel`, `kkm`) VALUES 
('MTK001', 'Matematika', 75.00),
('BIN001', 'Bahasa Indonesia', 75.00),
('ING001', 'Bahasa Inggris', 75.00),
('FIS001', 'Fisika', 75.00),
('KIM001', 'Kimia', 75.00),
('BIO001', 'Biologi', 75.00),
('SEJ001', 'Sejarah', 75.00),
('GEO001', 'Geografi', 75.00),
('EKO001', 'Ekonomi', 75.00),
('SOC001', 'Sosiologi', 75.00);

-- Insert ruang kelas (SEMESTER 1, 2024/2025)
INSERT IGNORE INTO `ruang_kelas` (`nama_ruang_kelas`, `semester`, `tahun_ajaran`, `status`) VALUES 
('X IPA 1', '1', '2024/2025', 'aktif'),
('X IPA 2', '1', '2024/2025', 'aktif'),
('X IPS 1', '1', '2024/2025', 'aktif'),
('XI IPA 1', '1', '2024/2025', 'aktif'),
('XI IPA 2', '1', '2024/2025', 'aktif'),
('XI IPS 1', '1', '2024/2025', 'aktif'),
('XII IPA 1', '1', '2024/2025', 'aktif'),
('XII IPA 2', '1', '2024/2025', 'aktif'),
('XII IPS 1', '1', '2024/2025', 'aktif');

-- Insert sample siswa
INSERT IGNORE INTO `siswa` (`nis`, `nama_siswa`, `jenis_kelamin`, `status`) VALUES 
('2024001', 'Andi Pratama', 'L', 'aktif'),
('2024002', 'Sari Dewi', 'P', 'aktif'),
('2024003', 'Budi Setiawan', 'L', 'aktif'),
('2024004', 'Rina Sari', 'P', 'aktif'),
('2024005', 'Doni Kurniawan', 'L', 'aktif'),
('2024006', 'Maya Putri', 'P', 'aktif'),
('2024007', 'Rudi Hartono', 'L', 'aktif'),
('2024008', 'Lina Marlina', 'P', 'aktif'),
('2024009', 'Agus Salim', 'L', 'aktif'),
('2024010', 'Fitri Handayani', 'P', 'aktif');

-- Assign students to classrooms (SIMPLE VERSION)
INSERT IGNORE INTO `siswa_ruang_kelas` (`siswa_id`, `ruang_kelas_id`, `tanggal_masuk`, `status`) VALUES 
(1, 1, '2024-07-01', 'aktif'),
(2, 1, '2024-07-01', 'aktif'),
(3, 1, '2024-07-01', 'aktif'),
(4, 2, '2024-07-01', 'aktif'),
(5, 2, '2024-07-01', 'aktif'),
(6, 2, '2024-07-01', 'aktif'),
(7, 3, '2024-07-01', 'aktif'),
(8, 3, '2024-07-01', 'aktif'),
(9, 4, '2024-07-01', 'aktif'),
(10, 4, '2024-07-01', 'aktif');

-- =====================================================
-- VERIFICATION
-- =====================================================

-- Check the setup
SELECT 'Database Setup Complete!' as status;

SELECT 'mata_pelajaran' as tabel, COUNT(*) as jumlah FROM mata_pelajaran
UNION ALL
SELECT 'ruang_kelas' as tabel, COUNT(*) as jumlah FROM ruang_kelas WHERE status = 'aktif'
UNION ALL
SELECT 'siswa' as tabel, COUNT(*) as jumlah FROM siswa WHERE status = 'aktif'
UNION ALL
SELECT 'siswa_ruang_kelas' as tabel, COUNT(*) as jumlah FROM siswa_ruang_kelas WHERE status = 'aktif';

-- Show sample classroom assignments
SELECT 
    s.nama_siswa,
    rk.nama_ruang_kelas,
    srk.tanggal_masuk
FROM siswa s
JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id
JOIN ruang_kelas rk ON srk.ruang_kelas_id = rk.id
WHERE srk.status = 'aktif'
ORDER BY rk.nama_ruang_kelas, s.nama_siswa
LIMIT 10;
