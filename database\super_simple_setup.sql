-- Super Simple Database Setup
-- Exact structure match - no errors!

-- Clean start
DROP TABLE IF EXISTS `nilai_tugas`;
DROP TABLE IF EXISTS `detail_absensi`;
DROP TABLE IF EXISTS `absensi`;
DROP TABLE IF EXISTS `tugas`;
DROP TABLE IF EXISTS `siswa_ruang_kelas`;
DROP TABLE IF EXISTS `siswa`;
DROP TABLE IF EXISTS `ruang_kelas`;
DROP TABLE IF EXISTS `mata_pelajaran`;

-- =====================================================
-- CREATE TABLES WITH EXACT STRUCTURE
-- =====================================================

-- Mata pelajaran table
CREATE TABLE `mata_pelajaran` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `kode_mapel` varchar(10) NOT NULL,
  `nama_mapel` varchar(100) NOT NULL,
  `kkm` decimal(5,2) DEFAULT 75.00,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Ruang kelas table
CREATE TABLE `ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nama_ruang_kelas` varchar(100) NOT NULL,
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
  `status` enum('aktif','nonaktif') DEFAULT 'aktif',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Siswa table
CREATE TABLE `siswa` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `nis` varchar(20) NOT NULL,
  `nama_siswa` varchar(100) NOT NULL,
  `jenis_kelamin` enum('L','P') NOT NULL,
  `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Siswa ruang kelas junction table
CREATE TABLE `siswa_ruang_kelas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `siswa_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `tanggal_masuk` date NOT NULL,
  `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Tugas table
CREATE TABLE `tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `mapel_id` int(11) NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `judul` varchar(255) NOT NULL,
  `deskripsi` text DEFAULT NULL,
  `tanggal` date NOT NULL,
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Absensi table
CREATE TABLE `absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tanggal` date NOT NULL,
  `ruang_kelas_id` int(11) NOT NULL,
  `mapel_id` int(11) NOT NULL,
  `semester` enum('1','2') NOT NULL DEFAULT '1',
  `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Detail absensi table
CREATE TABLE `detail_absensi` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `absensi_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `status` enum('hadir','sakit','izin','alpha') NOT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- Nilai tugas table
CREATE TABLE `nilai_tugas` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `tugas_id` int(11) NOT NULL,
  `siswa_id` int(11) NOT NULL,
  `nilai` decimal(5,2) DEFAULT NULL,
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;

-- =====================================================
-- INSERT SAMPLE DATA
-- =====================================================

-- Insert mata pelajaran
INSERT INTO `mata_pelajaran` (`kode_mapel`, `nama_mapel`, `kkm`) VALUES 
('MTK001', 'Matematika', 75.00),
('BIN001', 'Bahasa Indonesia', 75.00),
('ING001', 'Bahasa Inggris', 75.00),
('FIS001', 'Fisika', 75.00),
('KIM001', 'Kimia', 75.00),
('BIO001', 'Biologi', 75.00),
('SEJ001', 'Sejarah', 75.00),
('GEO001', 'Geografi', 75.00);

-- Insert ruang kelas
INSERT INTO `ruang_kelas` (`nama_ruang_kelas`, `semester`, `tahun_ajaran`, `status`) VALUES 
('X IPA 1', '1', '2024/2025', 'aktif'),
('X IPA 2', '1', '2024/2025', 'aktif'),
('X IPS 1', '1', '2024/2025', 'aktif'),
('XI IPA 1', '1', '2024/2025', 'aktif'),
('XI IPA 2', '1', '2024/2025', 'aktif'),
('XI IPS 1', '1', '2024/2025', 'aktif'),
('XII IPA 1', '1', '2024/2025', 'aktif'),
('XII IPA 2', '1', '2024/2025', 'aktif'),
('XII IPS 1', '1', '2024/2025', 'aktif');

-- Insert siswa
INSERT INTO `siswa` (`nis`, `nama_siswa`, `jenis_kelamin`, `status`) VALUES 
('2024001', 'Andi Pratama', 'L', 'aktif'),
('2024002', 'Sari Dewi', 'P', 'aktif'),
('2024003', 'Budi Setiawan', 'L', 'aktif'),
('2024004', 'Rina Sari', 'P', 'aktif'),
('2024005', 'Doni Kurniawan', 'L', 'aktif'),
('2024006', 'Maya Putri', 'P', 'aktif'),
('2024007', 'Rudi Hartono', 'L', 'aktif'),
('2024008', 'Lina Marlina', 'P', 'aktif'),
('2024009', 'Agus Salim', 'L', 'aktif'),
('2024010', 'Fitri Handayani', 'P', 'aktif'),
('2024011', 'Joko Susilo', 'L', 'aktif'),
('2024012', 'Dewi Sartika', 'P', 'aktif');

-- Assign students to classrooms
INSERT INTO `siswa_ruang_kelas` (`siswa_id`, `ruang_kelas_id`, `tanggal_masuk`, `status`) VALUES 
(1, 1, '2024-07-01', 'aktif'),
(2, 1, '2024-07-01', 'aktif'),
(3, 1, '2024-07-01', 'aktif'),
(4, 2, '2024-07-01', 'aktif'),
(5, 2, '2024-07-01', 'aktif'),
(6, 2, '2024-07-01', 'aktif'),
(7, 3, '2024-07-01', 'aktif'),
(8, 3, '2024-07-01', 'aktif'),
(9, 4, '2024-07-01', 'aktif'),
(10, 4, '2024-07-01', 'aktif'),
(11, 5, '2024-07-01', 'aktif'),
(12, 5, '2024-07-01', 'aktif');

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check setup
SELECT 'Setup Complete!' as message;

-- Count records
SELECT 'mata_pelajaran' as tabel, COUNT(*) as jumlah FROM mata_pelajaran
UNION ALL
SELECT 'ruang_kelas' as tabel, COUNT(*) as jumlah FROM ruang_kelas WHERE status = 'aktif'
UNION ALL
SELECT 'siswa' as tabel, COUNT(*) as jumlah FROM siswa WHERE status = 'aktif'
UNION ALL
SELECT 'siswa_ruang_kelas' as tabel, COUNT(*) as jumlah FROM siswa_ruang_kelas WHERE status = 'aktif';

-- Show sample assignments
SELECT 
    s.nama_siswa,
    rk.nama_ruang_kelas,
    srk.tanggal_masuk
FROM siswa s
JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id
JOIN ruang_kelas rk ON srk.ruang_kelas_id = rk.id
WHERE srk.status = 'aktif'
ORDER BY rk.nama_ruang_kelas, s.nama_siswa;

-- Show available subjects
SELECT kode_mapel, nama_mapel, kkm FROM mata_pelajaran ORDER BY nama_mapel;

-- Show active classrooms
SELECT id, nama_ruang_kelas, semester, tahun_ajaran FROM ruang_kelas WHERE status = 'aktif' ORDER BY nama_ruang_kelas;
