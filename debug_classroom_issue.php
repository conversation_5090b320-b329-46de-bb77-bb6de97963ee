<?php
require_once 'config/database.php';
require_once 'models/Tugas.php';

echo "<h1>Debug Classroom System Issue</h1>";

$database = new Database();
$conn = $database->getConnection();

echo "<h2>1. Checking Database Structure</h2>";

// Check if ruang_kelas table exists and has data
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'");
    $ruang_kelas_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div style='color: " . ($ruang_kelas_count > 0 ? 'green' : 'red') . ";'>✓ Active ruang_kelas: $ruang_kelas_count</div>";
    
    if ($ruang_kelas_count > 0) {
        echo "<h4>Sample ruang_kelas data:</h4>";
        $stmt = $conn->query("SELECT id, nama_ruang_kelas, semester, tahun_ajaran, status FROM ruang_kelas WHERE status = 'aktif' LIMIT 5");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nama</th><th>Semester</th><th>Tahun Ajaran</th><th>Status</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr><td>{$row['id']}</td><td>{$row['nama_ruang_kelas']}</td><td>{$row['semester']}</td><td>{$row['tahun_ajaran']}</td><td>{$row['status']}</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error checking ruang_kelas: " . $e->getMessage() . "</div>";
}

// Check mata_pelajaran
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM mata_pelajaran");
    $mapel_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div style='color: " . ($mapel_count > 0 ? 'green' : 'red') . ";'>✓ Mata pelajaran: $mapel_count</div>";
    
    if ($mapel_count > 0) {
        echo "<h4>Sample mata_pelajaran data:</h4>";
        $stmt = $conn->query("SELECT id, nama_mapel FROM mata_pelajaran LIMIT 5");
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nama Mapel</th></tr>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr><td>{$row['id']}</td><td>{$row['nama_mapel']}</td></tr>";
        }
        echo "</table>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error checking mata_pelajaran: " . $e->getMessage() . "</div>";
}

// Check tugas table structure
echo "<h2>2. Checking Tugas Table Structure</h2>";
try {
    $stmt = $conn->query("SHOW COLUMNS FROM tugas");
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $highlight = ($row['Field'] == 'ruang_kelas_id') ? 'background: yellow;' : '';
        echo "<tr style='$highlight'><td>{$row['Field']}</td><td>{$row['Type']}</td><td>{$row['Null']}</td><td>{$row['Key']}</td><td>{$row['Default']}</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error checking tugas structure: " . $e->getMessage() . "</div>";
}

// Check foreign key constraints
echo "<h2>3. Checking Foreign Key Constraints</h2>";
try {
    $stmt = $conn->query("
        SELECT 
            CONSTRAINT_NAME,
            TABLE_NAME,
            COLUMN_NAME,
            REFERENCED_TABLE_NAME,
            REFERENCED_COLUMN_NAME
        FROM information_schema.KEY_COLUMN_USAGE 
        WHERE REFERENCED_TABLE_SCHEMA = 'db_absensi' 
        AND TABLE_NAME = 'tugas'
        AND REFERENCED_TABLE_NAME IS NOT NULL
    ");
    
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>Constraint</th><th>Column</th><th>References</th></tr>";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr><td>{$row['CONSTRAINT_NAME']}</td><td>{$row['COLUMN_NAME']}</td><td>{$row['REFERENCED_TABLE_NAME']}.{$row['REFERENCED_COLUMN_NAME']}</td></tr>";
    }
    echo "</table>";
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error checking foreign keys: " . $e->getMessage() . "</div>";
}

// Test Tugas model
echo "<h2>4. Testing Tugas Model</h2>";
try {
    $tugas = new Tugas();
    
    // Test getActiveClassrooms
    echo "<h4>Testing getActiveClassrooms():</h4>";
    $classrooms = $tugas->getActiveClassrooms();
    if ($classrooms) {
        $classroom_count = 0;
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>Nama</th><th>Semester</th><th>Tahun Ajaran</th><th>Jumlah Siswa</th></tr>";
        while ($row = $classrooms->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr><td>{$row['id']}</td><td>{$row['nama_ruang_kelas']}</td><td>{$row['semester']}</td><td>{$row['tahun_ajaran']}</td><td>{$row['jumlah_siswa']}</td></tr>";
            $classroom_count++;
        }
        echo "</table>";
        echo "<div style='color: green;'>✓ Found $classroom_count active classrooms</div>";
    } else {
        echo "<div style='color: red;'>✗ getActiveClassrooms() returned false</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error testing Tugas model: " . $e->getMessage() . "</div>";
}

// Test creating a tugas
echo "<h2>5. Testing Tugas Creation</h2>";
try {
    // Get sample data
    $stmt = $conn->query("SELECT id FROM ruang_kelas WHERE status = 'aktif' LIMIT 1");
    $classroom = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stmt = $conn->query("SELECT id FROM mata_pelajaran LIMIT 1");
    $subject = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($classroom && $subject) {
        echo "<div>Testing with:</div>";
        echo "<div>- ruang_kelas_id: {$classroom['id']}</div>";
        echo "<div>- mapel_id: {$subject['id']}</div>";
        
        $tugas = new Tugas();
        $tugas->mapel_id = $subject['id'];
        $tugas->ruang_kelas_id = $classroom['id'];
        $tugas->judul = "Test Debug - " . date('Y-m-d H:i:s');
        $tugas->deskripsi = "Test untuk debug masalah";
        $tugas->tanggal = date('Y-m-d');
        $tugas->semester = "1";
        $tugas->tahun_ajaran = "2024/2025";
        
        echo "<div>Attempting to create tugas...</div>";
        if ($tugas->create()) {
            echo "<div style='color: green;'>✅ SUCCESS! Tugas created with ID: {$tugas->id}</div>";
            
            // Clean up
            $tugas->delete();
            echo "<div style='color: gray;'>Test tugas deleted</div>";
        } else {
            echo "<div style='color: red;'>❌ FAILED to create tugas</div>";
            
            // Check for session error
            if (isset($_SESSION['debug_error'])) {
                echo "<div style='color: red;'>Error: {$_SESSION['debug_error']}</div>";
                unset($_SESSION['debug_error']);
            }
        }
    } else {
        echo "<div style='color: red;'>✗ Missing sample data for testing</div>";
        if (!$classroom) echo "<div>- No active classroom found</div>";
        if (!$subject) echo "<div>- No subject found</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error testing tugas creation: " . $e->getMessage() . "</div>";
}

// Check periode aktif
echo "<h2>6. Checking Periode Aktif</h2>";
try {
    require_once 'models/PeriodeAktif.php';
    $periode = new PeriodeAktif();
    if ($periode->getActive()) {
        echo "<div style='color: green;'>✓ Active period found:</div>";
        echo "<div>- Semester: {$periode->semester}</div>";
        echo "<div>- Tahun Ajaran: {$periode->tahun_ajaran}</div>";
        echo "<div>- Tanggal Mulai: {$periode->tanggal_mulai}</div>";
        echo "<div>- Tanggal Selesai: {$periode->tanggal_selesai}</div>";
    } else {
        echo "<div style='color: orange;'>⚠ No active period found</div>";
    }
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error checking periode aktif: " . $e->getMessage() . "</div>";
}

// Suggested fixes
echo "<h2>7. Suggested Fixes</h2>";

echo "<div style='background: #f0f8ff; padding: 15px; border: 1px solid #0066cc; border-radius: 5px;'>";
echo "<h4>Possible Issues and Solutions:</h4>";
echo "<ol>";
echo "<li><strong>Missing Foreign Key Constraints:</strong><br>";
echo "Run: <code>ALTER TABLE tugas ADD CONSTRAINT fk_tugas_ruang_kelas FOREIGN KEY (ruang_kelas_id) REFERENCES ruang_kelas(id);</code></li>";
echo "<li><strong>No Active Classrooms:</strong><br>";
echo "Make sure you have ruang_kelas with status='aktif'</li>";
echo "<li><strong>Semester/Tahun Ajaran Mismatch:</strong><br>";
echo "Check if ruang_kelas semester/tahun_ajaran matches what you're trying to create</li>";
echo "<li><strong>Missing Periode Aktif:</strong><br>";
echo "Set up an active academic period in the system</li>";
echo "</ol>";
echo "</div>";

// Quick fix buttons
echo "<h2>8. Quick Fixes</h2>";
echo "<div style='margin: 20px 0;'>";
echo "<a href='?fix=add_constraints' style='background: #007bff; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Add Foreign Key Constraints</a>";
echo "<a href='?fix=create_sample_data' style='background: #28a745; color: white; padding: 10px 15px; text-decoration: none; border-radius: 5px; margin-right: 10px;'>Create Sample Data</a>";
echo "</div>";

// Handle quick fixes
if (isset($_GET['fix'])) {
    echo "<h3>Applying Fix: " . $_GET['fix'] . "</h3>";
    
    if ($_GET['fix'] == 'add_constraints') {
        try {
            // Add foreign key constraint for tugas
            $conn->exec("ALTER TABLE tugas ADD CONSTRAINT IF NOT EXISTS fk_tugas_ruang_kelas FOREIGN KEY (ruang_kelas_id) REFERENCES ruang_kelas(id) ON DELETE SET NULL");
            echo "<div style='color: green;'>✓ Added foreign key constraint for tugas</div>";
            
            // Add foreign key constraint for absensi
            $conn->exec("ALTER TABLE absensi ADD CONSTRAINT IF NOT EXISTS fk_absensi_ruang_kelas FOREIGN KEY (ruang_kelas_id) REFERENCES ruang_kelas(id) ON DELETE SET NULL");
            echo "<div style='color: green;'>✓ Added foreign key constraint for absensi</div>";
            
        } catch (Exception $e) {
            echo "<div style='color: red;'>✗ Error adding constraints: " . $e->getMessage() . "</div>";
        }
    }
    
    if ($_GET['fix'] == 'create_sample_data') {
        try {
            // Create sample ruang_kelas if none exist
            $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($count == 0) {
                $conn->exec("INSERT INTO ruang_kelas (nama_ruang_kelas, semester, tahun_ajaran, status) VALUES 
                    ('X IPA 1', '1', '2024/2025', 'aktif'),
                    ('X IPA 2', '1', '2024/2025', 'aktif'),
                    ('XI IPA 1', '1', '2024/2025', 'aktif')");
                echo "<div style='color: green;'>✓ Created sample ruang_kelas data</div>";
            } else {
                echo "<div style='color: blue;'>ℹ Ruang kelas data already exists</div>";
            }
            
            // Create sample mata_pelajaran if none exist
            $stmt = $conn->query("SELECT COUNT(*) as count FROM mata_pelajaran");
            $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            
            if ($count == 0) {
                $conn->exec("INSERT INTO mata_pelajaran (kode_mapel, nama_mapel, kkm) VALUES 
                    ('MTK001', 'Matematika', 75),
                    ('BIN001', 'Bahasa Indonesia', 75),
                    ('ING001', 'Bahasa Inggris', 75)");
                echo "<div style='color: green;'>✓ Created sample mata_pelajaran data</div>";
            } else {
                echo "<div style='color: blue;'>ℹ Mata pelajaran data already exists</div>";
            }
            
        } catch (Exception $e) {
            echo "<div style='color: red;'>✗ Error creating sample data: " . $e->getMessage() . "</div>";
        }
    }
}

echo "<hr>";
echo "<p><a href='tugas/index.php'>Test Tugas System</a> | <a href='migrate_existing_classroom_data.php'>Run Migration</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3, h4 { color: #333; }
table { margin: 10px 0; }
th, td { padding: 8px; text-align: left; }
th { background-color: #f2f2f2; }
code { background: #f4f4f4; padding: 2px 4px; border-radius: 3px; }
</style>
