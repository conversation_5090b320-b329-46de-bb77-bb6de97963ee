<?php
require_once 'config/database.php';

echo "<h1>Fix Foreign Key Constraints</h1>";

$database = new Database();
$conn = $database->getConnection();

if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>⚠️ Foreign Key Constraint Fix</h3>";
    echo "<p>This will add missing foreign key constraints for the classroom system:</p>";
    echo "<ul>";
    echo "<li>Add constraint: tugas.ruang_kelas_id → ruang_kelas.id</li>";
    echo "<li>Add constraint: absensi.ruang_kelas_id → ruang_kelas.id</li>";
    echo "<li>Add constraint: nilai.ruang_kelas_id → ruang_kelas.id</li>";
    echo "<li>Add indexes for better performance</li>";
    echo "</ul>";
    echo "<p><strong>SAFE:</strong> This will not modify existing data, only add constraints.</p>";
    echo "<p><a href='?confirm=yes' style='background: #28a745; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Add Foreign Key Constraints</a></p>";
    echo "</div>";
    exit;
}

try {
    $conn->beginTransaction();
    
    echo "<h2>Adding Foreign Key Constraints...</h2>";
    
    // List of constraints to add
    $constraints = [
        [
            'table' => 'tugas',
            'constraint' => 'fk_tugas_ruang_kelas',
            'column' => 'ruang_kelas_id',
            'references' => 'ruang_kelas(id)',
            'action' => 'SET NULL'
        ],
        [
            'table' => 'absensi',
            'constraint' => 'fk_absensi_ruang_kelas',
            'column' => 'ruang_kelas_id',
            'references' => 'ruang_kelas(id)',
            'action' => 'SET NULL'
        ],
        [
            'table' => 'nilai',
            'constraint' => 'fk_nilai_ruang_kelas',
            'column' => 'ruang_kelas_id',
            'references' => 'ruang_kelas(id)',
            'action' => 'SET NULL'
        ],
        [
            'table' => 'nilai_sikap',
            'constraint' => 'fk_nilai_sikap_ruang_kelas',
            'column' => 'ruang_kelas_id',
            'references' => 'ruang_kelas(id)',
            'action' => 'SET NULL'
        ],
        [
            'table' => 'nilai_tugas',
            'constraint' => 'fk_nilai_tugas_ruang_kelas',
            'column' => 'ruang_kelas_id',
            'references' => 'ruang_kelas(id)',
            'action' => 'SET NULL'
        ]
    ];
    
    foreach ($constraints as $constraint) {
        echo "<h3>Processing table: {$constraint['table']}</h3>";
        
        // Check if table exists
        try {
            $check_table = "SHOW TABLES LIKE '{$constraint['table']}'";
            $stmt = $conn->query($check_table);
            
            if ($stmt->rowCount() == 0) {
                echo "<div style='color: orange;'>⚠ Table {$constraint['table']} doesn't exist, skipping</div>";
                continue;
            }
            
            // Check if column exists
            $check_column = "SHOW COLUMNS FROM {$constraint['table']} LIKE '{$constraint['column']}'";
            $stmt = $conn->query($check_column);
            
            if ($stmt->rowCount() == 0) {
                echo "<div style='color: orange;'>⚠ Column {$constraint['column']} doesn't exist in {$constraint['table']}, adding it first</div>";
                
                // Add the column
                $add_column = "ALTER TABLE {$constraint['table']} ADD COLUMN {$constraint['column']} int(11) DEFAULT NULL";
                $conn->exec($add_column);
                echo "<div style='color: green;'>✓ Added column {$constraint['column']} to {$constraint['table']}</div>";
            }
            
            // Check if constraint already exists
            $check_constraint = "
                SELECT CONSTRAINT_NAME 
                FROM information_schema.KEY_COLUMN_USAGE 
                WHERE TABLE_SCHEMA = 'db_absensi' 
                AND TABLE_NAME = '{$constraint['table']}' 
                AND CONSTRAINT_NAME = '{$constraint['constraint']}'
            ";
            $stmt = $conn->query($check_constraint);
            
            if ($stmt->rowCount() > 0) {
                echo "<div style='color: blue;'>ℹ Constraint {$constraint['constraint']} already exists</div>";
                continue;
            }
            
            // Add the foreign key constraint
            $add_constraint = "
                ALTER TABLE {$constraint['table']} 
                ADD CONSTRAINT {$constraint['constraint']} 
                FOREIGN KEY ({$constraint['column']}) 
                REFERENCES {$constraint['references']} 
                ON DELETE {$constraint['action']}
            ";
            
            $conn->exec($add_constraint);
            echo "<div style='color: green;'>✓ Added constraint {$constraint['constraint']}</div>";
            
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key') !== false || 
                strpos($e->getMessage(), 'already exists') !== false) {
                echo "<div style='color: blue;'>ℹ Constraint {$constraint['constraint']} already exists</div>";
            } else {
                echo "<div style='color: red;'>✗ Error with {$constraint['table']}: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // Add indexes for better performance
    echo "<h2>Adding Indexes...</h2>";
    
    $indexes = [
        ['table' => 'tugas', 'column' => 'ruang_kelas_id', 'index' => 'idx_tugas_ruang_kelas'],
        ['table' => 'absensi', 'column' => 'ruang_kelas_id', 'index' => 'idx_absensi_ruang_kelas'],
        ['table' => 'nilai', 'column' => 'ruang_kelas_id', 'index' => 'idx_nilai_ruang_kelas']
    ];
    
    foreach ($indexes as $index) {
        try {
            $add_index = "ALTER TABLE {$index['table']} ADD INDEX IF NOT EXISTS {$index['index']} ({$index['column']})";
            $conn->exec($add_index);
            echo "<div style='color: green;'>✓ Added index {$index['index']} to {$index['table']}</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), 'Duplicate key') !== false) {
                echo "<div style='color: blue;'>ℹ Index {$index['index']} already exists</div>";
            } else {
                echo "<div style='color: orange;'>⚠ Could not add index to {$index['table']}: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // Clean up invalid ruang_kelas_id references
    echo "<h2>Cleaning Up Invalid References...</h2>";
    
    $cleanup_queries = [
        "UPDATE tugas SET ruang_kelas_id = NULL WHERE ruang_kelas_id IS NOT NULL AND ruang_kelas_id NOT IN (SELECT id FROM ruang_kelas)",
        "UPDATE absensi SET ruang_kelas_id = NULL WHERE ruang_kelas_id IS NOT NULL AND ruang_kelas_id NOT IN (SELECT id FROM ruang_kelas)",
        "UPDATE nilai SET ruang_kelas_id = NULL WHERE ruang_kelas_id IS NOT NULL AND ruang_kelas_id NOT IN (SELECT id FROM ruang_kelas)"
    ];
    
    foreach ($cleanup_queries as $query) {
        try {
            $result = $conn->exec($query);
            $table = explode(' ', $query)[1];
            echo "<div style='color: green;'>✓ Cleaned up $result invalid references in $table</div>";
        } catch (PDOException $e) {
            echo "<div style='color: orange;'>⚠ Cleanup warning: " . $e->getMessage() . "</div>";
        }
    }
    
    // Verification
    echo "<h2>Verification</h2>";
    
    $verification_queries = [
        'active_classrooms' => "SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'",
        'subjects' => "SELECT COUNT(*) as count FROM mata_pelajaran",
        'tugas_with_classroom' => "SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NOT NULL",
        'absensi_with_classroom' => "SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NOT NULL"
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Item</th><th style='border: 1px solid #ddd; padding: 10px;'>Count</th><th style='border: 1px solid #ddd; padding: 10px;'>Status</th></tr>";
    
    foreach ($verification_queries as $name => $query) {
        try {
            $stmt = $conn->query($query);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $count = $result['count'];
            $status = $count > 0 ? "<span style='color: green;'>✓ OK</span>" : "<span style='color: orange;'>⚠ Empty</span>";
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . ucfirst(str_replace('_', ' ', $name)) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>$count</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>$status</td>";
            echo "</tr>";
        } catch (PDOException $e) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . ucfirst(str_replace('_', ' ', $name)) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>Error</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'><span style='color: red;'>✗ " . $e->getMessage() . "</span></td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    $conn->commit();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Foreign Key Constraints Added Successfully!</h3>";
    echo "<p>The database constraints have been properly configured for the classroom system.</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Test creating a new tugas - it should now work properly</li>";
    echo "<li>Test creating a new absensi - it should now work properly</li>";
    echo "<li>If you still get errors, run the debug script to identify remaining issues</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    $conn->rollback();
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Failed to Add Constraints</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>The database has been rolled back to its previous state.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='debug_classroom_issue.php'>Debug Issues</a> | <a href='tugas/index.php'>Test Tugas System</a> | <a href='absensi/create.php'>Test Absensi System</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
