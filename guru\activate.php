<?php
require_once '../template/header.php';
require_once '../config/database.php';

// Initialize response
$response = [
    'success' => false,
    'message' => '',
    'redirect' => 'index.php'
];

// Check role first
if ($_SESSION['role'] !== 'admin') {
    $response['message'] = 'Anda tidak memiliki akses ke halaman ini!';
} else {
    // Database connection
    $database = new Database();
    $db = $database->getConnection();

    // Get guru data and process
    $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;

    if ($id <= 0) {
        $response['message'] = "ID guru tidak valid!";
    } else {
        try {
            // Get guru data
            $query = "SELECT nip, nama_lengkap FROM guru WHERE id = :id";
            $stmt = $db->prepare($query);
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            if ($stmt && $stmt->rowCount() > 0) {
                $guru = $stmt->fetch(PDO::FETCH_ASSOC);
                
                // Check if user already exists
                $check_user = "SELECT id FROM users WHERE username = :username";
                $stmt = $db->prepare($check_user);
                $username = 'guru' . $guru['nip'];
                $stmt->bindParam(':username', $username);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    $response['message'] = "User dengan username $username sudah ada!";
                } else {
                    $password = password_hash('guru123', PASSWORD_DEFAULT);
                    
                    // Begin transaction
                    $db->beginTransaction();
                    
                    try {
                        // Update guru status
                        $update_guru = "UPDATE guru SET status = 'aktif' WHERE id = :id";
                        $stmt = $db->prepare($update_guru);
                        $stmt->bindParam(':id', $id);
                        $stmt->execute();
                        
                        // Create user account - using nama_lengkap instead of nama
                        $insert_user = "INSERT INTO users (username, password, nama_lengkap, role) VALUES (:username, :password, :nama_lengkap, 'guru')";
                        $stmt = $db->prepare($insert_user);
                        $stmt->bindParam(':username', $username);
                        $stmt->bindParam(':password', $password);
                        $stmt->bindParam(':nama_lengkap', $guru['nama_lengkap']);
                        $stmt->execute();
                        
                        $db->commit();
                        $_SESSION['success'] = "Akun guru berhasil diaktifkan! Username: $username, Password: guru123";
                        $response['success'] = true;
                    } catch (Exception $e) {
                        $db->rollBack();
                        $response['message'] = "Terjadi kesalahan: " . $e->getMessage();
                    }
                }
            } else {
                $response['message'] = "Data guru tidak ditemukan!";
            }
        } catch (Exception $e) {
            $response['message'] = "Terjadi kesalahan: " . $e->getMessage();
        }
    }
}

// If there's an error message and no success message set
if (!empty($response['message']) && !$response['success']) {
    $_SESSION['error'] = $response['message'];
}
?>

<script>
    window.location.href = '<?php echo $response['redirect']; ?>';
</script>
