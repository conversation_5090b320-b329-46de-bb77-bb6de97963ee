<?php
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$sheet->setCellValue('A1', 'NIP');
$sheet->setCellValue('B1', '<PERSON>a <PERSON>');
$sheet->setCellValue('C1', '<PERSON><PERSON> (L/P)');
$sheet->setCellValue('D1', 'Alamat');
$sheet->setCellValue('E1', 'No. Telp');
$sheet->setCellValue('F1', 'Email');

// Example data
$sheet->setCellValue('A2', '12345');
$sheet->setCellValue('B2', '<PERSON><PERSON>');
$sheet->setCellValue('C2', 'L');
$sheet->setCellValue('D2', 'Jl. Example No. 123');
$sheet->setCellValue('E2', '08123456789');
$sheet->setCellValue('F2', '<EMAIL>');

// Auto-size columns
foreach(range('A','F') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Style the header row
$sheet->getStyle('A1:F1')->getFont()->setBold(true);
$sheet->getStyle('A1:F1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');

// Create the Excel file
$writer = new Xlsx($spreadsheet);
$writer->save('template_guru.xlsx');

echo "Template created successfully!";
?>
