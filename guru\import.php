<?php
require_once '../config/database.php';
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

// Process form submission before any output
$error = '';
$success = '';
$redirect = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    try {
        $file = $_FILES['file']['tmp_name'];
        $reader = new Xlsx();
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = [];

        // Start from row 2 (after header)
        foreach ($worksheet->getRowIterator(2) as $row) {
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            
            $cells = [];
            foreach ($cellIterator as $cell) {
                $cells[] = $cell->getValue();
            }

            // Skip empty rows
            if (empty($cells[0]) && empty($cells[1])) {
                continue;
            }

            $rowData = [
                'nip' => $cells[0],
                'nama_lengkap' => $cells[1],
                'jenis_kelamin' => $cells[2],
                'alamat' => $cells[3],
                'no_telp' => $cells[4],
                'email' => $cells[5]
            ];
            
            $data[] = $rowData;
        }

        // Database connection
        $database = new Database();
        $db = $database->getConnection();
        $db->beginTransaction();

        try {
            foreach ($data as $row) {
                // Validate NIP
                $check_query = "SELECT id FROM guru WHERE nip = :nip";
                $stmt = $db->prepare($check_query);
                $stmt->bindParam(':nip', $row['nip']);
                $stmt->execute();
                
                if ($stmt->rowCount() > 0) {
                    throw new Exception("NIP {$row['nip']} sudah terdaftar");
                }

                $query = "INSERT INTO guru (nip, nama_lengkap, jenis_kelamin, alamat, no_telp, email) 
                         VALUES (:nip, :nama_lengkap, :jenis_kelamin, :alamat, :no_telp, :email)";
                $stmt = $db->prepare($query);
                $stmt->execute($row);
            }

            $db->commit();
            $_SESSION['success'] = "Data guru berhasil diimpor";
            $redirect = true;
        } catch (Exception $e) {
            $db->rollBack();
            $error = "Error: " . $e->getMessage();
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }

    if ($redirect) {
        header('Location: index.php');
        exit;
    }
}

// Include header after all potential redirects
require_once '../template/header.php';

// Check for admin role after header (since it includes session)
if ($_SESSION['role'] !== 'admin') {
    header('Location: ../index.php');
    exit;
}
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Import Data Guru</h5>
            </div>
            <div class="card-body">
                <?php if (!empty($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>

                <?php if (!empty($success)): ?>
                <div class="alert alert-success">
                    <?php echo $success; ?>
                </div>
                <?php endif; ?>

                <form action="" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">File Excel</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx" required>
                        <div class="form-text">
                            Format file: .xlsx<br>
                            Kolom yang dibutuhkan:
                            <ul>
                                <li>NIP (kolom A)</li>
                                <li>Nama Lengkap (kolom B)</li>
                                <li>Jenis Kelamin (L/P) (kolom C)</li>
                                <li>Alamat (kolom D)</li>
                                <li>No. Telp (kolom E)</li>
                                <li>Email (kolom F)</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> NIP harus unik dan tidak boleh duplikat.
                            </div>
                            <a href="template_guru.xlsx" class="btn btn-sm btn-info">
                                <i class="fas fa-download"></i> Download Template
                            </a>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php require_once '../template/footer.php'; ?>
