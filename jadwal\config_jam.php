<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();
require_once '../template/header.php';
require_once '../models/JamPelajaranConfig.php';
require_once '../models/DetailJamPelajaran.php';

$config = new JamPelajaranConfig();
$detail = new DetailJamPelajaran();
$error_msg = "";
$success_msg = "";

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $hari = $_POST['hari'];
    $jumlah_jam = $_POST['jumlah_jam'];
    $existing_config = $config->getByHari($hari);
    
    try {
        // Validate that all required time fields are present
        $valid = true;
        $time_data = [];
        for ($i = 1; $i <= $jumlah_jam; $i++) {
            if (!isset($_POST["jam_mulai_$i"]) || !isset($_POST["jam_selesai_$i"]) || 
                empty($_POST["jam_mulai_$i"]) || empty($_POST["jam_selesai_$i"])) {
                $valid = false;
                $error_msg = "Semua field jam harus diisi";
                break;
            }
            $time_data[] = [
                'jam_ke' => $i,
                'jam_mulai' => $_POST["jam_mulai_$i"],
                'jam_selesai' => $_POST["jam_selesai_$i"]
            ];
        }

        if ($valid) {
            $config->hari = $hari;
            $config->jumlah_jam = $jumlah_jam;
            $isUpdate = false;
            
            if ($existing_config) {
                $config->id = $existing_config['id'];
                if ($config->update()) {
                    $detail->deleteByConfigId($config->id);
                    $success = true;
                    $isUpdate = true;
                }
            } else {
                $success = $config->create();
            }
            
            if ($success) {
                // Insert time details
                foreach ($time_data as $time) {
                    $detail->config_id = $config->id;
                    $detail->jam_ke = $time['jam_ke'];
                    $detail->jam_mulai = $time['jam_mulai'];
                    $detail->jam_selesai = $time['jam_selesai'];
                    if (!$detail->create()) {
                        throw new Exception("Gagal menyimpan detail jam ke-" . $time['jam_ke']);
                    }
                }
                header("Location: config_jam.php?success=1&action=" . ($isUpdate ? "update" : "create") . "&hari=" . urlencode($hari));
                exit;
            }
        }
    } catch (Exception $e) {
        $error_msg = "Gagal menyimpan konfigurasi: " . $e->getMessage();
    }
}

// Get success message from URL if exists
if (isset($_GET['success']) && $_GET['success'] == '1') {
    $action = $_GET['action'] ?? '';
    $hari = $_GET['hari'] ?? '';
    if ($action == 'update') {
        $success_msg = "Konfigurasi jam pelajaran untuk hari " . htmlspecialchars($hari) . " berhasil diperbarui";
    } else {
        $success_msg = "Konfigurasi jam pelajaran untuk hari " . htmlspecialchars($hari) . " berhasil disimpan";
    }
}

$hari_list = ['Senin', 'Selasa', 'Rabu', 'Kamis', 'Jumat', 'Sabtu'];
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Konfigurasi Jam Pelajaran</h5>
                </div>
                <div class="card-body">
                    <?php if ($error_msg): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo $error_msg; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>
                    <?php if ($success_msg): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo $success_msg; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form action="" method="post" id="configForm">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label for="hari" class="form-label">Hari</label>
                                <select class="form-select" id="hari" name="hari" required>
                                    <option value="">Pilih Hari</option>
                                    <?php foreach ($hari_list as $hari): ?>
                                        <option value="<?php echo $hari; ?>"><?php echo $hari; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label for="jumlah_jam" class="form-label">Jumlah Jam Pelajaran</label>
                                <input type="number" class="form-control" id="jumlah_jam" name="jumlah_jam" min="1" max="12" required>
                            </div>
                            <div class="col-md-4">
                                <label for="copy_dari" class="form-label">Copy dari Hari</label>
                                <select class="form-select" id="copy_dari" name="copy_dari">
                                    <option value="">Pilih Hari (Opsional)</option>
                                    <?php foreach ($hari_list as $hari): ?>
                                        <option value="<?php echo $hari; ?>"><?php echo $hari; ?></option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>

                        <div id="jamFields" class="mb-3">
                            <!-- Fields for lesson hours will be generated here -->
                        </div>

                        <button type="submit" class="btn btn-primary">Simpan Konfigurasi</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.getElementById('hari').addEventListener('change', loadConfig);
document.getElementById('jumlah_jam').addEventListener('change', function() {
    generateFields();
});
document.getElementById('copy_dari').addEventListener('change', copyFromDay);

function loadConfig() {
    const hari = document.getElementById('hari').value;
    const copyDari = document.getElementById('copy_dari');
    
    if (!hari) {
        document.getElementById('jumlah_jam').value = '';
        document.getElementById('jamFields').innerHTML = '';
        return;
    }

    // Reset and disable copy_dari for the same day
    copyDari.value = '';
    Array.from(copyDari.options).forEach(option => {
        option.disabled = option.value === hari;
    });

    fetch(`get_config.php?hari=${hari}`)
        .then(response => response.json())
        .then(data => {
            if (data.config) {
                document.getElementById('jumlah_jam').value = data.config.jumlah_jam;
                generateFields(data.details);
            } else {
                document.getElementById('jumlah_jam').value = '';
                document.getElementById('jamFields').innerHTML = '';
            }
        });
}

function copyFromDay() {
    const copyDari = document.getElementById('copy_dari').value;
    if (!copyDari) return;

    fetch(`get_config.php?hari=${copyDari}`)
        .then(response => response.json())
        .then(data => {
            if (data.config) {
                document.getElementById('jumlah_jam').value = data.config.jumlah_jam;
                generateFields(data.details);
            }
        });
}

function generateFields(existingData = null) {
    const container = document.getElementById('jamFields');
    const jumlah_jam = parseInt(document.getElementById('jumlah_jam').value) || 0;
    
    container.innerHTML = '';
    
    if (jumlah_jam <= 0) {
        return;
    }
    
    for (let i = 1; i <= jumlah_jam; i++) {
        const existing = existingData ? existingData.find(d => d.jam_ke == i) : null;
        
        const row = document.createElement('div');
        row.className = 'row mb-2';
        row.innerHTML = `
            <div class="col-12">
                <h6>Jam ke-${i}</h6>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">Jam Mulai</label>
                    <input type="time" class="form-control" name="jam_mulai_${i}" required 
                           value="${existing ? existing.jam_mulai : '07:00'}"
                           pattern="[0-9]{2}:[0-9]{2}">
                </div>
            </div>
            <div class="col-md-6">
                <div class="form-group">
                    <label class="form-label">Jam Selesai</label>
                    <input type="time" class="form-control" name="jam_selesai_${i}" required
                           value="${existing ? existing.jam_selesai : '07:45'}"
                           pattern="[0-9]{2}:[0-9]{2}">
                </div>
            </div>
        `;
        container.appendChild(row);
    }
}

// Initialize fields if hari is already selected
if (document.getElementById('hari').value) {
    loadConfig();
}

// Add form validation before submit
document.querySelector('form').addEventListener('submit', function(e) {
    const jumlah_jam = parseInt(document.getElementById('jumlah_jam').value) || 0;
    if (jumlah_jam <= 0) {
        e.preventDefault();
        alert('Jumlah jam harus diisi');
        return;
    }

    let valid = true;
    for (let i = 1; i <= jumlah_jam; i++) {
        const mulai = document.querySelector(`input[name="jam_mulai_${i}"]`);
        const selesai = document.querySelector(`input[name="jam_selesai_${i}"]`);
        
        if (!mulai || !selesai || !mulai.value || !selesai.value) {
            valid = false;
            break;
        }
    }

    if (!valid) {
        e.preventDefault();
        alert('Semua field jam harus diisi');
    }
});
</script>

<?php require_once '../template/footer.php'; ?>
