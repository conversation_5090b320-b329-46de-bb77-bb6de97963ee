<?php
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set headers
$sheet->setCellValue('A1', 'Nama Kelas');
$sheet->setCellValue('B1', 'Nama Mapel');
$sheet->setCellValue('C1', 'Hari');
$sheet->setCellValue('D1', 'Jam Mulai');
$sheet->setCellValue('E1', 'Jam Selesai');

// Example data
$sheet->setCellValue('A2', 'X IPA 1');
$sheet->setCellValue('B2', 'Matematika');
$sheet->setCellValue('C2', 'Senin');
$sheet->setCellValue('D2', '07:00');
$sheet->setCellValue('E2', '08:30');

// Auto-size columns
foreach(range('A','E') as $col) {
    $sheet->getColumnDimension($col)->setAutoSize(true);
}

// Style the header row
$sheet->getStyle('A1:E1')->getFont()->setBold(true);
$sheet->getStyle('A1:E1')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('CCCCCC');

// Create the Excel file
$writer = new Xlsx($spreadsheet);
$writer->save('template_jadwal.xlsx');

echo "Template created successfully!";
?>
