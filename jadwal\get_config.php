<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();
require_once '../models/JamPelajaranConfig.php';
require_once '../models/DetailJamPelajaran.php';

header('Content-Type: application/json');

if (!isset($_GET['hari'])) {
    echo json_encode(['error' => 'Hari tidak ditemukan']);
    exit;
}

$config = new JamPelajaranConfig();
$detail = new DetailJamPelajaran();

$config_data = $config->getByHari($_GET['hari']);
$response = ['config' => $config_data];

if ($config_data) {
    $details = $detail->getByConfigId($config_data['id']);
    $details_array = [];
    while ($row = $details->fetch(PDO::FETCH_ASSOC)) {
        $details_array[] = $row;
    }
    $response['details'] = $details_array;
} else {
    $response['details'] = [];
}

echo json_encode($response);
