<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();
require_once '../template/header.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require '../vendor/autoload.php';

use PhpOffice\PhpSpreadsheet\Reader\Xlsx;

if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['file'])) {
    try {
        $file = $_FILES['file']['tmp_name'];
        $reader = new Xlsx();
        $spreadsheet = $reader->load($file);
        $worksheet = $spreadsheet->getActiveSheet();
        $data = [];

        // Get class and subject data for mapping
        $kelas = new Kelas();
        $mapel = new MataPelajaran();
        
        $kelas_list = $kelas->getAll();
        $kelas_map = [];
        while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)) {
            $kelas_map[strtolower($row['nama_kelas'])] = $row['id'];
        }

        $mapel_list = $mapel->getAll();
        $mapel_map = [];
        while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
            $mapel_map[strtolower($row['nama_mapel'])] = $row['id'];
        }

        // Start from row 2 (after header)
        foreach ($worksheet->getRowIterator(2) as $row) {
            $rowData = [];
            $cellIterator = $row->getCellIterator();
            $cellIterator->setIterateOnlyExistingCells(false);
            
            $cells = [];
            foreach ($cellIterator as $cell) {
                $cells[] = $cell->getValue();
            }

            // Skip empty rows
            if (empty($cells[0]) && empty($cells[1])) {
                continue;
            }

            // Get kelas_id and mapel_id from names
            $nama_kelas = strtolower(trim($cells[0]));
            $nama_mapel = strtolower(trim($cells[1]));
            
            $kelas_id = isset($kelas_map[$nama_kelas]) ? $kelas_map[$nama_kelas] : null;
            $mapel_id = isset($mapel_map[$nama_mapel]) ? $mapel_map[$nama_mapel] : null;

            if (!$kelas_id) {
                throw new Exception("Kelas '$cells[0]' tidak ditemukan");
            }
            if (!$mapel_id) {
                throw new Exception("Mata Pelajaran '$cells[1]' tidak ditemukan");
            }

            $rowData = [
                'kelas_id' => $kelas_id,
                'mapel_id' => $mapel_id,
                'hari' => $cells[2],
                'jam_mulai' => $cells[3],
                'jam_selesai' => $cells[4]
            ];
            
            $data[] = $rowData;
        }

        $jadwal = new JadwalPelajaran();
        if ($jadwal->importFromArray($data)) {
            header("Location: index.php?success=1");
            exit;
        } else {
            $error = "Gagal mengimpor data";
        }
    } catch (Exception $e) {
        $error = "Error: " . $e->getMessage();
    }
}
?>

<div class="row">
    <div class="col-md-6 offset-md-3">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Import Data Jadwal Pelajaran</h5>
            </div>
            <div class="card-body">
                <?php if (isset($error)): ?>
                <div class="alert alert-danger">
                    <?php echo $error; ?>
                </div>
                <?php endif; ?>

                <form action="" method="post" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="file" class="form-label">File Excel</label>
                        <input type="file" class="form-control" id="file" name="file" accept=".xlsx" required>
                        <div class="form-text">
                            Format file: .xlsx<br>
                            Kolom yang dibutuhkan:
                            <ul>
                                <li>Nama Kelas (kolom A)</li>
                                <li>Nama Mapel (kolom B)</li>
                                <li>Hari (kolom C)</li>
                                <li>Jam Mulai (HH:mm) (kolom D)</li>
                                <li>Jam Selesai (HH:mm) (kolom E)</li>
                            </ul>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i> Pastikan data kelas dan mata pelajaran sudah ada sebelum melakukan import jadwal.
                            </div>
                            <a href="template_jadwal.xlsx" class="btn btn-sm btn-info">
                                <i class="fas fa-download"></i> Download Template
                            </a>
                        </div>
                    </div>
                    <div class="d-flex justify-content-between">
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left"></i> Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload"></i> Import Data
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php
require_once '../template/footer.php';
?>
