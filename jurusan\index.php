<?php
require_once __DIR__ . '/../middleware/auth.php';
checkAdminAccess();

require_once '../template/header.php';
require_once '../models/Jurusan.php';

$jurusan = new Jurusan();
$result = $jurusan->getAll();
?>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Data Jurusan</h5>
                <a href="create.php" class="btn btn-primary">
                    <i class="fas fa-plus"></i> <PERSON><PERSON> Jurusan
                </a>
            </div>
            <div class="card-body">
                <?php if (isset($_SESSION['success'])): ?>
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        <?php
                        echo $_SESSION['success'];
                        unset($_SESSION['success']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>

                <?php if (isset($_SESSION['error'])): ?>
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        <?php
                        echo $_SESSION['error'];
                        unset($_SESSION['error']);
                        ?>
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                <?php endif; ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tableJurusan">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Kode Jurusan</th>
                                <th>Nama Jurusan</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            $hasData = false;
                            while ($row = $result->fetch(PDO::FETCH_ASSOC)):
                                $hasData = true;
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($row['kode_jurusan']); ?></td>
                                <td><?php echo htmlspecialchars($row['nama_jurusan']); ?></td>
                                <td>
                                    <a href="edit.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-warning">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="delete.php?id=<?php echo $row['id']; ?>" class="btn btn-sm btn-danger" onclick="return confirmDelete()">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </td>
                            </tr>
                            <?php endwhile; ?>
                        </tbody>
                    </table>
                    <?php if (!$hasData): ?>
                        <p class="text-center mt-3">Tidak ada data jurusan</p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableJurusan').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json",
            "emptyTable": "Tidak ada data jurusan",
            "zeroRecords": "Tidak ditemukan data yang sesuai"
        },
        "dom": "<'row'<'col-sm-12 col-md-6'l><'col-sm-12 col-md-6'f>>" +
               "<'row'<'col-sm-12'tr>>" +
               "<'row'<'col-sm-12 col-md-5'i><'col-sm-12 col-md-7'p>>",
        "lengthMenu": [[10, 25, 50, -1], [10, 25, 50, "Semua"]],
        "pageLength": 10,
        "order": [[2, "asc"]], // Urutkan berdasarkan nama jurusan
        "columnDefs": [
            {"orderable": false, "targets": 3}, // Kolom aksi tidak bisa diurutkan
            {"width": "100px", "targets": 3} // Atur lebar kolom aksi
        ],
        "initComplete": function(settings, json) {
            // Tambahkan margin atas pada info dan pagination
            $('.dataTables_info, .dataTables_paginate').addClass('mt-3');
        }
    });
});

function confirmDelete() {
    return confirm("Apakah Anda yakin ingin menghapus data ini?");
}
</script>

<?php
require_once '../template/footer.php';
?>