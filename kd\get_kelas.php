<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../config/database.php';
require_once '../models/KompetensiDasar.php';
require_once '../models/Guru.php';

header('Content-Type: application/json');

// Get teacher ID from logged in user
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    echo json_encode([]);
    exit();
}

// Validate input
if (!isset($_GET['mapel_id']) || !isset($_GET['guru_id'])) {
    echo json_encode([]);
    exit();
}

$mapel_id = $_GET['mapel_id'];
$request_guru_id = $_GET['guru_id'];

// Security check: ensure the requested guru_id matches the logged-in teacher
if ($guru_id != $request_guru_id) {
    echo json_encode([]);
    exit();
}

try {
    $kd = new KompetensiDasar();
    $kelas_list = $kd->getKelasByMapelAndGuru($mapel_id, $guru_id);
    
    // Format the response
    $response = [];
    foreach ($kelas_list as $kelas) {
        $response[] = [
            'id' => $kelas['id'],
            'nama_kelas' => $kelas['nama_kelas']
        ];
    }
    
    echo json_encode($response);
    
} catch (Exception $e) {
    echo json_encode([]);
}
?>
