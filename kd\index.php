<?php
require_once __DIR__ . '/../middleware/auth.php';
checkGuruAccess();
require_once '../template/header.php';
require_once '../config/database.php';
require_once '../models/KompetensiDasar.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Guru.php';
require_once '../models/TahunAjaran.php';
require_once '../models/PeriodeAktif.php';

// Get teacher ID from logged in user
$guru = new Guru();
$stmt = $guru->getByUserId($_SESSION['user_id']);
if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $guru_id = $row['id'];
} else {
    die("Data guru tidak ditemukan");
}

// Get active period
$periodeAktif = new PeriodeAktif();
$periode_active = $periodeAktif->getActive();
$current_semester = $periode_active ? $periodeAktif->semester : '1';
$current_tahun_ajaran = $periode_active ? $periodeAktif->tahun_ajaran : '';

// Initialize models
$kd = new KompetensiDasar();
$mapel = new MataPelajaran();
$tahunAjaran = new TahunAjaran();

// Get filter parameters
$filter_mapel = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : '';
$filter_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$filter_semester = isset($_GET['semester']) ? $_GET['semester'] : $current_semester;
$filter_tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : $current_tahun_ajaran;

// Get teacher's subjects - try both methods for compatibility
$mapel_list_array = [];

// Method 1: Try mapel_guru table (direct assignment)
try {
    $mapel_direct = $mapel->getMapelByGuru($guru_id);
    if (!empty($mapel_direct)) {
        $mapel_list_array = $mapel_direct;
    }
} catch (Exception $e) {
    // Continue to method 2
}

// Method 2: Try jadwal_pelajaran table (schedule-based) if no direct assignments
if (empty($mapel_list_array)) {
    try {
        $mapel_schedule = $mapel->getMapelByGuruId($guru_id);
        while ($row = $mapel_schedule->fetch(PDO::FETCH_ASSOC)) {
            $mapel_list_array[] = $row;
        }
    } catch (Exception $e) {
        // Log error or handle as needed
    }
}

// Convert array back to statement-like object for compatibility
$database = new Database();
$conn = $database->getConnection();
if (!empty($mapel_list_array)) {
    $mapel_ids = array_column($mapel_list_array, 'id');
    $placeholders = str_repeat('?,', count($mapel_ids) - 1) . '?';
    $query = "SELECT * FROM mata_pelajaran WHERE id IN ($placeholders) ORDER BY nama_mapel ASC";
    $mapel_list = $conn->prepare($query);
    $mapel_list->execute($mapel_ids);
} else {
    // Return empty result set
    $query = "SELECT * FROM mata_pelajaran WHERE 1=0";
    $mapel_list = $conn->prepare($query);
    $mapel_list->execute();
}

$tahun_ajaran_list = $tahunAjaran->getAll();

// Get teacher's classes
$kelas_list = $kd->getKelasByGuruId($guru_id, $filter_mapel);

// Get KD data with filters
$kd_result = $kd->getAllByGuruId($guru_id, $filter_mapel, $filter_kelas, $filter_semester, $filter_tahun_ajaran);

// Handle success/error messages
$success_msg = isset($_GET['success']) ? "Data berhasil disimpan" : "";
$error_msg = isset($_GET['error']) ? "Terjadi kesalahan" : "";

// Check if teacher has any subjects assigned
$has_subjects = !empty($mapel_list_array);
if (!$has_subjects) {
    $error_msg = "Anda belum memiliki mata pelajaran yang ditugaskan. Silakan hubungi administrator untuk menambahkan penugasan mata pelajaran.";
}
?>

<div class="row">
    <div class="col-md-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3 mb-0">Kompetensi Dasar</h1>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="/absen/">Dashboard</a></li>
                    <li class="breadcrumb-item active">Kompetensi Dasar</li>
                </ol>
            </nav>
        </div>
        <?php if ($success_msg): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?= $success_msg ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_msg): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?= $error_msg ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Daftar Kompetensi Dasar</h5>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah KD
                    </a>
                </div>
                <div class="card-body">
                    <!-- Filter Form -->
                    <form method="GET" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                                <select class="form-select" id="mapel_id" name="mapel_id" onchange="loadKelas()">
                                    <option value="">Semua Mata Pelajaran</option>
                                    <?php
                                    $mapel_list->execute(); // Reset the statement
                                    while ($mapel_row = $mapel_list->fetch(PDO::FETCH_ASSOC)):
                                        $selected = ($mapel_row['id'] == $filter_mapel) ? 'selected' : '';
                                    ?>
                                        <option value="<?= $mapel_row['id'] ?>" <?= $selected ?>>
                                            <?= htmlspecialchars($mapel_row['nama_mapel']) ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label for="kelas_id" class="form-label">Kelas</label>
                                <select class="form-select" id="kelas_id" name="kelas_id">
                                    <option value="">Semua Kelas</option>
                                    <?php foreach ($kelas_list as $kelas_row):
                                        $selected = ($kelas_row['id'] == $filter_kelas) ? 'selected' : '';
                                    ?>
                                        <option value="<?= $kelas_row['id'] ?>" <?= $selected ?>>
                                            <?= htmlspecialchars($kelas_row['nama_kelas']) ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="semester" class="form-label">Semester</label>
                                <select class="form-select" id="semester" name="semester">
                                    <option value="1" <?= ($filter_semester == '1') ? 'selected' : '' ?>>Semester 1</option>
                                    <option value="2" <?= ($filter_semester == '2') ? 'selected' : '' ?>>Semester 2</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                                <select class="form-select" id="tahun_ajaran" name="tahun_ajaran">
                                    <?php while ($ta_row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)):
                                        $selected = ($ta_row['tahun_ajaran'] == $filter_tahun_ajaran) ? 'selected' : '';
                                    ?>
                                        <option value="<?= $ta_row['tahun_ajaran'] ?>" <?= $selected ?>>
                                            <?= htmlspecialchars($ta_row['tahun_ajaran']) ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-outline-primary">
                                        <i class="fas fa-filter"></i> Filter
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>

                    <!-- Data Table -->
                    <div class="table-responsive">
                        <table class="table table-striped table-hover" id="tableKD">
                            <thead>
                                <tr>
                                    <th>No</th>
                                    <th>Kode KD</th>
                                    <th>Mata Pelajaran</th>
                                    <th>Kelas</th>
                                    <th>Deskripsi</th>
                                    <th>Tema/Subtema</th>
                                    <th>Materi Pokok</th>
                                    <th>Semester</th>
                                    <th>Tahun Ajaran</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php
                                $no = 1;
                                $hasData = false;
                                while ($row = $kd_result->fetch(PDO::FETCH_ASSOC)) {
                                    $hasData = true;
                                ?>
                                <tr>
                                    <td><?= $no++ ?></td>
                                    <td><strong><?= htmlspecialchars($row['kode_kd']) ?></strong></td>
                                    <td><?= htmlspecialchars($row['nama_mapel']) ?></td>
                                    <td>
                                        <?php if ($row['nama_kelas']): ?>
                                            <span class="badge bg-info"><?= htmlspecialchars($row['nama_kelas']) ?></span>
                                        <?php else: ?>
                                            <span class="text-muted">Semua Kelas</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="text-truncate" style="max-width: 200px;" title="<?= htmlspecialchars($row['deskripsi_kd']) ?>">
                                            <?= htmlspecialchars($row['deskripsi_kd']) ?>
                                        </div>
                                    </td>
                                    <td>
                                        <?php if (!empty($row['tema_subtema'])): ?>
                                            <div class="text-truncate" style="max-width: 150px;" title="<?= htmlspecialchars($row['tema_subtema']) ?>">
                                                <i class="fas fa-bookmark text-primary"></i>
                                                <?= htmlspecialchars($row['tema_subtema']) ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <?php if (!empty($row['materi_pokok'])): ?>
                                            <div class="text-truncate" style="max-width: 150px;" title="<?= htmlspecialchars($row['materi_pokok']) ?>">
                                                <i class="fas fa-book-open text-success"></i>
                                                <?= htmlspecialchars($row['materi_pokok']) ?>
                                            </div>
                                        <?php else: ?>
                                            <span class="text-muted">-</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <span class="badge bg-<?= $row['semester'] == '1' ? 'primary' : 'success' ?>">
                                            Semester <?= $row['semester'] ?>
                                        </span>
                                    </td>
                                    <td><?= htmlspecialchars($row['tahun_ajaran']) ?></td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?= $row['id'] ?>" class="btn btn-info btn-sm" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="edit.php?id=<?= $row['id'] ?>" class="btn btn-warning btn-sm" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="javascript:void(0)" onclick="confirmDelete(<?= $row['id'] ?>)"
                                               class="btn btn-danger btn-sm" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                                <?php } ?>
                            </tbody>
                        </table>
                        <?php if (!$hasData): ?>
                            <div class="text-center py-4">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">Belum ada data Kompetensi Dasar</p>
                                <a href="create.php" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Tambah KD Pertama
                                </a>
                            </div>
                        <?php endif; ?>
                    </div>
            </div>
        </div>
    </div>
</div>

<script>
function confirmDelete(id) {
    if (confirm('Apakah Anda yakin ingin menghapus Kompetensi Dasar ini?')) {
        window.location.href = 'delete.php?id=' + id;
    }
}

function loadKelas() {
    const mapelId = document.getElementById('mapel_id').value;
    const kelasSelect = document.getElementById('kelas_id');

    // Clear existing options except "Semua Kelas"
    kelasSelect.innerHTML = '<option value="">Semua Kelas</option>';

    if (mapelId) {
        // Create AJAX request to get classes for selected subject
        fetch('get_kelas.php?mapel_id=' + mapelId + '&guru_id=<?= $guru_id ?>')
            .then(response => response.json())
            .then(data => {
                data.forEach(kelas => {
                    const option = document.createElement('option');
                    option.value = kelas.id;
                    option.textContent = kelas.nama_kelas;
                    kelasSelect.appendChild(option);
                });
            })
            .catch(error => {
                console.error('Error loading classes:', error);
            });
    }
}

$(document).ready(function() {
    $('#tableKD').DataTable({
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
        },
        "pageLength": 25,
        "order": [[ 1, "asc" ]],
        "columnDefs": [
            { "orderable": false, "targets": -1 } // Disable sorting on action column
        ]
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
