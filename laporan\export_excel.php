<?php
require '../vendor/autoload.php';
require_once '../models/Absensi.php';
require_once '../models/Kelas.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
// Remove header.php inclusion as it may output HTML
session_start();

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

// Prevent any unwanted output
ob_clean();

try {
    // Get active period
    $periode = new PeriodeAktif();
    if (!$periode->getActive()) {
        die("Tidak ada periode aktif saat ini.");
    }

    $absensi = new Absensi();
    $kelas = new Kelas();
    $jadwal = new JadwalPelajaran();
    $user = new User();

    // Get selected class
    $selected_kelas = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
    $selected_mapel = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : '';
    $selected_tahun_ajaran = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : '';

    // For teachers, verify they have access to this subject
    if ($_SESSION['role'] === 'guru') {
        $guru_id = $user->getGuruId($_SESSION['user_id']);
        $authorized = false;
        $stmt = $jadwal->getByMapelAndGuru($selected_mapel, $guru_id);
        if ($stmt->rowCount() > 0) {
            $authorized = true;
        } else {
            die("Anda tidak memiliki akses untuk mengekspor laporan mata pelajaran ini!");
        }
    }

    // Get class name if selected
    $kelas_name = '';
    if ($selected_kelas) {
        $kelas->id = $selected_kelas;
        if ($kelas->getOne()) {
            $kelas_name = $kelas->nama_kelas;
        }
    }

    // Get report data
    $report_data = $absensi->getReportBySemester($selected_kelas, $selected_mapel, $selected_tahun_ajaran);

    // Create new Spreadsheet object
    $spreadsheet = new Spreadsheet();

    // Set document properties
    $spreadsheet->getProperties()
        ->setCreator('Sistem Absensi')
        ->setLastModifiedBy('Sistem Absensi')
        ->setTitle('Laporan Absensi')
        ->setSubject('Laporan Absensi Siswa')
        ->setDescription('Laporan Absensi Siswa ' . ($kelas_name ? $kelas_name : 'Semua Kelas'));

    // Get active sheet
    $sheet = $spreadsheet->getActiveSheet();

    // Set header
    $sheet->setCellValue('A1', 'LAPORAN ABSENSI SISWA');
    $sheet->setCellValue('A2', 'Semester ' . $periode->semester . ' - ' . $periode->tahun_ajaran);
    if ($kelas_name) {
        $sheet->setCellValue('A3', 'Kelas: ' . $kelas_name);
    }
    $sheet->setCellValue('A4', 'Periode: ' . date('d/m/Y', strtotime($periode->tanggal_mulai)) . ' - ' . date('d/m/Y', strtotime($periode->tanggal_selesai)));

    // Merge cells for header
    $sheet->mergeCells('A1:J1');
    $sheet->mergeCells('A2:J2');
    $sheet->mergeCells('A3:J3');
    $sheet->mergeCells('A4:J4');

    // Set header style
    $headerStyle = [
        'font' => ['bold' => true],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ];
    $sheet->getStyle('A1:J4')->applyFromArray($headerStyle);

    // Add table headers
    $tableHeaders = ['No', 'NIS', 'Nama Siswa', 'Kelas', 'Total Pertemuan', 'Hadir', 'Sakit', 'Izin', 'Alpha', '% Kehadiran'];
    $sheet->fromArray($tableHeaders, NULL, 'A6');

    // Style the table headers
    $sheet->getStyle('A6:J6')->applyFromArray([
        'font' => ['bold' => true],
        'fill' => [
            'fillType' => Fill::FILL_SOLID,
            'startColor' => ['rgb' => 'E2EFDA']
        ],
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN
            ]
        ],
        'alignment' => [
            'horizontal' => Alignment::HORIZONTAL_CENTER,
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ]);

    // Add data
    $row = 7;
    $no = 1;

    if ($report_data && $report_data->rowCount() > 0) {
        while ($data = $report_data->fetch(PDO::FETCH_ASSOC)) {
            // Calculate percentage
            $persentase = $data['total_pertemuan'] > 0 ? 
                ($data['hadir'] * 100.0) / $data['total_pertemuan'] : 0;

            $sheet->setCellValue('A' . $row, $no++);
            $sheet->setCellValue('B' . $row, $data['nis']);
            $sheet->setCellValue('C' . $row, $data['nama_siswa']);
            $sheet->setCellValue('D' . $row, $data['nama_kelas']);
            $sheet->setCellValue('E' . $row, $data['total_pertemuan']);
            $sheet->setCellValue('F' . $row, $data['hadir']);
            $sheet->setCellValue('G' . $row, $data['sakit']);
            $sheet->setCellValue('H' . $row, $data['izin']);
            $sheet->setCellValue('I' . $row, $data['alpha']);
            $sheet->setCellValue('J' . $row, number_format($persentase, 2) . '%');

            // Color code the percentage
            if ($persentase >= 90) {
                $sheet->getStyle('J' . $row)->getFont()->getColor()->setRGB('28a745');
            } elseif ($persentase >= 75) {
                $sheet->getStyle('J' . $row)->getFont()->getColor()->setRGB('ffc107');
            } else {
                $sheet->getStyle('J' . $row)->getFont()->getColor()->setRGB('dc3545');
            }

            $row++;
        }
    }

    // Style the data
    $dataRange = 'A7:J' . ($row - 1);
    $sheet->getStyle($dataRange)->applyFromArray([
        'borders' => [
            'allBorders' => [
                'borderStyle' => Border::BORDER_THIN
            ]
        ],
        'alignment' => [
            'vertical' => Alignment::VERTICAL_CENTER
        ]
    ]);

    // Center align specific columns
    $sheet->getStyle('A7:A' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $sheet->getStyle('E7:J' . ($row - 1))->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);

    // Add signature
    $row += 2;
    $sheet->setCellValue('H' . $row, date('d/m/Y'));
    $row++;
    $sheet->setCellValue('H' . $row, 'Wali Kelas');
    $row += 4;
    $sheet->setCellValue('H' . $row, '______________________');

    // Auto-size columns
    foreach (range('A', 'J') as $col) {
        $sheet->getColumnDimension($col)->setAutoSize(true);
    }

    // Create writer
    $writer = new Xlsx($spreadsheet);

    // Set filename
    $filename = 'Laporan_Absensi_';
    $filename .= $kelas_name ? $kelas_name . '_' : '';
    $filename .= date('Y-m-d_H-i-s') . '.xlsx';

    // Set headers for download
    header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    header('Content-Disposition: attachment;filename="' . $filename . '"');
    header('Cache-Control: max-age=0');

    // Output file
    $writer->save('php://output');
    exit();
} catch (Exception $e) {
    // Log error and show user-friendly message
    error_log('Excel Export Error: ' . $e->getMessage());
    die("Maaf, terjadi kesalahan saat mengekspor file. Silakan coba lagi.");
}
