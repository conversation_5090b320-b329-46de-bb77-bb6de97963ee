<?php
require_once '../config/database.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
session_start();

header('Content-Type: application/json');

try {
    if (!isset($_GET['kelas_id'])) {
        throw new Exception('Parameter kelas_id tidak ditemukan');
    }

    $kelas_id = $_GET['kelas_id'];
    $jadwal = new JadwalPelajaran();
    
    // If user is guru, only show their subjects
    if ($_SESSION['role'] === 'guru') {
        $user = new User();
        $guru_id = $user->getGuruId($_SESSION['user_id']);
        $mapel_list = $jadwal->getMapelByKelasAndGuru($kelas_id, $guru_id);
    } else {
        $mapel_list = $jadwal->getMapelByKelas($kelas_id);
    }

    $result = array();
    while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)) {
        $result[] = array(
            'id' => $row['id'],
            'nama_mapel' => $row['nama_mapel']
        );
    }

    echo json_encode($result);

} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}
