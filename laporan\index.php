<?php
require_once '../config/database.php';
require_once '../models/Kelas.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Absensi.php';
require_once '../models/TahunAjaran.php';
require_once '../models/JadwalPelajaran.php';
require_once '../models/User.php';
require_once '../models/Siswa.php';
require_once '../middleware/auth.php';

// Check admin access
checkAdminAccess();

// Include header after auth check
require_once '../template/header.php';

// Initialize models
$kelas = new Kelas();
$mapel = new MataPelajaran();
$absensi = new Absensi();
$tahunAjaran = new TahunAjaran();
$jadwal = new JadwalPelajaran();
$user = new User();
$siswa = new Siswa();

// Get filter parameters
$kelas_id = isset($_GET['kelas_id']) ? $_GET['kelas_id'] : '';
$mapel_id = isset($_GET['mapel_id']) ? $_GET['mapel_id'] : '';
$tahun_ajaran_id = isset($_GET['tahun_ajaran']) ? $_GET['tahun_ajaran'] : '';

// Get lists for dropdowns
$kelas_list = $kelas->getAll();
$tahun_ajaran_list = $tahunAjaran->getAll();

// Get report data if filters are set
$report_data = null;
$siswa_list = null;
if ($kelas_id && $mapel_id && $tahun_ajaran_id) {
    // For teachers, verify they have access to this subject
    if ($_SESSION['role'] === 'guru') {
        $guru_id = $user->getGuruId($_SESSION['user_id']);
        $authorized = false;
        $stmt = $jadwal->getByMapelAndGuru($mapel_id, $guru_id);
        if ($stmt->rowCount() > 0) {
            $authorized = true;
            $report_data = $absensi->getReportBySemester($kelas_id, $mapel_id, $tahun_ajaran_id);
            $siswa_list = $siswa->getByKelas($kelas_id);
        } else {
            echo "<script>alert('Anda tidak memiliki akses untuk melihat laporan mata pelajaran ini!'); window.location.href='index.php';</script>";
            exit;
        }
    } else {
        $report_data = $absensi->getReportBySemester($kelas_id, $mapel_id, $tahun_ajaran_id);
        $siswa_list = $siswa->getByKelas($kelas_id);
    }
}
?>

<div class="row mb-3">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Rekap Absensi Semester</h5>
                <?php if ($siswa_list && $siswa_list->rowCount() > 0): ?>
                <div>
                    <a href="export_pdf.php?<?php echo http_build_query($_GET); ?>" class="btn btn-danger" target="_blank">
                        <i class="fas fa-file-pdf"></i> Export PDF
                    </a>
                    <a href="export_excel.php?<?php echo http_build_query($_GET); ?>" class="btn btn-success">
                        <i class="fas fa-file-excel"></i> Export Excel
                    </a>
                </div>
                <?php endif; ?>
            </div>

            <div class="card-body">
                <!-- Filter Form -->
                <form method="get" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <label for="kelas_id" class="form-label">Kelas</label>
                            <select name="kelas_id" id="kelas_id" class="form-select" required>
                                <option value="">Pilih Kelas</option>
                                <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                    <option value="<?php echo $row['id']; ?>" <?php echo $kelas_id == $row['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="mapel_id" class="form-label">Mata Pelajaran</label>
                            <select name="mapel_id" id="mapel_id" class="form-select" required>
                                <option value="">Pilih Mata Pelajaran</option>
                                <?php 
                                if ($kelas_id) {
                                    if ($_SESSION['role'] === 'guru') {
                                        $guru_id = $user->getGuruId($_SESSION['user_id']);
                                        $mapel_list = $jadwal->getMapelByKelasAndGuru($kelas_id, $guru_id);
                                    } else {
                                        $mapel_list = $jadwal->getMapelByKelas($kelas_id);
                                    }
                                    while ($row = $mapel_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?php echo $row['id']; ?>" <?php echo $mapel_id == $row['id'] ? 'selected' : ''; ?>>
                                            <?php echo htmlspecialchars($row['nama_mapel']); ?>
                                        </option>
                                    <?php endwhile;
                                }
                                ?>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                            <select name="tahun_ajaran" id="tahun_ajaran" class="form-select" required>
                                <option value="">Pilih Tahun Ajaran</option>
                                <?php while ($row = $tahun_ajaran_list->fetch(PDO::FETCH_ASSOC)): ?>
                                    <option value="<?php echo $row['id']; ?>" <?php echo $tahun_ajaran_id == $row['id'] ? 'selected' : ''; ?>>
                                        <?php echo htmlspecialchars($row['tahun_ajaran']); ?>
                                    </option>
                                <?php endwhile; ?>
                            </select>
                        </div>
                        <div class="col-md-3 d-flex align-items-end">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Tampilkan Laporan
                            </button>
                        </div>
                    </div>
                </form>

                <?php if ($siswa_list): ?>
                <div class="table-responsive">
                    <table class="table table-bordered table-striped" id="reportTable">
                        <thead class="table-primary">
                            <tr>
                                <th>No</th>
                                <th>NIS</th>
                                <th>Nama Siswa</th>
                                <th>Kelas</th>
                                <th>Total Pertemuan</th>
                                <th>Hadir</th>
                                <th>Sakit</th>
                                <th>Izin</th>
                                <th>Alpha</th>
                                <th>Persentase Kehadiran</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php 
                            if ($siswa_list->rowCount() > 0) :
                                $no = 1;
                                $attendance_data = [];
                                if ($report_data) {
                                    while ($row = $report_data->fetch(PDO::FETCH_ASSOC)) {
                                        $attendance_data[$row['nis']] = $row;
                                    }
                                }
                                
                                while ($siswa = $siswa_list->fetch(PDO::FETCH_ASSOC)) :
                                    $attendance = isset($attendance_data[$siswa['nis']]) ? $attendance_data[$siswa['nis']] : [
                                        'total_pertemuan' => 0,
                                        'hadir' => 0,
                                        'sakit' => 0,
                                        'izin' => 0,
                                        'alpha' => 0
                                    ];
                                    
                                    $persentase = $attendance['total_pertemuan'] > 0 ? 
                                        ($attendance['hadir'] * 100.0) / $attendance['total_pertemuan'] : 0;
                                    
                                    $percent_class = '';
                                    if ($persentase >= 90) {
                                        $percent_class = 'text-success';
                                    } elseif ($persentase >= 75) {
                                        $percent_class = 'text-warning';
                                    } else {
                                        $percent_class = 'text-danger';
                                    }
                            ?>
                            <tr>
                                <td><?php echo $no++; ?></td>
                                <td><?php echo htmlspecialchars($siswa['nis']); ?></td>
                                <td><?php echo htmlspecialchars($siswa['nama_siswa']); ?></td>
                                <td><?php echo htmlspecialchars($siswa['nama_kelas']); ?></td>
                                <td><?php echo $attendance['total_pertemuan']; ?></td>
                                <td><span class="badge bg-success"><?php echo $attendance['hadir']; ?></span></td>
                                <td><span class="badge bg-warning"><?php echo $attendance['sakit']; ?></span></td>
                                <td><span class="badge bg-info"><?php echo $attendance['izin']; ?></span></td>
                                <td><span class="badge bg-danger"><?php echo $attendance['alpha']; ?></span></td>
                                <td class="<?php echo $percent_class; ?> fw-bold">
                                    <?php echo number_format($persentase, 2); ?>%
                                </td>
                            </tr>
                            <?php 
                                endwhile;
                            else:
                            ?>
                            <tr>
                                <td colspan="10" class="text-center">Tidak ada data siswa di kelas ini</td>
                            </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Keterangan</h6>
                            </div>
                            <div class="card-body">
                                <ul class="list-unstyled mb-0">
                                    <li><span class="text-success">H</span> = Hadir</li>
                                    <li><span class="text-warning">S</span> = Sakit</li>
                                    <li><span class="text-info">I</span> = Izin</li>
                                    <li><span class="text-danger">A</span> = Alpha</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="card-title mb-0">Detail Laporan</h6>
                            </div>
                            <div class="card-body">
                                <table class="table table-sm">
                                    <tr>
                                        <th>Kelas</th>
                                        <td>: <?php 
                                            if ($kelas_id) {
                                                $kelas = new Kelas();
                                                $kelas->id = $kelas_id;
                                                $kelas->getOne();
                                                echo htmlspecialchars($kelas->nama_kelas);
                                            } else {
                                                echo '-';
                                            }
                                        ?></td>
                                    </tr>
                                    <tr>
                                        <th>Mata Pelajaran</th>
                                        <td>: <?php 
                                            if ($mapel_id) {
                                                $mapel->id = $mapel_id;
                                                $mapel->getOne();
                                                echo htmlspecialchars($mapel->nama_mapel);
                                            } else {
                                                echo '-';
                                            }
                                        ?></td>
                                    </tr>
                                    <tr>
                                        <th>Tahun Ajaran</th>
                                        <td>: <?php 
                                            if ($tahun_ajaran_id) {
                                                $ta_stmt = $tahunAjaran->getById($tahun_ajaran_id);
                                                $ta_data = $ta_stmt->fetch(PDO::FETCH_ASSOC);
                                                echo htmlspecialchars($ta_data['tahun_ajaran']);
                                            } else {
                                                echo '-';
                                            }
                                        ?></td>
                                    </tr>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Initialize DataTable
    if ($('#reportTable').length) {
        $('#reportTable').DataTable({
            "responsive": true,
            "order": [[0, "asc"]],
            "pageLength": 25,
            "language": {
                "url": "//cdn.datatables.net/plug-ins/1.10.24/i18n/Indonesian.json"
            }
        });
    }

    // Dynamic Mata Pelajaran dropdown based on Kelas selection
    $('#kelas_id').change(function() {
        var kelas_id = $(this).val();
        var mapel_select = $('#mapel_id');
        
        // Clear current options
        mapel_select.html('<option value="">Pilih Mata Pelajaran</option>');
        
        if (kelas_id) {
            // Show loading
            mapel_select.prop('disabled', true);
            
            // Get mata pelajaran for selected kelas
            $.ajax({
                url: 'get_mapel.php',
                type: 'GET',
                data: { kelas_id: kelas_id },
                dataType: 'json',
                success: function(data) {
                    // Add new options
                    data.forEach(function(item) {
                        mapel_select.append($('<option>', {
                            value: item.id,
                            text: item.nama_mapel
                        }));
                    });
                    
                    // Enable select
                    mapel_select.prop('disabled', false);
                },
                error: function() {
                    alert('Terjadi kesalahan saat memuat data mata pelajaran');
                    mapel_select.prop('disabled', false);
                }
            });
        }
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
