<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/ClassroomMigration.php';
require_once __DIR__ . '/../models/RuangKelas.php';
require_once __DIR__ . '/../models/Absensi.php';
require_once __DIR__ . '/../models/Tugas.php';
require_once __DIR__ . '/../models/Nilai.php';

// Only admin can access this module
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

$migrationModel = new ClassroomMigration();
$ruangKelasModel = new RuangKelas();
$absensiModel = new Absensi();
$tugasModel = new Tugas();
$nilaiModel = new Nilai();

$test_results = [];
$overall_status = 'success';

// Test 1: Check if all required tables exist
function testTableStructure($conn) {
    $required_tables = [
        'ruang_kelas' => ['id', 'nama_ruang_kelas', 'semester', 'tahun_ajaran'],
        'siswa_ruang_kelas' => ['id', 'siswa_id', 'ruang_kelas_id', 'status'],
        'classroom_migration_log' => ['id', 'migration_step', 'status']
    ];
    
    $results = [];
    foreach ($required_tables as $table => $required_columns) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                // Check columns
                $columns_stmt = $conn->query("SHOW COLUMNS FROM $table");
                $existing_columns = [];
                while ($col = $columns_stmt->fetch(PDO::FETCH_ASSOC)) {
                    $existing_columns[] = $col['Field'];
                }
                
                $missing_columns = array_diff($required_columns, $existing_columns);
                if (empty($missing_columns)) {
                    $results[$table] = ['status' => 'success', 'message' => 'Table exists with all required columns'];
                } else {
                    $results[$table] = ['status' => 'warning', 'message' => 'Missing columns: ' . implode(', ', $missing_columns)];
                }
            } else {
                $results[$table] = ['status' => 'error', 'message' => 'Table does not exist'];
            }
        } catch (Exception $e) {
            $results[$table] = ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    return $results;
}

// Test 2: Check if ruang_kelas_id columns exist in related tables
function testColumnAdditions($conn) {
    $tables_with_ruang_kelas_id = [
        'absensi', 'tugas', 'nilai', 'nilai_sikap', 'nilai_tugas', 
        'detail_absensi', 'jadwal_pelajaran', 'tugas_tambahan'
    ];
    
    $results = [];
    foreach ($tables_with_ruang_kelas_id as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $columns_stmt = $conn->query("SHOW COLUMNS FROM $table LIKE 'ruang_kelas_id'");
                if ($columns_stmt->rowCount() > 0) {
                    $results[$table] = ['status' => 'success', 'message' => 'ruang_kelas_id column exists'];
                } else {
                    $results[$table] = ['status' => 'warning', 'message' => 'ruang_kelas_id column missing'];
                }
            } else {
                $results[$table] = ['status' => 'info', 'message' => 'Table does not exist (optional)'];
            }
        } catch (Exception $e) {
            $results[$table] = ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    return $results;
}

// Test 3: Check data integrity
function testDataIntegrity($conn) {
    $results = [];
    
    try {
        // Check if there are classrooms
        $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas");
        $classroom_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($classroom_count > 0) {
            $results['classrooms'] = ['status' => 'success', 'message' => "$classroom_count classrooms found"];
            
            // Check student assignments
            $stmt = $conn->query("SELECT COUNT(*) as count FROM siswa_ruang_kelas WHERE status = 'aktif'");
            $assignment_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $results['student_assignments'] = ['status' => 'success', 'message' => "$assignment_count active student assignments"];
            
            // Check attendance records with classroom references
            $stmt = $conn->query("SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NOT NULL");
            $attendance_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $results['attendance_migration'] = ['status' => 'success', 'message' => "$attendance_count attendance records linked to classrooms"];
            
            // Check assignment records with classroom references
            $stmt = $conn->query("SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NOT NULL");
            $assignment_migration_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            $results['assignment_migration'] = ['status' => 'success', 'message' => "$assignment_migration_count assignment records linked to classrooms"];
            
        } else {
            $results['classrooms'] = ['status' => 'warning', 'message' => 'No classrooms found - migration may not be complete'];
        }
        
    } catch (Exception $e) {
        $results['data_integrity'] = ['status' => 'error', 'message' => $e->getMessage()];
    }
    
    return $results;
}

// Test 4: Test model functionality
function testModelFunctionality($ruangKelasModel, $absensiModel, $tugasModel, $nilaiModel) {
    $results = [];
    
    try {
        // Test RuangKelas model
        $stmt = $ruangKelasModel->getAll();
        $results['ruang_kelas_model'] = ['status' => 'success', 'message' => 'RuangKelas model working'];
        
        // Test classroom system availability in other models
        if ($absensiModel->isClassroomSystemAvailable()) {
            $results['absensi_classroom_support'] = ['status' => 'success', 'message' => 'Absensi model supports classroom system'];
        } else {
            $results['absensi_classroom_support'] = ['status' => 'warning', 'message' => 'Absensi model does not detect classroom system'];
        }
        
        if ($tugasModel->isClassroomSystemAvailable()) {
            $results['tugas_classroom_support'] = ['status' => 'success', 'message' => 'Tugas model supports classroom system'];
        } else {
            $results['tugas_classroom_support'] = ['status' => 'warning', 'message' => 'Tugas model does not detect classroom system'];
        }
        
        if ($nilaiModel->isClassroomSystemAvailable()) {
            $results['nilai_classroom_support'] = ['status' => 'success', 'message' => 'Nilai model supports classroom system'];
        } else {
            $results['nilai_classroom_support'] = ['status' => 'warning', 'message' => 'Nilai model does not detect classroom system'];
        }
        
    } catch (Exception $e) {
        $results['model_functionality'] = ['status' => 'error', 'message' => $e->getMessage()];
    }
    
    return $results;
}

// Run all tests
$database = new Database();
$conn = $database->getConnection();

$test_results['Table Structure'] = testTableStructure($conn);
$test_results['Column Additions'] = testColumnAdditions($conn);
$test_results['Data Integrity'] = testDataIntegrity($conn);
$test_results['Model Functionality'] = testModelFunctionality($ruangKelasModel, $absensiModel, $tugasModel, $nilaiModel);

// Determine overall status
foreach ($test_results as $category => $tests) {
    foreach ($tests as $test => $result) {
        if ($result['status'] === 'error') {
            $overall_status = 'error';
            break 2;
        } elseif ($result['status'] === 'warning' && $overall_status !== 'error') {
            $overall_status = 'warning';
        }
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-check-circle"></i> Classroom System Test & Validation</h2>
        <a href="classroom_migration.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Migration
        </a>
    </div>

    <!-- Overall Status -->
    <div class="card mb-4">
        <div class="card-header <?= $overall_status === 'success' ? 'bg-success' : ($overall_status === 'warning' ? 'bg-warning' : 'bg-danger') ?> text-white">
            <h5 class="card-title mb-0">
                <?php if ($overall_status === 'success'): ?>
                    <i class="fas fa-check-circle"></i> System Status: All Tests Passed
                <?php elseif ($overall_status === 'warning'): ?>
                    <i class="fas fa-exclamation-triangle"></i> System Status: Some Issues Found
                <?php else: ?>
                    <i class="fas fa-times-circle"></i> System Status: Critical Issues Found
                <?php endif; ?>
            </h5>
        </div>
        <div class="card-body">
            <?php if ($overall_status === 'success'): ?>
                <p class="mb-0">The classroom system is fully functional and ready for use.</p>
            <?php elseif ($overall_status === 'warning'): ?>
                <p class="mb-0">The classroom system is mostly functional but some components need attention.</p>
            <?php else: ?>
                <p class="mb-0">The classroom system has critical issues that need to be resolved before use.</p>
            <?php endif; ?>
        </div>
    </div>

    <!-- Test Results -->
    <?php foreach ($test_results as $category => $tests): ?>
        <div class="card mb-4">
            <div class="card-header">
                <h5 class="card-title mb-0"><?= htmlspecialchars($category) ?></h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Test</th>
                                <th>Status</th>
                                <th>Message</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($tests as $test => $result): ?>
                                <tr>
                                    <td><?= htmlspecialchars($test) ?></td>
                                    <td>
                                        <?php
                                        $badge_class = '';
                                        $icon = '';
                                        switch ($result['status']) {
                                            case 'success':
                                                $badge_class = 'bg-success';
                                                $icon = 'check-circle';
                                                break;
                                            case 'warning':
                                                $badge_class = 'bg-warning';
                                                $icon = 'exclamation-triangle';
                                                break;
                                            case 'error':
                                                $badge_class = 'bg-danger';
                                                $icon = 'times-circle';
                                                break;
                                            case 'info':
                                                $badge_class = 'bg-info';
                                                $icon = 'info-circle';
                                                break;
                                        }
                                        ?>
                                        <span class="badge <?= $badge_class ?>">
                                            <i class="fas fa-<?= $icon ?>"></i>
                                            <?= ucfirst($result['status']) ?>
                                        </span>
                                    </td>
                                    <td><?= htmlspecialchars($result['message']) ?></td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    <?php endforeach; ?>

    <!-- Recommendations -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="fas fa-lightbulb"></i> Recommendations</h5>
        </div>
        <div class="card-body">
            <?php if ($overall_status === 'success'): ?>
                <div class="alert alert-success">
                    <h6>System Ready!</h6>
                    <ul class="mb-0">
                        <li>Start creating classrooms for the current academic period</li>
                        <li>Assign students to appropriate classrooms</li>
                        <li>Begin using the updated attendance and assignment modules</li>
                        <li>Monitor system performance and data consistency</li>
                    </ul>
                </div>
            <?php elseif ($overall_status === 'warning'): ?>
                <div class="alert alert-warning">
                    <h6>Action Required:</h6>
                    <ul class="mb-0">
                        <li>Review the warnings above and address missing components</li>
                        <li>Run the migration process if not completed</li>
                        <li>Verify data integrity before full deployment</li>
                        <li>Test core functionality with sample data</li>
                    </ul>
                </div>
            <?php else: ?>
                <div class="alert alert-danger">
                    <h6>Critical Issues Found:</h6>
                    <ul class="mb-0">
                        <li>Do not use the system until all errors are resolved</li>
                        <li>Run the table creation process manually if needed</li>
                        <li>Check database permissions and connectivity</li>
                        <li>Contact system administrator for assistance</li>
                    </ul>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
