<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../template/header.php';

// Hanya admin yang dapat mengakses halaman ini
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Dapatkan koneksi database
        $database = new Database();
        $pdo = $database->getConnection();

        // Periksa apakah ada tabel yang dipilih
        if (!isset($_POST['tables']) || empty($_POST['tables'])) {
            throw new Exception("Silakan pilih minimal satu tabel untuk dibersihkan.");
        }

        // Nonaktifkan periksa kunci asing
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");

        $cleaned_tables = [];
        $failed_tables = [];
        $skipped_tables = [];

        // Tangani tabel users/user secara khusus
        $selected_tables = $_POST['tables'];
        $has_user_table = in_array('users', $selected_tables) || in_array('user', $selected_tables);

        if ($has_user_table) {
            try {
                // Periksa apakah tabel pengguna ada
                $stmt = $pdo->query("SHOW TABLES LIKE 'users'");
                $users_exists = $stmt->fetch() !== false;
                
                if ($users_exists) {
                    // Hapus semua pengguna non-admin
                    $stmt = $pdo->prepare("DELETE FROM users WHERE role != 'admin'");
                    $stmt->execute();
                    $rows_deleted = $stmt->rowCount();
                    if ($rows_deleted > 0) {
                        $cleaned_tables[] = "users (dihapus {$rows_deleted} pengguna non-admin)";
                    } else {
                        $skipped_tables[] = "users (tidak ada pengguna non-admin)";
                    }
                    
                    // Hapus dari array tabel yang dipilih
                    $selected_tables = array_diff($selected_tables, ['users']);
                } else {
                    // Periksa apakah tabel 'user' ada sebagai gantinya
                    $stmt = $pdo->query("SHOW TABLES LIKE 'user'");
                    $user_exists = $stmt->fetch() !== false;
                    
                    if ($user_exists) {
                        $stmt = $pdo->prepare("DELETE FROM user WHERE role != 'admin'");
                        $stmt->execute();
                        $rows_deleted = $stmt->rowCount();
                        if ($rows_deleted > 0) {
                            $cleaned_tables[] = "user (dihapus {$rows_deleted} pengguna non-admin)";
                        } else {
                            $skipped_tables[] = "user (tidak ada pengguna non-admin)";
                        }
                        
                        // Hapus dari array tabel yang dipilih
                        $selected_tables = array_diff($selected_tables, ['user']);
                    }
                }
            } catch (PDOException $e) {
                $failed_tables[] = "users/user: " . $e->getMessage();
            }
        }

        // Bersihkan tabel yang dipilih
        foreach ($selected_tables as $table) {
            try {
                $stmt = $pdo->prepare("TRUNCATE TABLE `$table`");
                $stmt->execute();
                $cleaned_tables[] = $table;
            } catch (PDOException $e) {
                $failed_tables[] = "$table: " . $e->getMessage();
            }
        }

        // Aktifkan kembali periksa kunci asing
        $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");

        // Siapkan pesan hasil
        $messages = [];
        if (!empty($cleaned_tables)) {
            $messages[] = "Tabel yang berhasil dibersihkan: " . implode(", ", $cleaned_tables);
        }
        if (!empty($skipped_tables)) {
            $messages[] = "Tabel yang dilewati: " . implode(", ", $skipped_tables);
        }
        if (!empty($failed_tables)) {
            $messages[] = "Tabel yang gagal dibersihkan: " . implode(", ", $failed_tables);
        }

        $message = implode("\n", $messages);
        if (empty($message)) {
            $message = "Tidak ada tabel yang dibersihkan.";
        }

    } catch (PDOException $e) {
        error_log("Error di clean.php: " . $e->getMessage());
        $error = "Error membersihkan database: " . $e->getMessage();
        
        // Pastikan periksa kunci asing diaktifkan kembali bahkan jika terjadi kesalahan
        try {
            $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
        } catch (Exception $e) {
            error_log("Error mengaktifkan foreign key checks: " . $e->getMessage());
        }
    } catch (Exception $e) {
        error_log("Error di clean.php: " . $e->getMessage());
        $error = $e->getMessage();
    }
}

// Dapatkan daftar tabel dari database
try {
    $database = new Database();
    $pdo = $database->getConnection();
    $tables = [];
    $stmt = $pdo->query("SHOW TABLES");
    while ($row = $stmt->fetch(PDO::FETCH_NUM)) {
        $tables[] = $row[0];
    }
} catch (PDOException $e) {
    error_log("Error mengambil daftar tabel: " . $e->getMessage());
    $error = "Error mengambil daftar tabel: " . $e->getMessage();
    $tables = [];
}

// Periksa pesan sesi
if (isset($_SESSION['success'])) {
    $message = $_SESSION['success'];
    unset($_SESSION['success']);
}
if (isset($_SESSION['error'])) {
    $error = $_SESSION['error'];
    unset($_SESSION['error']);
}
?>

<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">Bersihkan Database</h5>
                    <a href="index.php" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Kembali
                    </a>
                </div>
                <div class="card-body">
                    <?php if ($message): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($message)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <?php if ($error): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <?php echo nl2br(htmlspecialchars($error)); ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                    <?php endif; ?>

                    <form method="post" id="cleanForm">
                        <div class="mb-3">
                            <div class="d-flex justify-content-between align-items-center mb-2">
                                <h6>Pilih Tabel untuk Dibersihkan:</h6>
                                <div>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="selectAllTables(true)">Pilih Semua</button>
                                    <button type="button" class="btn btn-sm btn-secondary" onclick="selectAllTables(false)">Batalkan Semua</button>
                                </div>
                            </div>
                            <div class="table-selection border p-3" style="max-height: 300px; overflow-y: auto;">
                                <?php foreach ($tables as $table): ?>
                                <div class="form-check">
                                    <input class="form-check-input table-checkbox" type="checkbox" name="tables[]" value="<?php echo htmlspecialchars($table); ?>" id="table_<?php echo htmlspecialchars($table); ?>">
                                    <label class="form-check-label" for="table_<?php echo htmlspecialchars($table); ?>">
                                        <?php echo htmlspecialchars($table); ?>
                                    </label>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i> Peringatan: Tindakan ini akan menghapus semua data dari tabel yang dipilih. Untuk tabel users/user, hanya pengguna non-admin yang akan dihapus.
                        </div>
                        <button type="submit" class="btn btn-danger" onclick="return validateForm()">
                            <i class="fas fa-eraser"></i> Bersihkan Tabel Terpilih
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function selectAllTables(select) {
    document.querySelectorAll('.table-checkbox').forEach(checkbox => {
        checkbox.checked = select;
    });
}

function validateForm() {
    const checkedBoxes = document.querySelectorAll('.table-checkbox:checked');
    if (checkedBoxes.length === 0) {
        alert('Silakan pilih minimal satu tabel untuk dibersihkan.');
        return false;
    }
    return confirm('Apakah Anda yakin ingin membersihkan tabel yang dipilih? Tindakan ini tidak dapat dibatalkan.');
}
</script>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
