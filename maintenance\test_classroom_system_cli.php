<?php
/**
 * Command Line Interface for Classroom System Testing
 * Run this script to validate the classroom system implementation
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/ClassroomMigration.php';
require_once __DIR__ . '/../models/RuangKelas.php';
require_once __DIR__ . '/../models/Absensi.php';
require_once __DIR__ . '/../models/Tugas.php';
require_once __DIR__ . '/../models/Nilai.php';

echo "=== CLASSROOM SYSTEM VALIDATION TEST ===\n";
echo "Starting comprehensive system validation...\n\n";

$database = new Database();
$conn = $database->getConnection();

$migrationModel = new ClassroomMigration();
$ruangKelasModel = new RuangKelas();
$absensiModel = new Absensi();
$tugasModel = new Tugas();
$nilaiModel = new Nilai();

$test_results = [];
$overall_status = 'success';

// Test 1: Check if all required tables exist
function testTableStructure($conn) {
    echo "1. Testing Table Structure...\n";
    
    $required_tables = [
        'ruang_kelas' => ['id', 'nama_ruang_kelas', 'semester', 'tahun_ajaran'],
        'siswa_ruang_kelas' => ['id', 'siswa_id', 'ruang_kelas_id', 'status'],
        'classroom_migration_log' => ['id', 'migration_step', 'status']
    ];
    
    $results = [];
    foreach ($required_tables as $table => $required_columns) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                // Check columns
                $columns_stmt = $conn->query("SHOW COLUMNS FROM $table");
                $existing_columns = [];
                while ($col = $columns_stmt->fetch(PDO::FETCH_ASSOC)) {
                    $existing_columns[] = $col['Field'];
                }
                
                $missing_columns = array_diff($required_columns, $existing_columns);
                if (empty($missing_columns)) {
                    echo "   ✓ $table - OK\n";
                    $results[$table] = ['status' => 'success', 'message' => 'Table exists with all required columns'];
                } else {
                    echo "   ⚠ $table - Missing columns: " . implode(', ', $missing_columns) . "\n";
                    $results[$table] = ['status' => 'warning', 'message' => 'Missing columns: ' . implode(', ', $missing_columns)];
                }
            } else {
                echo "   ✗ $table - Table does not exist\n";
                $results[$table] = ['status' => 'error', 'message' => 'Table does not exist'];
            }
        } catch (Exception $e) {
            echo "   ✗ $table - Error: " . $e->getMessage() . "\n";
            $results[$table] = ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    return $results;
}

// Test 2: Check if ruang_kelas_id columns exist in related tables
function testColumnAdditions($conn) {
    echo "\n2. Testing Column Additions...\n";
    
    $tables_with_ruang_kelas_id = [
        'absensi', 'tugas', 'nilai', 'nilai_sikap', 'nilai_tugas', 
        'detail_absensi', 'jadwal_pelajaran', 'tugas_tambahan'
    ];
    
    $results = [];
    foreach ($tables_with_ruang_kelas_id as $table) {
        try {
            $stmt = $conn->query("SHOW TABLES LIKE '$table'");
            if ($stmt->rowCount() > 0) {
                $columns_stmt = $conn->query("SHOW COLUMNS FROM $table LIKE 'ruang_kelas_id'");
                if ($columns_stmt->rowCount() > 0) {
                    echo "   ✓ $table - ruang_kelas_id column exists\n";
                    $results[$table] = ['status' => 'success', 'message' => 'ruang_kelas_id column exists'];
                } else {
                    echo "   ⚠ $table - ruang_kelas_id column missing\n";
                    $results[$table] = ['status' => 'warning', 'message' => 'ruang_kelas_id column missing'];
                }
            } else {
                echo "   ℹ $table - Table does not exist (optional)\n";
                $results[$table] = ['status' => 'info', 'message' => 'Table does not exist (optional)'];
            }
        } catch (Exception $e) {
            echo "   ✗ $table - Error: " . $e->getMessage() . "\n";
            $results[$table] = ['status' => 'error', 'message' => $e->getMessage()];
        }
    }
    
    return $results;
}

// Test 3: Check data integrity
function testDataIntegrity($conn) {
    echo "\n3. Testing Data Integrity...\n";
    
    $results = [];
    
    try {
        // Check if there are classrooms
        $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas");
        $classroom_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        
        if ($classroom_count > 0) {
            echo "   ✓ Found $classroom_count classrooms\n";
            $results['classrooms'] = ['status' => 'success', 'message' => "$classroom_count classrooms found"];
            
            // Check student assignments
            $stmt = $conn->query("SELECT COUNT(*) as count FROM siswa_ruang_kelas WHERE status = 'aktif'");
            $assignment_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "   ✓ Found $assignment_count active student assignments\n";
            $results['student_assignments'] = ['status' => 'success', 'message' => "$assignment_count active student assignments"];
            
            // Check attendance records with classroom references
            $stmt = $conn->query("SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NOT NULL");
            $attendance_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "   ✓ Found $attendance_count attendance records linked to classrooms\n";
            $results['attendance_migration'] = ['status' => 'success', 'message' => "$attendance_count attendance records linked to classrooms"];
            
            // Check assignment records with classroom references
            $stmt = $conn->query("SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NOT NULL");
            $assignment_migration_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
            echo "   ✓ Found $assignment_migration_count assignment records linked to classrooms\n";
            $results['assignment_migration'] = ['status' => 'success', 'message' => "$assignment_migration_count assignment records linked to classrooms"];
            
        } else {
            echo "   ⚠ No classrooms found - migration may not be complete\n";
            $results['classrooms'] = ['status' => 'warning', 'message' => 'No classrooms found - migration may not be complete'];
        }
        
    } catch (Exception $e) {
        echo "   ✗ Data integrity check failed: " . $e->getMessage() . "\n";
        $results['data_integrity'] = ['status' => 'error', 'message' => $e->getMessage()];
    }
    
    return $results;
}

// Test 4: Test model functionality
function testModelFunctionality($ruangKelasModel, $absensiModel, $tugasModel, $nilaiModel) {
    echo "\n4. Testing Model Functionality...\n";
    
    $results = [];
    
    try {
        // Test RuangKelas model
        $stmt = $ruangKelasModel->getAll();
        echo "   ✓ RuangKelas model working\n";
        $results['ruang_kelas_model'] = ['status' => 'success', 'message' => 'RuangKelas model working'];
        
        // Test classroom system availability in other models
        if ($absensiModel->isClassroomSystemAvailable()) {
            echo "   ✓ Absensi model supports classroom system\n";
            $results['absensi_classroom_support'] = ['status' => 'success', 'message' => 'Absensi model supports classroom system'];
        } else {
            echo "   ⚠ Absensi model does not detect classroom system\n";
            $results['absensi_classroom_support'] = ['status' => 'warning', 'message' => 'Absensi model does not detect classroom system'];
        }
        
        if ($tugasModel->isClassroomSystemAvailable()) {
            echo "   ✓ Tugas model supports classroom system\n";
            $results['tugas_classroom_support'] = ['status' => 'success', 'message' => 'Tugas model supports classroom system'];
        } else {
            echo "   ⚠ Tugas model does not detect classroom system\n";
            $results['tugas_classroom_support'] = ['status' => 'warning', 'message' => 'Tugas model does not detect classroom system'];
        }
        
        if ($nilaiModel->isClassroomSystemAvailable()) {
            echo "   ✓ Nilai model supports classroom system\n";
            $results['nilai_classroom_support'] = ['status' => 'success', 'message' => 'Nilai model supports classroom system'];
        } else {
            echo "   ⚠ Nilai model does not detect classroom system\n";
            $results['nilai_classroom_support'] = ['status' => 'warning', 'message' => 'Nilai model does not detect classroom system'];
        }
        
    } catch (Exception $e) {
        echo "   ✗ Model functionality test failed: " . $e->getMessage() . "\n";
        $results['model_functionality'] = ['status' => 'error', 'message' => $e->getMessage()];
    }
    
    return $results;
}

// Run all tests
$test_results['Table Structure'] = testTableStructure($conn);
$test_results['Column Additions'] = testColumnAdditions($conn);
$test_results['Data Integrity'] = testDataIntegrity($conn);
$test_results['Model Functionality'] = testModelFunctionality($ruangKelasModel, $absensiModel, $tugasModel, $nilaiModel);

// Determine overall status
foreach ($test_results as $category => $tests) {
    foreach ($tests as $test => $result) {
        if ($result['status'] === 'error') {
            $overall_status = 'error';
            break 2;
        } elseif ($result['status'] === 'warning' && $overall_status !== 'error') {
            $overall_status = 'warning';
        }
    }
}

// Display final results
echo "\n=== FINAL RESULTS ===\n";
if ($overall_status === 'success') {
    echo "✅ SYSTEM STATUS: ALL TESTS PASSED\n";
    echo "The classroom system is fully functional and ready for use.\n";
} elseif ($overall_status === 'warning') {
    echo "⚠️  SYSTEM STATUS: SOME ISSUES FOUND\n";
    echo "The classroom system is mostly functional but some components need attention.\n";
} else {
    echo "❌ SYSTEM STATUS: CRITICAL ISSUES FOUND\n";
    echo "The classroom system has critical issues that need to be resolved before use.\n";
}

echo "\n=== RECOMMENDATIONS ===\n";
if ($overall_status === 'success') {
    echo "• Start creating classrooms for the current academic period\n";
    echo "• Assign students to appropriate classrooms\n";
    echo "• Begin using the updated attendance and assignment modules\n";
    echo "• Monitor system performance and data consistency\n";
} elseif ($overall_status === 'warning') {
    echo "• Review the warnings above and address missing components\n";
    echo "• Run the migration process if not completed\n";
    echo "• Verify data integrity before full deployment\n";
    echo "• Test core functionality with sample data\n";
} else {
    echo "• Do not use the system until all errors are resolved\n";
    echo "• Run the table creation process manually if needed\n";
    echo "• Check database permissions and connectivity\n";
    echo "• Contact system administrator for assistance\n";
}

echo "\nValidation complete.\n";
?>
