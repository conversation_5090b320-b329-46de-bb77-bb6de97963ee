<?php
require_once 'config/database.php';

echo "<h1>Migration for Existing Classroom Data</h1>";
echo "<p><strong>INFO:</strong> This migration is designed for systems that already have ruang_kelas and student assignments.</p>";

$database = new Database();
$conn = $database->getConnection();

// Check if migration should proceed
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>ℹ️ Migration for Existing Classroom Data</h3>";
    echo "<p>This migration will:</p>";
    echo "<ul>";
    echo "<li><strong>SKIP</strong> creating ruang_kelas data (you already have it)</li>";
    echo "<li><strong>SKIP</strong> student assignments (you already have them)</li>";
    echo "<li><strong>UPDATE</strong> existing tugas/absensi records to use ruang_kelas_id</li>";
    echo "<li><strong>ADD</strong> ruang_kelas_id columns to tables if missing</li>";
    echo "<li><strong>PRESERVE</strong> all your existing classroom and student data</li>";
    echo "</ul>";
    echo "<p><strong>SAFE:</strong> This will not overwrite your existing ruang_kelas or siswa_ruang_kelas data.</p>";
    echo "<p><a href='?confirm=yes' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Proceed with Migration</a></p>";
    echo "</div>";
    
    // Show current data status
    echo "<h3>Current Data Status</h3>";
    try {
        $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas");
        $ruang_kelas_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>✓ Ruang Kelas: $ruang_kelas_count records</p>";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM siswa_ruang_kelas WHERE status = 'aktif'");
        $student_assignments = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>✓ Active Student Assignments: $student_assignments records</p>";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NOT NULL");
        $tugas_migrated = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $stmt = $conn->query("SELECT COUNT(*) as count FROM tugas");
        $tugas_total = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>📝 Tugas with ruang_kelas_id: $tugas_migrated / $tugas_total</p>";
        
        $stmt = $conn->query("SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NOT NULL");
        $absensi_migrated = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $stmt = $conn->query("SELECT COUNT(*) as count FROM absensi");
        $absensi_total = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>📋 Absensi with ruang_kelas_id: $absensi_migrated / $absensi_total</p>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>Error checking data: " . $e->getMessage() . "</p>";
    }
    
    exit;
}

$transaction_started = false;

try {
    $conn->beginTransaction();
    $transaction_started = true;

    echo "<h2>Starting Migration for Existing Classroom Data...</h2>";
    
    // Step 1: Add columns if they don't exist
    echo "<h3>Step 1: Adding Required Columns</h3>";
    
    $columns_to_add = [
        'tugas' => 'ruang_kelas_id',
        'absensi' => 'ruang_kelas_id',
        'nilai' => 'ruang_kelas_id',
        'nilai_sikap' => 'ruang_kelas_id',
        'nilai_tugas' => 'ruang_kelas_id',
        'siswa' => 'kelas_id_backup'
    ];
    
    foreach ($columns_to_add as $table => $column) {
        try {
            $check_column = "SHOW COLUMNS FROM `$table` LIKE '$column'";
            $stmt = $conn->query($check_column);
            
            if ($stmt->rowCount() == 0) {
                $add_column = "ALTER TABLE `$table` ADD COLUMN `$column` int(11) DEFAULT NULL";
                $conn->exec($add_column);
                echo "<div style='color: green;'>✓ Added column $column to table $table</div>";
            } else {
                echo "<div style='color: blue;'>ℹ Column $column already exists in table $table</div>";
            }
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), "doesn't exist") !== false) {
                echo "<div style='color: orange;'>⚠ Table $table doesn't exist, skipping</div>";
            } else {
                echo "<div style='color: red;'>✗ Error with table $table: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // Step 2: Add indexes
    echo "<h3>Step 2: Adding Indexes</h3>";
    
    $indexes_to_add = [
        'tugas' => 'fk_tugas_ruang_kelas',
        'absensi' => 'fk_absensi_ruang_kelas',
        'nilai' => 'fk_nilai_ruang_kelas'
    ];
    
    foreach ($indexes_to_add as $table => $index_name) {
        try {
            $add_index = "ALTER TABLE `$table` ADD KEY IF NOT EXISTS `$index_name` (`ruang_kelas_id`)";
            $conn->exec($add_index);
            echo "<div style='color: green;'>✓ Added index $index_name to table $table</div>";
        } catch (PDOException $e) {
            if (strpos($e->getMessage(), "Duplicate key") !== false || 
                strpos($e->getMessage(), "already exists") !== false) {
                echo "<div style='color: blue;'>ℹ Index $index_name already exists in table $table</div>";
            } else {
                echo "<div style='color: orange;'>⚠ Could not add index to $table: " . $e->getMessage() . "</div>";
            }
        }
    }
    
    // Step 3: Backup kelas_id in siswa table
    echo "<h3>Step 3: Backing up existing kelas_id</h3>";
    try {
        $backup_sql = "UPDATE `siswa` SET `kelas_id_backup` = `kelas_id` WHERE `kelas_id` IS NOT NULL AND `kelas_id_backup` IS NULL";
        $result = $conn->exec($backup_sql);
        echo "<div style='color: green;'>✓ Backed up $result kelas_id values in siswa table</div>";
    } catch (PDOException $e) {
        echo "<div style='color: orange;'>⚠ Backup kelas_id: " . $e->getMessage() . "</div>";
    }
    
    // Step 4: Update existing records
    echo "<h3>Step 4: Updating Existing Records</h3>";
    
    // Update tugas records
    try {
        $update_tugas = "
            UPDATE tugas t
            INNER JOIN kelas k ON t.kelas_id = k.id
            INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
                AND rk.tahun_ajaran = t.tahun_ajaran 
                AND rk.semester = t.semester
                AND rk.status = 'aktif'
            SET t.ruang_kelas_id = rk.id
            WHERE t.ruang_kelas_id IS NULL AND t.kelas_id IS NOT NULL
        ";
        $result = $conn->exec($update_tugas);
        echo "<div style='color: green;'>✓ Updated $result tugas records with ruang_kelas_id</div>";
    } catch (PDOException $e) {
        echo "<div style='color: red;'>✗ Error updating tugas: " . $e->getMessage() . "</div>";
    }
    
    // Update absensi records
    try {
        $update_absensi = "
            UPDATE absensi a
            INNER JOIN kelas k ON a.kelas_id = k.id
            INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
                AND rk.tahun_ajaran = a.tahun_ajaran 
                AND rk.semester = a.semester
                AND rk.status = 'aktif'
            SET a.ruang_kelas_id = rk.id
            WHERE a.ruang_kelas_id IS NULL AND a.kelas_id IS NOT NULL
        ";
        $result = $conn->exec($update_absensi);
        echo "<div style='color: green;'>✓ Updated $result absensi records with ruang_kelas_id</div>";
    } catch (PDOException $e) {
        echo "<div style='color: red;'>✗ Error updating absensi: " . $e->getMessage() . "</div>";
    }
    
    // Update nilai records
    try {
        $update_nilai = "
            UPDATE nilai n
            INNER JOIN siswa s ON n.siswa_id = s.id
            INNER JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id AND srk.status = 'aktif'
            INNER JOIN ruang_kelas rk ON srk.ruang_kelas_id = rk.id
                AND rk.tahun_ajaran = n.tahun_ajaran 
                AND rk.semester = n.semester
                AND rk.status = 'aktif'
            SET n.ruang_kelas_id = rk.id
            WHERE n.ruang_kelas_id IS NULL
        ";
        $result = $conn->exec($update_nilai);
        echo "<div style='color: green;'>✓ Updated $result nilai records with ruang_kelas_id</div>";
    } catch (PDOException $e) {
        echo "<div style='color: red;'>✗ Error updating nilai: " . $e->getMessage() . "</div>";
    }
    
    // Step 5: Verification
    echo "<h3>Step 5: Migration Verification</h3>";
    
    $verification_queries = [
        'tugas_migrated' => "SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NOT NULL",
        'tugas_total' => "SELECT COUNT(*) as count FROM tugas",
        'absensi_migrated' => "SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NOT NULL",
        'absensi_total' => "SELECT COUNT(*) as count FROM absensi",
        'ruang_kelas_count' => "SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'",
        'student_assignments' => "SELECT COUNT(*) as count FROM siswa_ruang_kelas WHERE status = 'aktif'"
    ];
    
    $results = [];
    foreach ($verification_queries as $name => $query) {
        try {
            $stmt = $conn->query($query);
            $results[$name] = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        } catch (PDOException $e) {
            $results[$name] = 0;
        }
    }
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Item</th><th style='border: 1px solid #ddd; padding: 10px;'>Count</th><th style='border: 1px solid #ddd; padding: 10px;'>Status</th></tr>";
    
    $tugas_percentage = $results['tugas_total'] > 0 ? ($results['tugas_migrated'] / $results['tugas_total']) * 100 : 0;
    $absensi_percentage = $results['absensi_total'] > 0 ? ($results['absensi_migrated'] / $results['absensi_total']) * 100 : 0;
    
    echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>Active Classrooms</td><td style='border: 1px solid #ddd; padding: 10px;'>{$results['ruang_kelas_count']}</td><td style='border: 1px solid #ddd; padding: 10px;'><span style='color: green;'>✓ OK</span></td></tr>";
    echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>Student Assignments</td><td style='border: 1px solid #ddd; padding: 10px;'>{$results['student_assignments']}</td><td style='border: 1px solid #ddd; padding: 10px;'><span style='color: green;'>✓ OK</span></td></tr>";
    echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>Tugas Migration</td><td style='border: 1px solid #ddd; padding: 10px;'>{$results['tugas_migrated']}/{$results['tugas_total']}</td><td style='border: 1px solid #ddd; padding: 10px;'>" . round($tugas_percentage, 1) . "%</td></tr>";
    echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>Absensi Migration</td><td style='border: 1px solid #ddd; padding: 10px;'>{$results['absensi_migrated']}/{$results['absensi_total']}</td><td style='border: 1px solid #ddd; padding: 10px;'>" . round($absensi_percentage, 1) . "%</td></tr>";
    echo "</table>";
    
    if ($transaction_started) {
        $conn->commit();
        $transaction_started = false;
    }

    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Migration Completed Successfully!</h3>";
    echo "<p>Your existing classroom data has been preserved and the system has been updated to use the new classroom system.</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Test creating new assignments (tugas) - they should now use classroom selection</li>";
    echo "<li>Test creating new attendance (absensi) - they should now use classroom selection</li>";
    echo "<li>Verify that existing data is still accessible</li>";
    echo "</ol>";
    echo "</div>";

} catch (Exception $e) {
    if ($transaction_started) {
        try {
            $conn->rollback();
        } catch (PDOException $rollback_error) {
            echo "<div style='color: red;'>Warning: Could not rollback transaction: " . $rollback_error->getMessage() . "</div>";
        }
    }

    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Migration Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    if ($transaction_started) {
        echo "<p>The database has been rolled back to its previous state.</p>";
    } else {
        echo "<p>Some changes may have been applied before the error occurred.</p>";
    }
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='test_classroom_migration.php'>Test Migration Results</a> | <a href='tugas/index.php'>Test Tugas System</a> | <a href='absensi/create.php'>Test Absensi System</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
