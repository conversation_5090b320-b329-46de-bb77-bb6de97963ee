<?php
require_once 'config/database.php';

echo "<h1>Complete Migration to Classroom System</h1>";
echo "<p><strong>WARNING:</strong> This will completely migrate your system from the old class system to the new classroom system.</p>";

$database = new Database();
$conn = $database->getConnection();

// Check if migration should proceed
if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>⚠️ Migration Confirmation Required</h3>";
    echo "<p>This migration will:</p>";
    echo "<ul>";
    echo "<li>Create new <code>ruang_kelas</code> and <code>siswa_ruang_kelas</code> tables</li>";
    echo "<li>Migrate all existing class data to the new classroom system</li>";
    echo "<li>Update all related tables (tugas, absensi, nilai, etc.)</li>";
    echo "<li>Assign students to classrooms for both semesters</li>";
    echo "<li>Make the system use <code>ruang_kelas_id</code> instead of <code>kelas_id</code></li>";
    echo "</ul>";
    echo "<p><strong>IMPORTANT:</strong> Please backup your database before proceeding!</p>";
    echo "<p><a href='?confirm=yes' style='background: #dc3545; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Proceed with Migration</a></p>";
    echo "</div>";
    exit;
}

try {
    $conn->beginTransaction();
    
    echo "<h2>Starting Database Migration...</h2>";
    
    // Read and execute the migration SQL
    $migrationSQL = file_get_contents('database/complete_classroom_migration.sql');
    
    // Split SQL into individual statements
    $statements = array_filter(array_map('trim', explode(';', $migrationSQL)));
    
    $step = 1;
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        echo "<div style='margin: 10px 0; padding: 10px; background: #f8f9fa; border-left: 4px solid #007bff;'>";
        echo "<strong>Step $step:</strong> Executing SQL statement...<br>";
        
        try {
            $result = $conn->exec($statement);
            echo "<span style='color: green;'>✓ Success</span>";
            if ($result > 0) {
                echo " ($result rows affected)";
            }
        } catch (PDOException $e) {
            // Some statements might fail if tables/columns already exist, which is OK
            if (strpos($e->getMessage(), 'already exists') !== false || 
                strpos($e->getMessage(), 'Duplicate column') !== false ||
                strpos($e->getMessage(), 'Duplicate key') !== false) {
                echo "<span style='color: orange;'>⚠ Already exists (skipped)</span>";
            } else {
                echo "<span style='color: red;'>✗ Error: " . $e->getMessage() . "</span>";
            }
        }
        echo "</div>";
        $step++;
    }
    
    // Verify migration results
    echo "<h2>Migration Verification</h2>";
    
    $verificationQueries = [
        'ruang_kelas' => "SELECT COUNT(*) as count FROM ruang_kelas",
        'siswa_ruang_kelas' => "SELECT COUNT(*) as count FROM siswa_ruang_kelas",
        'tugas_with_ruang_kelas' => "SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NOT NULL",
        'absensi_with_ruang_kelas' => "SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NOT NULL",
        'students_assigned' => "SELECT COUNT(DISTINCT siswa_id) as count FROM siswa_ruang_kelas WHERE status = 'aktif'"
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Item</th><th style='border: 1px solid #ddd; padding: 10px;'>Count</th><th style='border: 1px solid #ddd; padding: 10px;'>Status</th></tr>";
    
    foreach ($verificationQueries as $name => $query) {
        try {
            $stmt = $conn->query($query);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $count = $result['count'];
            $status = $count > 0 ? "<span style='color: green;'>✓ OK</span>" : "<span style='color: orange;'>⚠ Empty</span>";
            
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . ucfirst(str_replace('_', ' ', $name)) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>$count</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>$status</td>";
            echo "</tr>";
        } catch (PDOException $e) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>" . ucfirst(str_replace('_', ' ', $name)) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'>Error</td>";
            echo "<td style='border: 1px solid #ddd; padding: 10px;'><span style='color: red;'>✗ " . $e->getMessage() . "</span></td>";
            echo "</tr>";
        }
    }
    echo "</table>";
    
    // Show sample classroom data
    echo "<h3>Sample Classroom Data</h3>";
    try {
        $stmt = $conn->query("SELECT rk.id, rk.nama_ruang_kelas, rk.semester, rk.tahun_ajaran, 
                                     COUNT(srk.siswa_id) as jumlah_siswa
                              FROM ruang_kelas rk
                              LEFT JOIN siswa_ruang_kelas srk ON rk.id = srk.ruang_kelas_id AND srk.status = 'aktif'
                              GROUP BY rk.id, rk.nama_ruang_kelas, rk.semester, rk.tahun_ajaran
                              ORDER BY rk.nama_ruang_kelas, rk.semester
                              LIMIT 10");
        
        echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
        echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 8px;'>ID</th><th style='border: 1px solid #ddd; padding: 8px;'>Classroom Name</th><th style='border: 1px solid #ddd; padding: 8px;'>Semester</th><th style='border: 1px solid #ddd; padding: 8px;'>Academic Year</th><th style='border: 1px solid #ddd; padding: 8px;'>Students</th></tr>";
        
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "<tr>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['id'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . htmlspecialchars($row['nama_ruang_kelas']) . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['semester'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['tahun_ajaran'] . "</td>";
            echo "<td style='border: 1px solid #ddd; padding: 8px;'>" . $row['jumlah_siswa'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } catch (PDOException $e) {
        echo "<p style='color: red;'>Error displaying classroom data: " . $e->getMessage() . "</p>";
    }
    
    $conn->commit();
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>✅ Database Migration Completed Successfully!</h3>";
    echo "<p>The database has been successfully migrated to the new classroom system.</p>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Update the application code to use the new classroom system</li>";
    echo "<li>Test all functionality (creating assignments, attendance, etc.)</li>";
    echo "<li>Verify data integrity</li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    $conn->rollback();
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Migration Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>The database has been rolled back to its previous state.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><a href='index.php'>Return to Dashboard</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
