<?php
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/PeriodeAktif.php';
require_once __DIR__ . '/TahunAjaran.php';

class Absensi {
    private $conn;
    private $table_name = "absensi";
    private $detail_table = "detail_absensi";

    public $id;
    public $tanggal;
    public $kelas_id; // Legacy - for backward compatibility
    public $ruang_kelas_id; // New classroom reference
    public $mapel_id;
    public $created_at;
    public $updated_at;
    public $nama_kelas;
    public $nama_ruang_kelas;
    public $nama_mapel;
    public $semester;
    public $tahun_ajaran;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getOne() {
        $query = "SELECT a.*,
                         COALESCE(rk.nama_ruang_kelas, k.nama_kelas) as nama_kelas,
                         rk.nama_ruang_kelas,
                         m.nama_mapel
                FROM " . $this->table_name . " a
                LEFT JOIN ruang_kelas rk ON a.ruang_kelas_id = rk.id
                LEFT JOIN kelas k ON a.kelas_id = k.id
                JOIN mata_pelajaran m ON a.mapel_id = m.id
                WHERE a.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($row) {
            $this->tanggal = $row['tanggal'];
            $this->kelas_id = $row['kelas_id'];
            $this->ruang_kelas_id = $row['ruang_kelas_id'];
            $this->mapel_id = $row['mapel_id'];
            $this->created_at = $row['created_at'];
            $this->updated_at = $row['updated_at'];
            $this->nama_kelas = $row['nama_kelas'];
            $this->nama_ruang_kelas = $row['nama_ruang_kelas'];
            $this->nama_mapel = $row['nama_mapel'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            return true;
        }
        return false;
    }

    public function getDetailAbsensi($absensi_id) {
        $query = "SELECT da.*, s.nis, s.nama_siswa
                FROM " . $this->detail_table . " da
                JOIN siswa s ON da.siswa_id = s.id
                WHERE da.absensi_id = :absensi_id
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":absensi_id", $absensi_id);
        $stmt->execute();

        return $stmt;
    }

    public function create() {
        $periode = new PeriodeAktif();
        if(!$periode->getActive()) {
            return false;
        }

        // Validate required fields
        if (empty($this->tanggal) || empty($this->mapel_id)) {
            return false;
        }

        // Validate that either ruang_kelas_id or kelas_id is provided
        if (empty($this->ruang_kelas_id) && empty($this->kelas_id)) {
            return false;
        }

        // Validate tanggal is within active period
        if(strtotime($this->tanggal) < strtotime($periode->tanggal_mulai) ||
           strtotime($this->tanggal) > strtotime($periode->tanggal_selesai)) {
            return false;
        }

        // Validate foreign key references exist
        if ($this->ruang_kelas_id) {
            // Check if ruang_kelas_id exists
            $check_query = "SELECT id FROM ruang_kelas WHERE id = :ruang_kelas_id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(":ruang_kelas_id", $this->ruang_kelas_id);
            $check_stmt->execute();
            if ($check_stmt->rowCount() == 0) {
                return false; // ruang_kelas_id doesn't exist
            }
        } else {
            // Check if kelas_id exists
            $check_query = "SELECT id FROM kelas WHERE id = :kelas_id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(":kelas_id", $this->kelas_id);
            $check_stmt->execute();
            if ($check_stmt->rowCount() == 0) {
                return false; // kelas_id doesn't exist
            }
        }

        // Check if mapel_id exists
        $check_query = "SELECT id FROM mata_pelajaran WHERE id = :mapel_id";
        $check_stmt = $this->conn->prepare($check_query);
        $check_stmt->bindParam(":mapel_id", $this->mapel_id);
        $check_stmt->execute();
        if ($check_stmt->rowCount() == 0) {
            return false; // mapel_id doesn't exist
        }

        // Use ruang_kelas_id if available, otherwise fall back to kelas_id
        if ($this->ruang_kelas_id) {
            $query = "INSERT INTO " . $this->table_name . "
                    (tanggal, ruang_kelas_id, mapel_id, semester, tahun_ajaran)
                    VALUES
                    (:tanggal, :ruang_kelas_id, :mapel_id, :semester, :tahun_ajaran)";
        } else {
            $query = "INSERT INTO " . $this->table_name . "
                    (tanggal, kelas_id, mapel_id, semester, tahun_ajaran)
                    VALUES
                    (:tanggal, :kelas_id, :mapel_id, :semester, :tahun_ajaran)";
        }

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->tanggal = htmlspecialchars(strip_tags($this->tanggal));
        if ($this->ruang_kelas_id) {
            $this->ruang_kelas_id = htmlspecialchars(strip_tags($this->ruang_kelas_id));
        } else {
            $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
        }
        $this->mapel_id = htmlspecialchars(strip_tags($this->mapel_id));

        // Bind values
        $stmt->bindParam(":tanggal", $this->tanggal);
        if ($this->ruang_kelas_id) {
            $stmt->bindParam(":ruang_kelas_id", $this->ruang_kelas_id);
        } else {
            $stmt->bindParam(":kelas_id", $this->kelas_id);
        }
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":semester", $periode->semester);
        $stmt->bindParam(":tahun_ajaran", $periode->tahun_ajaran);

        try {
            if($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();
                $this->semester = $periode->semester;
                $this->tahun_ajaran = $periode->tahun_ajaran;
                return true;
            }
        } catch (PDOException $e) {
            // Log error for debugging
            error_log("Absensi create error: " . $e->getMessage());
            return false;
        }
        return false;
    }

    public function createDetail($absensi_id, $siswa_id, $status) {
        $query = "INSERT INTO " . $this->detail_table . "
                (absensi_id, siswa_id, status)
                VALUES
                (:absensi_id, :siswa_id, :status)";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $absensi_id = htmlspecialchars(strip_tags($absensi_id));
        $siswa_id = htmlspecialchars(strip_tags($siswa_id));
        $status = htmlspecialchars(strip_tags($status));

        // Bind values
        $stmt->bindParam(":absensi_id", $absensi_id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":status", $status);

        return $stmt->execute();
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET tanggal = :tanggal,
                    kelas_id = :kelas_id,
                    mapel_id = :mapel_id
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->tanggal = htmlspecialchars(strip_tags($this->tanggal));
        $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
        $this->mapel_id = htmlspecialchars(strip_tags($this->mapel_id));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind values
        $stmt->bindParam(":tanggal", $this->tanggal);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function updateDetail($absensi_id, $siswa_id, $status) {
        // Check if detail exists
        $query = "SELECT id FROM " . $this->detail_table . "
                WHERE absensi_id = :absensi_id AND siswa_id = :siswa_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":absensi_id", $absensi_id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->execute();

        if ($stmt->rowCount() > 0) {
            // Update existing record
            $query = "UPDATE " . $this->detail_table . "
                    SET status = :status
                    WHERE absensi_id = :absensi_id AND siswa_id = :siswa_id";
        } else {
            // Insert new record
            $query = "INSERT INTO " . $this->detail_table . "
                    (absensi_id, siswa_id, status)
                    VALUES (:absensi_id, :siswa_id, :status)";
        }

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $status = htmlspecialchars(strip_tags($status));
        $absensi_id = htmlspecialchars(strip_tags($absensi_id));
        $siswa_id = htmlspecialchars(strip_tags($siswa_id));

        // Bind values
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":absensi_id", $absensi_id);
        $stmt->bindParam(":siswa_id", $siswa_id);

        return $stmt->execute();
    }

    public function delete() {
        // First delete all detail records
        $query = "DELETE FROM " . $this->detail_table . " WHERE absensi_id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();

        // Then delete the main record
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function getByDateRange($start_date, $end_date, $kelas_id = null) {
        $periode = new PeriodeAktif();
        if(!$periode->getActive()) {
            return false;
        }

        // Ensure dates are within active period
        $start_date = max($start_date, $periode->tanggal_mulai);
        $end_date = min($end_date, $periode->tanggal_selesai);

        $query = "SELECT DISTINCT a.id, a.tanggal, k.nama_kelas, m.nama_mapel
                 FROM " . $this->table_name . " a
                 JOIN kelas k ON a.kelas_id = k.id
                 JOIN mata_pelajaran m ON a.mapel_id = m.id
                 WHERE a.tanggal BETWEEN :start_date AND :end_date";

        if($kelas_id) {
            $query .= " AND a.kelas_id = :kelas_id";
        }

        $query .= " ORDER BY a.tanggal DESC, k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":start_date", $start_date);
        $stmt->bindParam(":end_date", $end_date);

        if($kelas_id) {
            $stmt->bindParam(":kelas_id", $kelas_id);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getByDateRangeAndMapel($start_date, $end_date, $kelas_id = null, $mapel_ids = []) {
        $periode = new PeriodeAktif();
        if(!$periode->getActive()) {
            return false;
        }

        // Ensure dates are within active period
        $start_date = max($start_date, $periode->tanggal_mulai);
        $end_date = min($end_date, $periode->tanggal_selesai);

        $query = "SELECT DISTINCT a.id, a.tanggal, k.nama_kelas, m.nama_mapel
                 FROM " . $this->table_name . " a
                 JOIN kelas k ON a.kelas_id = k.id
                 JOIN mata_pelajaran m ON a.mapel_id = m.id
                 WHERE a.tanggal BETWEEN :start_date AND :end_date
                 AND a.mapel_id IN (" . implode(',', array_map(function($k) { return ':mapel_'.$k; }, array_keys($mapel_ids))) . ")";

        if($kelas_id) {
            $query .= " AND a.kelas_id = :kelas_id";
        }

        $query .= " ORDER BY a.tanggal DESC, k.nama_kelas ASC";

        $stmt = $this->conn->prepare($query);

        // Bind date parameters
        $stmt->bindParam(":start_date", $start_date);
        $stmt->bindParam(":end_date", $end_date);

        // Bind mapel_ids using named parameters
        foreach($mapel_ids as $key => $mapel_id) {
            $stmt->bindValue(":mapel_".$key, $mapel_id);
        }

        // Bind kelas_id if provided
        if($kelas_id) {
            $stmt->bindParam(":kelas_id", $kelas_id);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getRekapByPeriode($kelas_id = null, $semester = null, $tahun_ajaran = null) {
        $periode = new PeriodeAktif();
        if(!$periode->getActive()) {
            return false;
        }

        // If semester and tahun_ajaran are not provided, use active period
        if ($semester === null || $tahun_ajaran === null) {
            $semester = $periode->semester;
            $tahun_ajaran = $periode->tahun_ajaran;
        }

        $query = "SELECT
                    s.id as siswa_id,
                    s.nama_siswa,
                    s.nis,
                    k.nama_kelas,
                    SUM(CASE WHEN da.status = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
                    SUM(CASE WHEN da.status = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
                    SUM(CASE WHEN da.status = 'Izin' THEN 1 ELSE 0 END) as total_izin,
                    SUM(CASE WHEN da.status = 'Alpha' THEN 1 ELSE 0 END) as total_alpha
                FROM siswa s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                LEFT JOIN detail_absensi da ON s.id = da.siswa_id
                LEFT JOIN absensi a ON da.absensi_id = a.id
                WHERE 1=1
                AND (da.id IS NULL OR (a.semester = :semester AND a.tahun_ajaran = :tahun_ajaran))";

        if ($kelas_id) {
            $query .= " AND s.kelas_id = :kelas_id";
        }

        $query .= " GROUP BY s.id, s.nama_siswa, s.nis, k.nama_kelas
                   ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);

        // Bind semester and tahun_ajaran parameters
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);

        if ($kelas_id) {
            $stmt->bindParam(":kelas_id", $kelas_id);
        }

        $stmt->execute();
        return $stmt;
    }

    public function getRekapByKelas($kelas_id, $month, $year) {
        $query = "SELECT
                    s.nis,
                    s.nama_siswa,
                    k.nama_kelas,
                    COUNT(CASE WHEN da.status = 'Hadir' THEN 1 END) as hadir,
                    COUNT(CASE WHEN da.status = 'Sakit' THEN 1 END) as sakit,
                    COUNT(CASE WHEN da.status = 'Izin' THEN 1 END) as izin,
                    COUNT(CASE WHEN da.status = 'Alpha' THEN 1 END) as alpha
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                LEFT JOIN detail_absensi da ON s.id = da.siswa_id
                LEFT JOIN absensi a ON da.absensi_id = a.id
                    AND MONTH(a.tanggal) = :month
                    AND YEAR(a.tanggal) = :year
                WHERE s.kelas_id = :kelas_id
                GROUP BY s.id, s.nis, s.nama_siswa, k.nama_kelas
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':kelas_id', $kelas_id);
        $stmt->bindParam(':month', $month);
        $stmt->bindParam(':year', $year);
        $stmt->execute();

        return $stmt;
    }

    public function getRekapDetailSiswa($siswa_id, $semester = null, $tahun_ajaran = null) {
        $periode = new PeriodeAktif();
        if(!$periode->getActive()) {
            return false;
        }

        // If semester and tahun_ajaran are not provided, use active period
        if ($semester === null || $tahun_ajaran === null) {
            $semester = $periode->semester;
            $tahun_ajaran = $periode->tahun_ajaran;
        }

        $query = "WITH TimeRanges AS (
                    SELECT
                        jp.id as jadwal_id,
                        djj.jam_ke,
                        djj.jam_mulai,
                        djj.jam_selesai,
                        CASE
                            WHEN djj.jam_mulai = LAG(djj.jam_selesai) OVER (PARTITION BY jp.id ORDER BY djj.jam_ke)
                            THEN 0
                            ELSE 1
                        END as new_group
                    FROM jadwal_pelajaran jp
                    JOIN detail_jadwal_jam djj ON jp.id = djj.jadwal_id
                    ORDER BY jp.id, djj.jam_ke
                ),
                GroupedRanges AS (
                    SELECT
                        jadwal_id,
                        SUM(new_group) OVER (PARTITION BY jadwal_id ORDER BY jam_ke) as group_id,
                        jam_ke,
                        jam_mulai,
                        jam_selesai
                    FROM TimeRanges
                ),
                FinalRanges AS (
                    SELECT
                        jadwal_id,
                        group_id,
                        MIN(jam_ke) as start_jam,
                        MAX(jam_ke) as end_jam,
                        MIN(jam_mulai) as start_time,
                        MAX(jam_selesai) as end_time
                    FROM GroupedRanges
                    GROUP BY jadwal_id, group_id
                ),
                FormattedRanges AS (
                    SELECT
                        jadwal_id,
                        CONCAT(
                            'Jam ke-',
                            start_jam,
                            CASE
                                WHEN start_jam != end_jam THEN CONCAT('-', end_jam)
                                ELSE ''
                            END,
                            ' (',
                            DATE_FORMAT(start_time, '%H:%i'),
                            '-',
                            DATE_FORMAT(end_time, '%H:%i'),
                            ')'
                        ) as jam_display
                    FROM FinalRanges
                )
                SELECT
                    a.tanggal as tanggal_absensi,
                    d.status,
                    s.nama_siswa,
                    s.nis,
                    k.nama_kelas,
                    m.nama_mapel,
                    g.nama_lengkap as nama_guru,
                    GROUP_CONCAT(DISTINCT fr.jam_display ORDER BY fr.jam_display) as waktu,
                    wk.nama_lengkap as wali_kelas
                FROM " . $this->table_name . " a
                JOIN " . $this->detail_table . " d ON a.id = d.absensi_id
                JOIN siswa s ON d.siswa_id = s.id
                JOIN kelas k ON a.kelas_id = k.id
                JOIN mata_pelajaran m ON a.mapel_id = m.id
                JOIN mapel_guru mg ON m.id = mg.mapel_id
                JOIN guru g ON mg.guru_id = g.id
                JOIN jadwal_pelajaran jp ON (
                    a.kelas_id = jp.kelas_id
                    AND a.mapel_id = jp.mapel_id
                    AND jp.hari = CASE
                        WHEN DAYNAME(a.tanggal) = 'Monday' THEN 'Senin'
                        WHEN DAYNAME(a.tanggal) = 'Tuesday' THEN 'Selasa'
                        WHEN DAYNAME(a.tanggal) = 'Wednesday' THEN 'Rabu'
                        WHEN DAYNAME(a.tanggal) = 'Thursday' THEN 'Kamis'
                        WHEN DAYNAME(a.tanggal) = 'Friday' THEN 'Jumat'
                        WHEN DAYNAME(a.tanggal) = 'Saturday' THEN 'Sabtu'
                        WHEN DAYNAME(a.tanggal) = 'Sunday' THEN 'Minggu'
                    END
                )
                JOIN detail_jadwal_jam djj ON jp.id = djj.jadwal_id
                JOIN FormattedRanges fr ON jp.id = fr.jadwal_id
                LEFT JOIN guru wk ON k.guru_id = wk.id
                WHERE d.siswa_id = :siswa_id
                AND a.semester = :semester
                AND a.tahun_ajaran = :tahun_ajaran
                GROUP BY a.id, a.tanggal, d.status, s.nama_siswa, s.nis, k.nama_kelas, m.nama_mapel, g.nama_lengkap, wk.nama_lengkap
                ORDER BY a.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);

        $stmt->execute();
        return $stmt;
    }

    public function addDetail($siswa_id, $status, $keterangan = '') {
        $query = "INSERT INTO " . $this->detail_table . "
                (absensi_id, siswa_id, status, keterangan)
                VALUES
                (:absensi_id, :siswa_id, :status, :keterangan)";

        $stmt = $this->conn->prepare($query);

        // Bind values
        $stmt->bindParam(":absensi_id", $this->id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":status", $status);
        $stmt->bindParam(":keterangan", $keterangan);

        return $stmt->execute();
    }

    public function getReportBySemester($kelas_id, $mapel_id, $tahun_ajaran_id) {
        // Get tahun ajaran data
        $tahunAjaran = new TahunAjaran();
        $ta_stmt = $tahunAjaran->getById($tahun_ajaran_id);
        $ta_data = $ta_stmt->fetch(PDO::FETCH_ASSOC);

        if (!$ta_data) {
            return false;
        }

        $query = "SELECT
                    s.nis,
                    s.nama_siswa,
                    k.nama_kelas,
                    m.nama_mapel,
                    COUNT(DISTINCT a.id) as total_pertemuan,
                    COUNT(CASE WHEN d.status = 'Hadir' THEN 1 END) as hadir,
                    COUNT(CASE WHEN d.status = 'Sakit' THEN 1 END) as sakit,
                    COUNT(CASE WHEN d.status = 'Izin' THEN 1 END) as izin,
                    COUNT(CASE WHEN d.status = 'Alpha' THEN 1 END) as alpha
                FROM siswa s
                JOIN kelas k ON s.kelas_id = k.id
                LEFT JOIN " . $this->detail_table . " d ON s.id = d.siswa_id
                LEFT JOIN " . $this->table_name . " a ON d.absensi_id = a.id
                    AND a.tanggal BETWEEN :semester_1_mulai AND :semester_2_selesai
                LEFT JOIN mata_pelajaran m ON a.mapel_id = m.id
                WHERE k.id = :kelas_id
                AND (a.mapel_id = :mapel_id OR a.mapel_id IS NULL)
                GROUP BY s.id, s.nis, s.nama_siswa, k.nama_kelas, m.nama_mapel
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);

        // Bind parameters
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester_1_mulai", $ta_data['semester_1_mulai']);
        $stmt->bindParam(":semester_2_selesai", $ta_data['semester_2_selesai']);

        $stmt->execute();
        return $stmt;
    }

    public function getCountByDate($date) {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . " WHERE DATE(tanggal) = :date";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":date", $date);
        $stmt->execute();

        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getByJadwalAndDate($jadwal_id, $tanggal) {
        // First get the jadwal details
        $query = "SELECT kelas_id, mapel_id FROM jadwal_pelajaran WHERE id = :jadwal_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":jadwal_id", $jadwal_id);
        $stmt->execute();

        if($jadwal = $stmt->fetch(PDO::FETCH_ASSOC)) {
            // Then check if absensi exists for this class, subject and date
            $query = "SELECT id FROM " . $this->table_name . "
                    WHERE kelas_id = :kelas_id
                    AND mapel_id = :mapel_id
                    AND tanggal = :tanggal
                    LIMIT 1";

            $stmt = $this->conn->prepare($query);
            $stmt->bindParam(":kelas_id", $jadwal['kelas_id']);
            $stmt->bindParam(":mapel_id", $jadwal['mapel_id']);
            $stmt->bindParam(":tanggal", $tanggal);
            $stmt->execute();

            if($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                return $row['id'];
            }
        }
        return false;
    }

    public function getStatusByJadwalId($jadwal_id, $tanggal) {
        // First get the kelas_id and mapel_id from jadwal
        $query = "SELECT kelas_id, mapel_id FROM jadwal_pelajaran WHERE id = :jadwal_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":jadwal_id", $jadwal_id);
        $stmt->execute();
        $jadwal = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$jadwal) {
            return false;
        }

        // Then check if there's an absensi record for this class, subject and date
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name . "
                 WHERE kelas_id = :kelas_id
                 AND mapel_id = :mapel_id
                 AND tanggal = :tanggal";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $jadwal['kelas_id']);
        $stmt->bindParam(":mapel_id", $jadwal['mapel_id']);
        $stmt->bindParam(":tanggal", $tanggal);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'] > 0;
    }

    public function getAbsensiId($kelas_id, $mapel_id, $tanggal) {
        $query = "SELECT id FROM " . $this->table_name . "
                 WHERE kelas_id = :kelas_id
                 AND mapel_id = :mapel_id
                 AND tanggal = :tanggal
                 LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":tanggal", $tanggal);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row ? $row['id'] : null;
    }

    public function getDetails($absensi_id) {
        $query = "SELECT da.*, s.nis, s.nama_siswa, da.status as status_absensi
                FROM " . $this->detail_table . " da
                JOIN siswa s ON da.siswa_id = s.id
                WHERE da.absensi_id = :absensi_id
                ORDER BY s.nama_siswa ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":absensi_id", $absensi_id);
        $stmt->execute();
        return $stmt;
    }

    public function getDetailSummary($absensi_id) {
        $query = "SELECT
                    COUNT(CASE WHEN status = 'Hadir' THEN 1 END) as hadir,
                    COUNT(CASE WHEN status = 'Sakit' THEN 1 END) as sakit,
                    COUNT(CASE WHEN status = 'Izin' THEN 1 END) as izin,
                    COUNT(CASE WHEN status = 'Alpha' THEN 1 END) as alpha,
                    COUNT(*) as total
                FROM " . $this->detail_table . "
                WHERE absensi_id = :absensi_id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":absensi_id", $absensi_id);
        $stmt->execute();
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getMissingDates($jadwal_id, $periode_aktif_id) {
        $query = "SELECT DISTINCT dates.date
                  FROM (
                      SELECT DATE(date_series.date) as date
                      FROM (
                          SELECT DATE_ADD(
                              (SELECT
                                  CASE
                                      WHEN pa.semester = 1 THEN ta.semester_1_mulai
                                      ELSE ta.semester_2_mulai
                                  END
                               FROM periode_aktif pa
                               JOIN tahun_ajaran ta ON pa.tahun_ajaran_id = ta.id
                               WHERE pa.id = :periode_aktif_id),
                              INTERVAL numbers.n DAY
                          ) as date
                          FROM (
                              SELECT a.N + b.N * 10 + c.N * 100 as n
                              FROM (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) a,
                                   (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) b,
                                   (SELECT 0 as N UNION SELECT 1 UNION SELECT 2 UNION SELECT 3 UNION SELECT 4 UNION SELECT 5 UNION SELECT 6 UNION SELECT 7 UNION SELECT 8 UNION SELECT 9) c
                          ) numbers
                      ) date_series
                      WHERE date_series.date <= (
                          SELECT
                              CASE
                                  WHEN pa.semester = 1 THEN ta.semester_1_selesai
                                  ELSE ta.semester_2_selesai
                              END
                          FROM periode_aktif pa
                          JOIN tahun_ajaran ta ON pa.tahun_ajaran_id = ta.id
                          WHERE pa.id = :periode_aktif_id
                      )
                  ) dates
                  JOIN jadwal_pelajaran jp ON DAYOFWEEK(dates.date) = CASE jp.hari
                      WHEN 'Senin' THEN 2
                      WHEN 'Selasa' THEN 3
                      WHEN 'Rabu' THEN 4
                      WHEN 'Kamis' THEN 5
                      WHEN 'Jumat' THEN 6
                      WHEN 'Sabtu' THEN 7
                      WHEN 'Minggu' THEN 1
                  END
                  LEFT JOIN absensi a ON dates.date = a.tanggal
                      AND a.kelas_id = jp.kelas_id
                      AND a.mapel_id = jp.mapel_id
                  WHERE jp.id = :jadwal_id
                  AND dates.date <= CURRENT_DATE
                  AND a.id IS NULL
                  ORDER BY dates.date DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':jadwal_id', $jadwal_id);
        $stmt->bindParam(':periode_aktif_id', $periode_aktif_id);
        $stmt->execute();

        $dates = [];
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $dates[] = $row['date'];
        }

        return $dates;
    }

    public function getRekapBySiswa($siswa_id, $semester, $tahun_ajaran) {
        $query = "SELECT
                    SUM(CASE WHEN da.status = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
                    SUM(CASE WHEN da.status = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
                    SUM(CASE WHEN da.status = 'Izin' THEN 1 ELSE 0 END) as total_izin,
                    SUM(CASE WHEN da.status = 'Alpha' THEN 1 ELSE 0 END) as total_alpha,
                    COUNT(da.id) as total_pertemuan
                FROM detail_absensi da
                JOIN absensi a ON da.absensi_id = a.id
                WHERE da.siswa_id = :siswa_id
                AND a.semester = :semester
                AND a.tahun_ajaran = :tahun_ajaran";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getRekapBySiswaPerMapel($siswa_id, $semester, $tahun_ajaran) {
        $query = "SELECT
                    a.mapel_id,
                    mp.nama_mapel,
                    SUM(CASE WHEN da.status = 'Hadir' THEN 1 ELSE 0 END) as total_hadir,
                    SUM(CASE WHEN da.status = 'Sakit' THEN 1 ELSE 0 END) as total_sakit,
                    SUM(CASE WHEN da.status = 'Izin' THEN 1 ELSE 0 END) as total_izin,
                    SUM(CASE WHEN da.status = 'Alpha' THEN 1 ELSE 0 END) as total_alpha,
                    COUNT(da.id) as total_pertemuan
                FROM detail_absensi da
                JOIN absensi a ON da.absensi_id = a.id
                JOIN mata_pelajaran mp ON a.mapel_id = mp.id
                WHERE da.siswa_id = :siswa_id
                AND a.semester = :semester
                AND a.tahun_ajaran = :tahun_ajaran
                GROUP BY a.mapel_id, mp.nama_mapel
                ORDER BY mp.nama_mapel ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function getDetailBySiswaMapel($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT
                    a.tanggal,
                    da.status,
                    da.keterangan
                FROM detail_absensi da
                JOIN absensi a ON da.absensi_id = a.id
                WHERE da.siswa_id = :siswa_id
                AND a.mapel_id = :mapel_id
                AND a.semester = :semester
                AND a.tahun_ajaran = :tahun_ajaran
                ORDER BY a.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':siswa_id', $siswa_id);
        $stmt->bindParam(':mapel_id', $mapel_id);
        $stmt->bindParam(':semester', $semester);
        $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    // Get students from classroom (new method for classroom-based system)
    public function getStudentsByRuangKelas($ruang_kelas_id) {
        $query = "SELECT s.id, s.nis, s.nama_siswa, s.jenis_kelamin
                FROM siswa s
                INNER JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id
                WHERE srk.ruang_kelas_id = :ruang_kelas_id
                AND srk.status = 'aktif'
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);
        $stmt->execute();
        return $stmt;
    }

    // Get classrooms for current period
    public function getActiveClassrooms() {
        $periode = new PeriodeAktif();
        if(!$periode->getActive()) {
            return false;
        }

        $query = "SELECT rk.id, rk.nama_ruang_kelas,
                         COUNT(srk.siswa_id) as jumlah_siswa
                FROM ruang_kelas rk
                LEFT JOIN siswa_ruang_kelas srk ON rk.id = srk.ruang_kelas_id AND srk.status = 'aktif'
                WHERE rk.semester = :semester
                AND rk.tahun_ajaran = :tahun_ajaran
                AND rk.status = 'aktif'
                GROUP BY rk.id, rk.nama_ruang_kelas
                ORDER BY rk.nama_ruang_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":semester", $periode->semester);
        $stmt->bindParam(":tahun_ajaran", $periode->tahun_ajaran);
        $stmt->execute();
        return $stmt;
    }

    // Check if classroom system is available
    public function isClassroomSystemAvailable() {
        try {
            $stmt = $this->conn->query("SHOW TABLES LIKE 'ruang_kelas'");
            return $stmt->rowCount() > 0;
        } catch (Exception $e) {
            return false;
        }
    }
}
