<?php
require_once __DIR__ . '/../config/database.php';

class Rpp {
    protected $conn;
    private $table_name = "rpp";

    public $id;
    public $mapel_id;
    public $guru_id;
    public $kelas_id;
    public $semester;
    public $tahun_ajaran;
    public $nama_sekolah;
    public $tema_subtema;
    public $materi_pokok;
    public $alokasi_waktu;
    public $tujuan_pembelajaran;
    public $kompetensi_dasar;
    public $indikator_pencapaian;
    public $materi_pembelajaran;
    public $metode_pembelajaran;
    public $media_pembelajaran;
    public $sumber_belajar;
    public $penilaian;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getAll() {
        $query = "SELECT r.*, m.nama_mapel, g.nama_lengkap as nama_guru, k.nama_kelas 
                 FROM " . $this->table_name . " r
                 LEFT JOIN mata_pelajaran m ON r.mapel_id = m.id
                 LEFT JOIN guru g ON r.guru_id = g.id
                 LEFT JOIN kelas k ON r.kelas_id = k.id
                 ORDER BY r.created_at DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getAllByGuru($guru_id) {
        $query = "SELECT r.*, m.nama_mapel, k.nama_kelas  
                FROM " . $this->table_name . " r
                LEFT JOIN mata_pelajaran m ON r.mapel_id = m.id
                LEFT JOIN kelas k ON r.kelas_id = k.id
                WHERE r.guru_id = :guru_id
                ORDER BY r.created_at DESC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(':guru_id', $guru_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function getOne($id) {
        $query = "SELECT r.*, m.nama_mapel, g.nama_lengkap as nama_guru, k.nama_kelas 
                 FROM " . $this->table_name . " r
                 LEFT JOIN mata_pelajaran m ON r.mapel_id = m.id
                 LEFT JOIN guru g ON r.guru_id = g.id
                 LEFT JOIN kelas k ON r.kelas_id = k.id
                 WHERE r.id = ?";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt;
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                mapel_id=:mapel_id, guru_id=:guru_id, kelas_id=:kelas_id,
                semester=:semester, tahun_ajaran=:tahun_ajaran,
                nama_sekolah=:nama_sekolah, tema_subtema=:tema_subtema,
                materi_pokok=:materi_pokok, alokasi_waktu=:alokasi_waktu,
                tujuan_pembelajaran=:tujuan_pembelajaran,
                kompetensi_dasar=:kompetensi_dasar,
                indikator_pencapaian=:indikator_pencapaian,
                materi_pembelajaran=:materi_pembelajaran,
                metode_pembelajaran=:metode_pembelajaran,
                media_pembelajaran=:media_pembelajaran,
                sumber_belajar=:sumber_belajar,
                penilaian=:penilaian";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->mapel_id = htmlspecialchars(strip_tags($this->sanitizeInput($this->mapel_id)));
        $this->guru_id = htmlspecialchars(strip_tags($this->sanitizeInput($this->guru_id)));
        $this->kelas_id = htmlspecialchars(strip_tags($this->sanitizeInput($this->kelas_id)));
        $this->semester = htmlspecialchars(strip_tags($this->sanitizeInput($this->semester)));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->tahun_ajaran)));
        $this->nama_sekolah = htmlspecialchars(strip_tags($this->sanitizeInput($this->nama_sekolah)));
        $this->tema_subtema = htmlspecialchars(strip_tags($this->sanitizeInput($this->tema_subtema)));
        $this->materi_pokok = htmlspecialchars(strip_tags($this->sanitizeInput($this->materi_pokok)));
        $this->alokasi_waktu = htmlspecialchars(strip_tags($this->sanitizeInput($this->alokasi_waktu)));
        $this->tujuan_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->tujuan_pembelajaran)));
        $this->kompetensi_dasar = htmlspecialchars(strip_tags($this->sanitizeInput($this->kompetensi_dasar)));
        $this->indikator_pencapaian = htmlspecialchars(strip_tags($this->sanitizeInput($this->indikator_pencapaian)));
        $this->materi_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->materi_pembelajaran)));
        $this->metode_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->metode_pembelajaran)));
        $this->media_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->media_pembelajaran)));
        $this->sumber_belajar = htmlspecialchars(strip_tags($this->sanitizeInput($this->sumber_belajar)));
        $this->penilaian = htmlspecialchars(strip_tags($this->sanitizeInput($this->penilaian)));

        // Bind values
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":nama_sekolah", $this->nama_sekolah);
        $stmt->bindParam(":tema_subtema", $this->tema_subtema);
        $stmt->bindParam(":materi_pokok", $this->materi_pokok);
        $stmt->bindParam(":alokasi_waktu", $this->alokasi_waktu);
        $stmt->bindParam(":tujuan_pembelajaran", $this->tujuan_pembelajaran);
        $stmt->bindParam(":kompetensi_dasar", $this->kompetensi_dasar);
        $stmt->bindParam(":indikator_pencapaian", $this->indikator_pencapaian);
        $stmt->bindParam(":materi_pembelajaran", $this->materi_pembelajaran);
        $stmt->bindParam(":metode_pembelajaran", $this->metode_pembelajaran);
        $stmt->bindParam(":media_pembelajaran", $this->media_pembelajaran);
        $stmt->bindParam(":sumber_belajar", $this->sumber_belajar);
        $stmt->bindParam(":penilaian", $this->penilaian);

        if($stmt->execute()) {
            return $this->conn->lastInsertId();
        }
        return false;
    }

    public function exportPDF($id) {
        require_once __DIR__ . '/../vendor/autoload.php';
        $dompdf = new \Dompdf\Dompdf();
        
        // Get RPP data
        $query = "SELECT r.*, m.nama_mapel, g.nama_lengkap as nama_guru, k.nama_kelas 
                 FROM " . $this->table_name . " r
                 LEFT JOIN mata_pelajaran m ON r.mapel_id = m.id
                 LEFT JOIN guru g ON r.guru_id = g.id
                 LEFT JOIN kelas k ON r.kelas_id = k.id
                 WHERE r.id = ?";
        
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        $rpp = $stmt->fetch(PDO::FETCH_ASSOC);

        // Get kegiatan pembelajaran
        $query = "SELECT * FROM rpp_kegiatan WHERE rpp_id = ? ORDER BY urutan ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        $kegiatan = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // Generate HTML
        $html = $this->generateRppHtml($rpp, $kegiatan);
        
        $dompdf->loadHtml($html);
        $dompdf->setPaper('A4', 'portrait');
        $dompdf->render();
        
        return $dompdf->output();
    }

    public function getById($id) {
        $query = "SELECT r.*, m.nama_mapel, g.nama_lengkap as nama_guru, k.nama_kelas
                 FROM " . $this->table_name . " r
                 LEFT JOIN mata_pelajaran m ON r.mapel_id = m.id
                 LEFT JOIN guru g ON r.guru_id = g.id
                 LEFT JOIN kelas k ON r.kelas_id = k.id
                 WHERE r.id = ?";

        $stmt = $this->conn->prepare($query);
        $stmt->execute([$id]);
        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . " SET
                mapel_id=:mapel_id, guru_id=:guru_id, kelas_id=:kelas_id,
                semester=:semester, tahun_ajaran=:tahun_ajaran,
                nama_sekolah=:nama_sekolah, tema_subtema=:tema_subtema,
                materi_pokok=:materi_pokok, alokasi_waktu=:alokasi_waktu,
                tujuan_pembelajaran=:tujuan_pembelajaran,
                kompetensi_dasar=:kompetensi_dasar,
                indikator_pencapaian=:indikator_pencapaian,
                materi_pembelajaran=:materi_pembelajaran,
                metode_pembelajaran=:metode_pembelajaran,
                media_pembelajaran=:media_pembelajaran,
                sumber_belajar=:sumber_belajar,
                penilaian=:penilaian
                WHERE id=:id";
    
        $stmt = $this->conn->prepare($query);
        
        // Sanitize input
        $this->mapel_id = htmlspecialchars(strip_tags($this->sanitizeInput($this->mapel_id)));
        $this->guru_id = htmlspecialchars(strip_tags($this->sanitizeInput($this->guru_id)));
        $this->kelas_id = htmlspecialchars(strip_tags($this->sanitizeInput($this->kelas_id)));
        $this->semester = htmlspecialchars(strip_tags($this->sanitizeInput($this->semester)));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->tahun_ajaran)));
        $this->nama_sekolah = htmlspecialchars(strip_tags($this->sanitizeInput($this->nama_sekolah)));
        $this->tema_subtema = htmlspecialchars(strip_tags($this->sanitizeInput($this->tema_subtema)));
        $this->materi_pokok = htmlspecialchars(strip_tags($this->sanitizeInput($this->materi_pokok)));
        $this->alokasi_waktu = htmlspecialchars(strip_tags($this->sanitizeInput($this->alokasi_waktu)));
        $this->tujuan_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->tujuan_pembelajaran)));
        $this->kompetensi_dasar = htmlspecialchars(strip_tags($this->sanitizeInput($this->kompetensi_dasar)));
        $this->indikator_pencapaian = htmlspecialchars(strip_tags($this->sanitizeInput($this->indikator_pencapaian)));
        $this->materi_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->materi_pembelajaran)));
        $this->metode_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->metode_pembelajaran)));
        $this->media_pembelajaran = htmlspecialchars(strip_tags($this->sanitizeInput($this->media_pembelajaran)));
        $this->sumber_belajar = htmlspecialchars(strip_tags($this->sanitizeInput($this->sumber_belajar)));
        $this->penilaian = htmlspecialchars(strip_tags($this->sanitizeInput($this->penilaian)));
        
        // Bind values
        $stmt->bindParam(":id", $this->id);
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":guru_id", $this->guru_id);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":nama_sekolah", $this->nama_sekolah);
        $stmt->bindParam(":tema_subtema", $this->tema_subtema);
        $stmt->bindParam(":materi_pokok", $this->materi_pokok);
        $stmt->bindParam(":alokasi_waktu", $this->alokasi_waktu);
        $stmt->bindParam(":tujuan_pembelajaran", $this->tujuan_pembelajaran);
        $stmt->bindParam(":kompetensi_dasar", $this->kompetensi_dasar);
        $stmt->bindParam(":indikator_pencapaian", $this->indikator_pencapaian);
        $stmt->bindParam(":materi_pembelajaran", $this->materi_pembelajaran);
        $stmt->bindParam(":metode_pembelajaran", $this->metode_pembelajaran);
        $stmt->bindParam(":media_pembelajaran", $this->media_pembelajaran);
        $stmt->bindParam(":sumber_belajar", $this->sumber_belajar);
        $stmt->bindParam(":penilaian", $this->penilaian);
        
        return $stmt->execute();
    }
    
    public function delete($id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$id]);
    }

    private function sanitizeInput($input) {
        // Handle array inputs by converting to string or taking first value
        if (is_array($input)) {
            if (empty($input)) {
                return '';
            }
            // For arrays, take the first value or join with comma
            return is_string($input[0]) ? $input[0] : '';
        }

        // Handle null or non-string inputs
        if ($input === null) {
            return '';
        }

        // Convert to string if not already
        return (string) $input;
    }

    private function generateRppHtml($rpp, $kegiatan) {
        // Generate HTML template for RPP
        $html = '
        <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; }
                .header { text-align: center; margin-bottom: 20px; }
                .content { margin: 20px; }
                table { width: 100%; border-collapse: collapse; }
                td, th { border: 1px solid #000; padding: 8px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h2>RENCANA PELAKSANAAN PEMBELAJARAN (RPP)</h2>
            </div>
            <div class="content">
                <!-- Add RPP content here -->
            </div>
        </body>
        </html>';

        return $html;
    }
}