<?php
require_once __DIR__ . '/../config/database.php';

class RppKegiatan {
    private $conn;
    private $table_name = "rpp_kegiatan";

    public $id;
    public $rpp_id;
    public $jenis_kegiatan;
    public $deskripsi;
    public $urutan;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function getByRppId($rpp_id) {
        $query = "SELECT * FROM " . $this->table_name . " 
                 WHERE rpp_id = :rpp_id 
                 ORDER BY urutan ASC";
        
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":rpp_id", $rpp_id);
        $stmt->execute();
        
        return $stmt;
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . " SET
                rpp_id=:rpp_id, jenis_kegiatan=:jenis_kegiatan,
                deskripsi=:deskripsi, urutan=:urutan";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->rpp_id = htmlspecialchars(strip_tags($this->rpp_id));
        $this->jenis_kegiatan = htmlspecialchars(strip_tags($this->jenis_kegiatan));
        $this->deskripsi = htmlspecialchars(strip_tags($this->deskripsi));
        $this->urutan = htmlspecialchars(strip_tags($this->urutan));

        // Bind
        $stmt->bindParam(":rpp_id", $this->rpp_id);
        $stmt->bindParam(":jenis_kegiatan", $this->jenis_kegiatan);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":urutan", $this->urutan);

        if($stmt->execute()) {
            return true;
        }
        return false;
    }

    public function deleteByRppId($rpp_id) {
        $query = "DELETE FROM " . $this->table_name . " WHERE rpp_id = ?";
        $stmt = $this->conn->prepare($query);
        return $stmt->execute([$rpp_id]);
    }
}