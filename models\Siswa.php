<?php
require_once __DIR__ . '/../config/database.php';

class Siswa {
    private $conn;
    private $table_name = "siswa";

    public $id;
    public $nis;
    public $nama_siswa;
    public $jenis_kelamin;
    public $kelas_id;
    public $nama_kelas;
    public $alamat;
    public $no_telp;
    public $created_at;
    public $updated_at;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    // Get total count of students
    public function getCount() {
        $query = "SELECT COUNT(*) as total FROM " . $this->table_name;
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['total'];
    }

    public function getAll() {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                ORDER BY CAST(s.nis AS UNSIGNED) ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getByKelas($kelas_id) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.kelas_id = :kelas_id
                ORDER BY nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();

        return $stmt;
    }

    public function getSiswaById($id) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.id = :id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByKelasOld($kelas_id) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.kelas_id = :kelas_id
                ORDER BY CAST(s.nis AS UNSIGNED) ASC";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":kelas_id", $kelas_id);
        $stmt->execute();
        return $stmt;
    }

    public function create() {
        $query = "INSERT INTO " . $this->table_name . "
                (nis, nama_siswa, jenis_kelamin, kelas_id, alamat, no_telp)
                VALUES (:nis, :nama_siswa, :jenis_kelamin, :kelas_id, :alamat, :no_telp)";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nis = htmlspecialchars(strip_tags($this->nis));
        $this->nama_siswa = htmlspecialchars(strip_tags($this->nama_siswa));
        $this->jenis_kelamin = htmlspecialchars(strip_tags($this->jenis_kelamin));
        $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
        $this->alamat = htmlspecialchars(strip_tags($this->alamat));
        $this->no_telp = htmlspecialchars(strip_tags($this->no_telp));

        // Bind parameters
        $stmt->bindParam(":nis", $this->nis);
        $stmt->bindParam(":nama_siswa", $this->nama_siswa);
        $stmt->bindParam(":jenis_kelamin", $this->jenis_kelamin);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":alamat", $this->alamat);
        $stmt->bindParam(":no_telp", $this->no_telp);

        return $stmt->execute();
    }

    public function getOne() {
        $data = $this->getSiswaById($this->id);
        if ($data) {
            $this->nis = $data['nis'];
            $this->nama_siswa = $data['nama_siswa'];
            $this->jenis_kelamin = $data['jenis_kelamin'];
            $this->kelas_id = $data['kelas_id'];
            $this->alamat = $data['alamat'];
            $this->no_telp = $data['no_telp'];
            $this->nama_kelas = $data['nama_kelas'];
            return true;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET nis = :nis,
                    nama_siswa = :nama_siswa,
                    jenis_kelamin = :jenis_kelamin,
                    kelas_id = :kelas_id,
                    alamat = :alamat,
                    no_telp = :no_telp
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Sanitize input
        $this->nis = htmlspecialchars(strip_tags($this->nis));
        $this->nama_siswa = htmlspecialchars(strip_tags($this->nama_siswa));
        $this->jenis_kelamin = htmlspecialchars(strip_tags($this->jenis_kelamin));
        $this->kelas_id = htmlspecialchars(strip_tags($this->kelas_id));
        $this->alamat = htmlspecialchars(strip_tags($this->alamat));
        $this->no_telp = htmlspecialchars(strip_tags($this->no_telp));
        $this->id = htmlspecialchars(strip_tags($this->id));

        // Bind parameters
        $stmt->bindParam(":nis", $this->nis);
        $stmt->bindParam(":nama_siswa", $this->nama_siswa);
        $stmt->bindParam(":jenis_kelamin", $this->jenis_kelamin);
        $stmt->bindParam(":kelas_id", $this->kelas_id);
        $stmt->bindParam(":alamat", $this->alamat);
        $stmt->bindParam(":no_telp", $this->no_telp);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);

        $this->id = htmlspecialchars(strip_tags($this->id));
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function getByNIS($nis) {
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.nis = :nis";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nis", $nis);
        $stmt->execute();

        return $stmt->fetch(PDO::FETCH_ASSOC);
    }

    public function getByNama($nama) {
        $search_term = "%$nama%";
        $query = "SELECT s.*, k.nama_kelas
                FROM " . $this->table_name . " s
                LEFT JOIN kelas k ON s.kelas_id = k.id
                WHERE s.nama_siswa LIKE :nama
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":nama", $search_term);
        $stmt->execute();

        return $stmt;
    }

    public function importFromArray($data) {
        $query = "INSERT INTO " . $this->table_name . "
                (nis, nama_siswa, jenis_kelamin, kelas_id, alamat, no_telp)
                VALUES (:nis, :nama_siswa, :jenis_kelamin, :kelas_id, :alamat, :no_telp)";

        $stmt = $this->conn->prepare($query);
        $success = true;

        foreach($data as $row) {
            // Sanitize input
            $nis = htmlspecialchars(strip_tags($row['nis']));
            $nama_siswa = htmlspecialchars(strip_tags($row['nama_siswa']));
            $jenis_kelamin = htmlspecialchars(strip_tags($row['jenis_kelamin']));
            $kelas_id = htmlspecialchars(strip_tags($row['kelas_id']));
            $alamat = htmlspecialchars(strip_tags($row['alamat']));
            $no_telp = htmlspecialchars(strip_tags($row['no_telp']));

            // Bind parameters
            $stmt->bindParam(":nis", $nis);
            $stmt->bindParam(":nama_siswa", $nama_siswa);
            $stmt->bindParam(":jenis_kelamin", $jenis_kelamin);
            $stmt->bindParam(":kelas_id", $kelas_id);
            $stmt->bindParam(":alamat", $alamat);
            $stmt->bindParam(":no_telp", $no_telp);

            if(!$stmt->execute()) {
                $success = false;
                break;
            }
        }
        return $success;
    }
}
