<?php
require_once __DIR__ . '/../config/database.php';

class TahunAjaran {
    private $conn;
    private $table_name = "tahun_ajaran";

    public $id;
    public $tahun_ajaran;
    public $semester_1_mulai;
    public $semester_1_selesai;
    public $semester_2_mulai;
    public $semester_2_selesai;
    public $is_active;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        // Validate format
        if(!preg_match('/^[0-9]{4}\/[0-9]{4}$/', $this->tahun_ajaran)) {
            return false;
        }

        // Validate consecutive years
        list($year1, $year2) = explode('/', $this->tahun_ajaran);
        if($year2 != $year1 + 1) {
            return false;
        }

        // Validate dates
        if(strtotime($this->semester_1_selesai) <= strtotime($this->semester_1_mulai) ||
           strtotime($this->semester_2_selesai) <= strtotime($this->semester_2_mulai) ||
           strtotime($this->semester_2_mulai) <= strtotime($this->semester_1_selesai)) {
            return false;
        }

        $query = "INSERT INTO " . $this->table_name . "
                (tahun_ajaran, semester_1_mulai, semester_1_selesai,
                 semester_2_mulai, semester_2_selesai)
                VALUES (:tahun_ajaran, :semester_1_mulai, :semester_1_selesai,
                        :semester_2_mulai, :semester_2_selesai)";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));
        $this->semester_1_mulai = htmlspecialchars(strip_tags($this->semester_1_mulai));
        $this->semester_1_selesai = htmlspecialchars(strip_tags($this->semester_1_selesai));
        $this->semester_2_mulai = htmlspecialchars(strip_tags($this->semester_2_mulai));
        $this->semester_2_selesai = htmlspecialchars(strip_tags($this->semester_2_selesai));

        // Bind values
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);
        $stmt->bindParam(":semester_1_mulai", $this->semester_1_mulai);
        $stmt->bindParam(":semester_1_selesai", $this->semester_1_selesai);
        $stmt->bindParam(":semester_2_mulai", $this->semester_2_mulai);
        $stmt->bindParam(":semester_2_selesai", $this->semester_2_selesai);

        return $stmt->execute();
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        return $stmt->execute();
    }

    public function getAll() {
        $query = "SELECT * FROM " . $this->table_name . " ORDER BY semester_1_mulai DESC";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();
        return $stmt;
    }

    public function getAllAsArray() {
        $stmt = $this->getAll();
        $result = [];
        while($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            $result[] = $row;
        }
        return $result;
    }

    public function getById($id) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $id);
        $stmt->execute();
        return $stmt;
    }

    public function exists($tahun_ajaran) {
        $query = "SELECT COUNT(*) as count FROM " . $this->table_name . " WHERE tahun_ajaran = :tahun_ajaran";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['count'] > 0;
    }

    public function getDefaultDates($tahun_awal) {
        $tahun_akhir = $tahun_awal + 1;
        return [
            'semester_1_mulai' => $tahun_awal . '-07-01',    // 1 Juli
            'semester_1_selesai' => $tahun_awal . '-12-31',  // 31 Desember
            'semester_2_mulai' => $tahun_akhir . '-01-01',   // 1 Januari
            'semester_2_selesai' => $tahun_akhir . '-06-30'  // 30 Juni
        ];
    }

    public function formatDate($date) {
        return date('d F Y', strtotime($date));
    }

    public function getSemesterPeriod($semester, $row) {
        if($semester == 1) {
            return $this->formatDate($row['semester_1_mulai']) . ' s/d ' .
                   $this->formatDate($row['semester_1_selesai']);
        } else {
            return $this->formatDate($row['semester_2_mulai']) . ' s/d ' .
                   $this->formatDate($row['semester_2_selesai']);
        }
    }

    public function getByTahunAjaran($tahun_ajaran) {
        $query = "SELECT * FROM " . $this->table_name . " WHERE tahun_ajaran = :tahun_ajaran";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();
        return $stmt;
    }

    public function getActive() {
        // First try to get from periode_aktif
        $query = "SELECT ta.tahun_ajaran
                FROM periode_aktif pa
                JOIN tahun_ajaran ta ON pa.tahun_ajaran_id = ta.id
                WHERE pa.is_active = 1
                LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            return $row['tahun_ajaran'];
        }

        // If no active period, get the latest tahun_ajaran
        $query = "SELECT tahun_ajaran FROM " . $this->table_name . " ORDER BY semester_1_mulai DESC LIMIT 1";
        $stmt = $this->conn->prepare($query);
        $stmt->execute();

        if ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            return $row['tahun_ajaran'];
        }

        // If no tahun ajaran exists, return current year/next year
        $currentYear = date('Y');
        return $currentYear . '/' . ($currentYear + 1);
    }
}
