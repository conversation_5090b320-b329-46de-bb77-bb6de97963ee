<?php
require_once __DIR__ . '/../config/database.php';

class Tugas {
    private $conn;
    private $table_name = "tugas";
    private $nilai_table = "nilai_tugas";

    public $id;
    public $mapel_id;
    public $ruang_kelas_id; // Primary classroom reference
    public $judul;
    public $deskripsi;
    public $tanggal;
    public $semester;
    public $tahun_ajaran;

    public function __construct() {
        $database = new Database();
        $this->conn = $database->getConnection();
    }

    public function create() {
        // Validate required fields
        if (empty($this->mapel_id) || empty($this->judul) || empty($this->tanggal) || empty($this->ruang_kelas_id)) {
            return false;
        }

        // Set default semester and tahun_ajaran if not provided
        if (empty($this->semester)) {
            $this->semester = "1"; // Default semester
        }
        if (empty($this->tahun_ajaran)) {
            $current_year = date('Y');
            $this->tahun_ajaran = $current_year . '/' . ($current_year + 1); // Default tahun ajaran
        }

        // Validate foreign key references exist
        try {
            // Check if ruang_kelas_id exists
            $check_query = "SELECT id, nama_ruang_kelas, status FROM ruang_kelas WHERE id = :ruang_kelas_id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(":ruang_kelas_id", $this->ruang_kelas_id);
            $check_stmt->execute();
            $classroom = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$classroom) {
                error_log("Tugas validation error: ruang_kelas_id " . $this->ruang_kelas_id . " not found");
                $_SESSION['debug_error'] = "Ruang kelas dengan ID " . $this->ruang_kelas_id . " tidak ditemukan di database.";
                return false;
            }

            if ($classroom['status'] !== 'aktif') {
                error_log("Tugas validation error: ruang_kelas_id " . $this->ruang_kelas_id . " is not active (status: " . $classroom['status'] . ")");
                $_SESSION['debug_error'] = "Ruang kelas '" . $classroom['nama_ruang_kelas'] . "' tidak aktif (status: " . $classroom['status'] . ").";
                return false;
            }

            // Check if mapel_id exists
            $check_query = "SELECT id, nama_mapel FROM mata_pelajaran WHERE id = :mapel_id";
            $check_stmt = $this->conn->prepare($check_query);
            $check_stmt->bindParam(":mapel_id", $this->mapel_id);
            $check_stmt->execute();
            $subject = $check_stmt->fetch(PDO::FETCH_ASSOC);

            if (!$subject) {
                error_log("Tugas validation error: mapel_id " . $this->mapel_id . " not found");
                $_SESSION['debug_error'] = "Mata pelajaran dengan ID " . $this->mapel_id . " tidak ditemukan di database.";
                return false;
            }

        } catch (PDOException $e) {
            error_log("Tugas validation error: " . $e->getMessage());
            $_SESSION['debug_error'] = "Error validasi database: " . $e->getMessage();
            return false;
        }

        // Insert using ruang_kelas_id (new classroom system)
        $query = "INSERT INTO " . $this->table_name . "
                (mapel_id, ruang_kelas_id, judul, deskripsi, tanggal, semester, tahun_ajaran)
                VALUES
                (:mapel_id, :ruang_kelas_id, :judul, :deskripsi, :tanggal, :semester, :tahun_ajaran)";

        $stmt = $this->conn->prepare($query);

        // Sanitize
        $this->mapel_id = htmlspecialchars(strip_tags($this->mapel_id));
        $this->ruang_kelas_id = htmlspecialchars(strip_tags($this->ruang_kelas_id));
        $this->judul = htmlspecialchars(strip_tags($this->judul));
        $this->deskripsi = htmlspecialchars(strip_tags($this->deskripsi));
        $this->tanggal = htmlspecialchars(strip_tags($this->tanggal));
        $this->semester = htmlspecialchars(strip_tags($this->semester));
        $this->tahun_ajaran = htmlspecialchars(strip_tags($this->tahun_ajaran));

        // Bind values
        $stmt->bindParam(":mapel_id", $this->mapel_id);
        $stmt->bindParam(":ruang_kelas_id", $this->ruang_kelas_id);
        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":tanggal", $this->tanggal);
        $stmt->bindParam(":semester", $this->semester);
        $stmt->bindParam(":tahun_ajaran", $this->tahun_ajaran);

        try {
            if($stmt->execute()) {
                $this->id = $this->conn->lastInsertId();
                return true;
            }
        } catch (PDOException $e) {
            // Log detailed error for debugging
            $error_msg = "Tugas create error: " . $e->getMessage();
            $error_msg .= " | Data: mapel_id=" . $this->mapel_id;
            $error_msg .= ", ruang_kelas_id=" . $this->ruang_kelas_id;
            $error_msg .= ", semester=" . $this->semester;
            $error_msg .= ", tahun_ajaran=" . $this->tahun_ajaran;
            error_log($error_msg);

            // Store specific error message for user
            if (strpos($e->getMessage(), 'fk_tugas_ruang_kelas') !== false) {
                $_SESSION['debug_error'] = "Ruang kelas yang dipilih tidak valid atau tidak ditemukan.";
            } elseif (strpos($e->getMessage(), 'tugas_ibfk_1') !== false) {
                $_SESSION['debug_error'] = "Mata pelajaran yang dipilih tidak valid atau tidak ditemukan.";
            } elseif (strpos($e->getMessage(), 'foreign key constraint') !== false) {
                $_SESSION['debug_error'] = "Data yang dipilih tidak valid. Pastikan ruang kelas dan mata pelajaran sudah tersedia di database.";
            } else {
                $_SESSION['debug_error'] = $e->getMessage();
            }
            return false;
        }
        return false;
    }

    public function update() {
        $query = "UPDATE " . $this->table_name . "
                SET judul = :judul,
                    deskripsi = :deskripsi,
                    tanggal = :tanggal
                WHERE id = :id";

        $stmt = $this->conn->prepare($query);

        // Bind values
        $stmt->bindParam(":judul", $this->judul);
        $stmt->bindParam(":deskripsi", $this->deskripsi);
        $stmt->bindParam(":tanggal", $this->tanggal);
        $stmt->bindParam(":id", $this->id);

        return $stmt->execute();
    }

    public function delete() {
        $query = "DELETE FROM " . $this->table_name . " WHERE id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        return $stmt->execute();
    }

    public function getOne() {
        $query = "SELECT t.*, rk.nama_ruang_kelas, rk.semester, rk.tahun_ajaran
                FROM " . $this->table_name . " t
                INNER JOIN ruang_kelas rk ON t.ruang_kelas_id = rk.id
                WHERE t.id = :id";
        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":id", $this->id);
        $stmt->execute();
        $row = $stmt->fetch(PDO::FETCH_ASSOC);

        if($row) {
            $this->mapel_id = $row['mapel_id'];
            $this->ruang_kelas_id = $row['ruang_kelas_id'];
            $this->judul = $row['judul'];
            $this->deskripsi = $row['deskripsi'];
            $this->tanggal = $row['tanggal'];
            $this->semester = $row['semester'];
            $this->tahun_ajaran = $row['tahun_ajaran'];
            return true;
        }
        return false;
    }

    public function getTugasMapel($mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT t.*, rk.nama_ruang_kelas as nama_kelas
                FROM " . $this->table_name . " t
                INNER JOIN ruang_kelas rk ON t.ruang_kelas_id = rk.id
                WHERE t.mapel_id = :mapel_id
                AND t.semester = :semester
                AND t.tahun_ajaran = :tahun_ajaran
                AND rk.status = 'aktif'
                ORDER BY t.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function getTugasMapelRuangKelas($mapel_id, $ruang_kelas_id, $semester, $tahun_ajaran) {
        $query = "SELECT t.*, rk.nama_ruang_kelas as nama_kelas
                FROM " . $this->table_name . " t
                INNER JOIN ruang_kelas rk ON t.ruang_kelas_id = rk.id
                WHERE t.mapel_id = :mapel_id
                AND t.ruang_kelas_id = :ruang_kelas_id
                AND t.semester = :semester
                AND t.tahun_ajaran = :tahun_ajaran
                AND rk.status = 'aktif'
                ORDER BY t.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    public function addNilai($tugas_id, $siswa_id, $nilai) {
        $query = "INSERT INTO " . $this->nilai_table . "
                (tugas_id, siswa_id, nilai)
                VALUES (:tugas_id, :siswa_id, :nilai)
                ON DUPLICATE KEY UPDATE nilai = :nilai";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":nilai", $nilai);
        return $stmt->execute();
    }

    public function getNilaiTugas($tugas_id) {
        $query = "SELECT nt.*, s.nis, s.nama_siswa, rk.nama_ruang_kelas as nama_kelas
                FROM " . $this->nilai_table . " nt
                JOIN siswa s ON nt.siswa_id = s.id
                JOIN " . $this->table_name . " t ON nt.tugas_id = t.id
                JOIN ruang_kelas rk ON t.ruang_kelas_id = rk.id
                WHERE nt.tugas_id = :tugas_id
                ORDER BY rk.nama_ruang_kelas ASC, s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->execute();
        return $stmt;
    }

    public function getRataRataNilaiTugas($siswa_id, $mapel_id, $semester, $tahun_ajaran) {
        $query = "SELECT AVG(nt.nilai) as rata_rata
                FROM " . $this->nilai_table . " nt
                JOIN " . $this->table_name . " t ON nt.tugas_id = t.id
                WHERE t.mapel_id = :mapel_id 
                AND t.semester = :semester 
                AND t.tahun_ajaran = :tahun_ajaran
                AND nt.siswa_id = :siswa_id";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->execute();
        
        $row = $stmt->fetch(PDO::FETCH_ASSOC);
        return $row['rata_rata'] ? round($row['rata_rata'], 2) : 0;
    }

    public function getSiswaWithNilai($tugas_id) {
        $query = "SELECT s.id, s.nis, s.nama_siswa, nt.nilai
                FROM siswa s
                INNER JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id
                INNER JOIN " . $this->table_name . " t ON t.ruang_kelas_id = srk.ruang_kelas_id
                LEFT JOIN " . $this->nilai_table . " nt ON s.id = nt.siswa_id AND nt.tugas_id = :tugas_id
                WHERE t.id = :tugas_id AND srk.status = 'aktif'
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->execute();

        return $stmt;
    }

    public function updateNilai($tugas_id, $siswa_id, $nilai) {
        $query = "INSERT INTO " . $this->nilai_table . " 
                (tugas_id, siswa_id, nilai) 
                VALUES (:tugas_id, :siswa_id, :nilai)
                ON DUPLICATE KEY UPDATE nilai = :nilai";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->bindParam(":siswa_id", $siswa_id);
        $stmt->bindParam(":nilai", $nilai);

        return $stmt->execute();
    }

    // Get assignments for a classroom (new method for classroom-based system)
    public function getTugasRuangKelas($mapel_id, $ruang_kelas_id, $semester, $tahun_ajaran) {
        $query = "SELECT t.*, rk.nama_ruang_kelas
                FROM " . $this->table_name . " t
                LEFT JOIN ruang_kelas rk ON t.ruang_kelas_id = rk.id
                WHERE t.mapel_id = :mapel_id
                AND t.ruang_kelas_id = :ruang_kelas_id
                AND t.semester = :semester
                AND t.tahun_ajaran = :tahun_ajaran
                ORDER BY t.tanggal DESC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":mapel_id", $mapel_id);
        $stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();

        return $stmt;
    }

    // Get students from classroom for assignment
    public function getStudentsByRuangKelas($ruang_kelas_id) {
        $query = "SELECT s.id, s.nis, s.nama_siswa, s.jenis_kelamin
                FROM siswa s
                INNER JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id
                WHERE srk.ruang_kelas_id = :ruang_kelas_id
                AND srk.status = 'aktif'
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":ruang_kelas_id", $ruang_kelas_id);
        $stmt->execute();
        return $stmt;
    }

    // Get active classrooms for current period
    public function getActiveClassrooms($semester = null, $tahun_ajaran = null) {
        // If no semester/tahun_ajaran provided, try to get from active period
        if (!$semester || !$tahun_ajaran) {
            require_once __DIR__ . '/PeriodeAktif.php';
            $periode = new PeriodeAktif();
            if($periode->getActive()) {
                $semester = $periode->semester;
                $tahun_ajaran = $periode->tahun_ajaran;
            } else {
                // Default to current academic year if no active period
                $current_year = date('Y');
                $semester = (date('m') >= 7) ? '1' : '2';
                $tahun_ajaran = $current_year . '/' . ($current_year + 1);
            }
        }

        $query = "SELECT rk.id, rk.nama_ruang_kelas, rk.semester, rk.tahun_ajaran,
                         COUNT(srk.siswa_id) as jumlah_siswa
                FROM ruang_kelas rk
                LEFT JOIN siswa_ruang_kelas srk ON rk.id = srk.ruang_kelas_id AND srk.status = 'aktif'
                WHERE rk.semester = :semester
                AND rk.tahun_ajaran = :tahun_ajaran
                AND rk.status = 'aktif'
                GROUP BY rk.id, rk.nama_ruang_kelas, rk.semester, rk.tahun_ajaran
                ORDER BY rk.nama_ruang_kelas ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":semester", $semester);
        $stmt->bindParam(":tahun_ajaran", $tahun_ajaran);
        $stmt->execute();
        return $stmt;
    }

    // Classroom system is now the primary system
    public function isClassroomSystemAvailable() {
        return true; // Always true now - classroom system is mandatory
    }

    // Get assignment grades (classroom system)
    public function getNilaiTugasWithClassroom($tugas_id) {
        $query = "SELECT nt.*, s.nis, s.nama_siswa, rk.nama_ruang_kelas as nama_kelas
                FROM " . $this->nilai_table . " nt
                JOIN siswa s ON nt.siswa_id = s.id
                JOIN " . $this->table_name . " t ON nt.tugas_id = t.id
                JOIN ruang_kelas rk ON t.ruang_kelas_id = rk.id
                WHERE nt.tugas_id = :tugas_id
                ORDER BY rk.nama_ruang_kelas ASC, s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->execute();
        return $stmt;
    }

    // Get students with grades for classroom-based assignments
    public function getSiswaWithNilaiClassroom($tugas_id) {
        // Use classroom-based query (new system only)
        $query = "SELECT s.id, s.nis, s.nama_siswa, nt.nilai
                FROM siswa s
                INNER JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id
                INNER JOIN " . $this->table_name . " t ON t.ruang_kelas_id = srk.ruang_kelas_id
                LEFT JOIN " . $this->nilai_table . " nt ON s.id = nt.siswa_id AND nt.tugas_id = :tugas_id
                WHERE t.id = :tugas_id AND srk.status = 'aktif'
                ORDER BY s.nama_siswa ASC";

        $stmt = $this->conn->prepare($query);
        $stmt->bindParam(":tugas_id", $tugas_id);
        $stmt->execute();

        return $stmt;
    }
}
