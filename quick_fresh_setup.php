<?php
require_once 'config/database.php';

echo "<h1>Quick Fresh Database Setup</h1>";
echo "<p>Simple and safe setup for empty database.</p>";

$database = new Database();
$conn = $database->getConnection();

if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🚀 Quick Fresh Database Setup</h3>";
    echo "<p>This will create essential tables and sample data for immediate use.</p>";
    echo "<p><strong>Safe for empty database only!</strong></p>";
    echo "<p><a href='?confirm=yes' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Database</a></p>";
    echo "</div>";
    exit;
}

$step = 1;

function show_step($title, $success = true, $message = '') {
    global $step;
    $color = $success ? 'green' : 'red';
    $icon = $success ? '✅' : '❌';
    echo "<div style='color: $color; margin: 5px 0;'>$icon Step $step: $title";
    if ($message) echo " - $message";
    echo "</div>";
    $step++;
}

try {
    echo "<h2>Creating Database...</h2>";
    
    // Step 1: Core tables
    show_step("Creating core tables");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `mata_pelajaran` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `kode_mapel` varchar(10) NOT NULL,
      `nama_mapel` varchar(100) NOT NULL,
      `kkm` decimal(5,2) DEFAULT 75.00,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `kode_mapel` (`kode_mapel`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nama_ruang_kelas` varchar(100) NOT NULL,
      `semester` enum('1','2') NOT NULL,
      `tahun_ajaran` varchar(9) NOT NULL,
      `status` enum('aktif','nonaktif') DEFAULT 'aktif',
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `siswa` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nis` varchar(20) NOT NULL,
      `nama_siswa` varchar(100) NOT NULL,
      `jenis_kelamin` enum('L','P') NOT NULL,
      `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `nis` (`nis`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `siswa_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `tanggal_masuk` date NOT NULL,
      `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `siswa_id` (`siswa_id`),
      KEY `ruang_kelas_id` (`ruang_kelas_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    // Step 2: Learning tables
    show_step("Creating learning tables");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `mapel_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `judul` varchar(255) NOT NULL,
      `deskripsi` text DEFAULT NULL,
      `tanggal` date NOT NULL,
      `semester` enum('1','2') NOT NULL,
      `tahun_ajaran` varchar(9) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `mapel_id` (`mapel_id`),
      KEY `ruang_kelas_id` (`ruang_kelas_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tanggal` date NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `mapel_id` int(11) NOT NULL,
      `semester` enum('1','2') NOT NULL,
      `tahun_ajaran` varchar(9) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `ruang_kelas_id` (`ruang_kelas_id`),
      KEY `mapel_id` (`mapel_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `detail_absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `absensi_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `status` enum('hadir','sakit','izin','alpha') NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `absensi_id` (`absensi_id`),
      KEY `siswa_id` (`siswa_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `nilai_tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tugas_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `nilai` decimal(5,2) DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `tugas_id` (`tugas_id`),
      KEY `siswa_id` (`siswa_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    // Step 3: Sample data
    show_step("Inserting sample data");
    
    // Mata pelajaran
    $conn->exec("INSERT IGNORE INTO mata_pelajaran (kode_mapel, nama_mapel, kkm) VALUES 
        ('MTK001', 'Matematika', 75.00),
        ('BIN001', 'Bahasa Indonesia', 75.00),
        ('ING001', 'Bahasa Inggris', 75.00),
        ('FIS001', 'Fisika', 75.00),
        ('KIM001', 'Kimia', 75.00),
        ('BIO001', 'Biologi', 75.00)");
    
    // Ruang kelas
    $conn->exec("INSERT IGNORE INTO ruang_kelas (nama_ruang_kelas, semester, tahun_ajaran, status) VALUES 
        ('X IPA 1', '1', '2024/2025', 'aktif'),
        ('X IPA 2', '1', '2024/2025', 'aktif'),
        ('X IPS 1', '1', '2024/2025', 'aktif'),
        ('XI IPA 1', '1', '2024/2025', 'aktif'),
        ('XI IPA 2', '1', '2024/2025', 'aktif'),
        ('XI IPS 1', '1', '2024/2025', 'aktif'),
        ('XII IPA 1', '1', '2024/2025', 'aktif'),
        ('XII IPA 2', '1', '2024/2025', 'aktif'),
        ('XII IPS 1', '1', '2024/2025', 'aktif')");
    
    // Siswa
    $conn->exec("INSERT IGNORE INTO siswa (nis, nama_siswa, jenis_kelamin, status) VALUES 
        ('2024001', 'Andi Pratama', 'L', 'aktif'),
        ('2024002', 'Sari Dewi', 'P', 'aktif'),
        ('2024003', 'Budi Setiawan', 'L', 'aktif'),
        ('2024004', 'Rina Sari', 'P', 'aktif'),
        ('2024005', 'Doni Kurniawan', 'L', 'aktif'),
        ('2024006', 'Maya Putri', 'P', 'aktif'),
        ('2024007', 'Rudi Hartono', 'L', 'aktif'),
        ('2024008', 'Lina Marlina', 'P', 'aktif'),
        ('2024009', 'Agus Salim', 'L', 'aktif'),
        ('2024010', 'Fitri Handayani', 'P', 'aktif')");
    
    // Student assignments
    $conn->exec("INSERT IGNORE INTO siswa_ruang_kelas (siswa_id, ruang_kelas_id, tanggal_masuk, status) VALUES 
        (1, 1, '2024-07-01', 'aktif'),
        (2, 1, '2024-07-01', 'aktif'),
        (3, 1, '2024-07-01', 'aktif'),
        (4, 2, '2024-07-01', 'aktif'),
        (5, 2, '2024-07-01', 'aktif'),
        (6, 2, '2024-07-01', 'aktif'),
        (7, 3, '2024-07-01', 'aktif'),
        (8, 3, '2024-07-01', 'aktif'),
        (9, 4, '2024-07-01', 'aktif'),
        (10, 4, '2024-07-01', 'aktif')");
    
    // Step 4: Add foreign keys (optional, skip if error)
    show_step("Adding foreign key constraints");
    try {
        $conn->exec("ALTER TABLE tugas ADD CONSTRAINT IF NOT EXISTS fk_tugas_mapel FOREIGN KEY (mapel_id) REFERENCES mata_pelajaran(id)");
        $conn->exec("ALTER TABLE tugas ADD CONSTRAINT IF NOT EXISTS fk_tugas_ruang_kelas FOREIGN KEY (ruang_kelas_id) REFERENCES ruang_kelas(id)");
        $conn->exec("ALTER TABLE absensi ADD CONSTRAINT IF NOT EXISTS fk_absensi_mapel FOREIGN KEY (mapel_id) REFERENCES mata_pelajaran(id)");
        $conn->exec("ALTER TABLE absensi ADD CONSTRAINT IF NOT EXISTS fk_absensi_ruang_kelas FOREIGN KEY (ruang_kelas_id) REFERENCES ruang_kelas(id)");
        $conn->exec("ALTER TABLE siswa_ruang_kelas ADD CONSTRAINT IF NOT EXISTS fk_srk_siswa FOREIGN KEY (siswa_id) REFERENCES siswa(id)");
        $conn->exec("ALTER TABLE siswa_ruang_kelas ADD CONSTRAINT IF NOT EXISTS fk_srk_ruang_kelas FOREIGN KEY (ruang_kelas_id) REFERENCES ruang_kelas(id)");
    } catch (Exception $e) {
        show_step("Foreign key constraints", false, "Skipped - " . $e->getMessage());
    }
    
    // Step 5: Verification
    show_step("Verifying setup");
    
    $verification = [
        'Mata Pelajaran' => "SELECT COUNT(*) as count FROM mata_pelajaran",
        'Ruang Kelas' => "SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'",
        'Siswa' => "SELECT COUNT(*) as count FROM siswa WHERE status = 'aktif'",
        'Student Assignments' => "SELECT COUNT(*) as count FROM siswa_ruang_kelas WHERE status = 'aktif'"
    ];
    
    echo "<h3>Database Summary</h3>";
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Table</th><th style='border: 1px solid #ddd; padding: 10px;'>Records</th></tr>";
    
    $all_good = true;
    foreach ($verification as $name => $query) {
        $stmt = $conn->query($query);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        $color = $count > 0 ? 'green' : 'red';
        if ($count == 0) $all_good = false;
        echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>$name</td><td style='border: 1px solid #ddd; padding: 10px; color: $color;'>$count</td></tr>";
    }
    echo "</table>";
    
    if ($all_good) {
        echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #155724;'>🎉 Database Setup Complete!</h3>";
        echo "<p><strong>Your classroom system is ready!</strong></p>";
        echo "<p><strong>What's ready:</strong></p>";
        echo "<ul>";
        echo "<li>✅ 6 mata pelajaran</li>";
        echo "<li>✅ 9 ruang kelas aktif</li>";
        echo "<li>✅ 10 siswa sample</li>";
        echo "<li>✅ Student assignments</li>";
        echo "</ul>";
        echo "<p><strong>Test the system:</strong></p>";
        echo "<ol>";
        echo "<li><a href='tugas/index.php'>Create a new assignment (tugas)</a></li>";
        echo "<li><a href='absensi/create.php'>Take attendance (absensi)</a></li>";
        echo "<li><a href='debug_classroom_issue.php'>Run system diagnostics</a></li>";
        echo "</ol>";
        echo "</div>";
    } else {
        echo "<div style='background: #fff3cd; border: 1px solid #ffeaa7; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
        echo "<h3 style='color: #856404;'>⚠ Setup Partially Complete</h3>";
        echo "<p>Some tables have no data. This might be normal if you're starting fresh.</p>";
        echo "</div>";
    }
    
} catch (Exception $e) {
    show_step("Database setup", false, $e->getMessage());
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Setup Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "<p>Try running the SQL file manually in phpMyAdmin.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Quick Setup:</strong> Essential tables only, ready for immediate use!</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
