<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/RuangKelas.php';
require_once __DIR__ . '/../models/Siswa.php';

// Only admin can access this module
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

// Check if classroom ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "ID ruang kelas tidak valid.";
    header("Location: index.php");
    exit();
}

$ruang_kelas_id = $_GET['id'];

// Initialize models
$ruangKelasModel = new RuangKelas();
$siswaModel = new Siswa();

// Get classroom details
$classroom = $ruangKelasModel->getById($ruang_kelas_id);
if (!$classroom) {
    $_SESSION['error'] = "Ruang kelas tidak ditemukan.";
    header("Location: index.php");
    exit();
}

$message = '';
$error = '';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    if (isset($_POST['action'])) {
        if ($_POST['action'] == 'assign') {
            // Assign students to classroom
            $selected_students = $_POST['selected_students'] ?? [];
            $success_count = 0;
            $error_count = 0;
            
            foreach ($selected_students as $siswa_id) {
                if ($ruangKelasModel->assignStudent($siswa_id, $ruang_kelas_id)) {
                    $success_count++;
                } else {
                    $error_count++;
                }
            }
            
            if ($success_count > 0) {
                $message = "$success_count siswa berhasil ditambahkan ke ruang kelas.";
            }
            if ($error_count > 0) {
                $error = "$error_count siswa gagal ditambahkan (mungkin sudah terdaftar).";
            }
            
        } elseif ($_POST['action'] == 'remove') {
            // Remove student from classroom
            $siswa_id = $_POST['siswa_id'];
            $status = $_POST['status'] ?? 'pindah';
            $keterangan = $_POST['keterangan'] ?? '';
            
            if ($ruangKelasModel->removeStudent($siswa_id, $ruang_kelas_id, $status, $keterangan)) {
                $message = "Siswa berhasil dikeluarkan dari ruang kelas.";
            } else {
                $error = "Gagal mengeluarkan siswa dari ruang kelas.";
            }
        }
    }
}

// Get current students in classroom
$current_students = [];
$stmt = $ruangKelasModel->getStudents($ruang_kelas_id, 'aktif');
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $current_students[] = $row;
}

// Get available students (not assigned to any classroom in the same period)
$available_students = [];
$stmt = $ruangKelasModel->getAvailableStudents($classroom['semester'], $classroom['tahun_ajaran']);
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $available_students[] = $row;
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-users"></i> Kelola Siswa - <?= htmlspecialchars($classroom['nama_ruang_kelas']) ?></h2>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if ($message): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($message) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Classroom Info -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0">Informasi Ruang Kelas</h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-3">
                    <strong>Nama Ruang Kelas:</strong><br>
                    <?= htmlspecialchars($classroom['nama_ruang_kelas']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Periode:</strong><br>
                    Semester <?= $classroom['semester'] ?> - <?= htmlspecialchars($classroom['tahun_ajaran']) ?>
                </div>
                <div class="col-md-3">
                    <strong>Kapasitas:</strong><br>
                    <?= $classroom['jumlah_siswa'] ?> / <?= $classroom['kapasitas_maksimal'] ?> siswa
                </div>
                <div class="col-md-3">
                    <strong>Wali Kelas:</strong><br>
                    <?= htmlspecialchars($classroom['nama_guru_wali'] ?? '-') ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Current Students -->
        <div class="col-md-7">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users"></i> Siswa di Ruang Kelas (<?= count($current_students) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($current_students)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Belum ada siswa di ruang kelas ini</h6>
                            <p class="text-muted">Silakan tambahkan siswa dari daftar siswa yang tersedia.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-sm table-striped">
                                <thead>
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama Siswa</th>
                                        <th>Tanggal Masuk</th>
                                        <th>Aksi</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($current_students as $index => $student): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td><?= htmlspecialchars($student['nis']) ?></td>
                                            <td><?= htmlspecialchars($student['nama_siswa']) ?></td>
                                            <td><?= date('d/m/Y', strtotime($student['tanggal_masuk'])) ?></td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="removeStudent(<?= $student['id'] ?>, '<?= htmlspecialchars($student['nama_siswa']) ?>')">
                                                    <i class="fas fa-times"></i>
                                                </button>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Available Students -->
        <div class="col-md-5">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user-plus"></i> Siswa Tersedia (<?= count($available_students) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($available_students)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-user-check fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Semua siswa sudah terdaftar</h6>
                            <p class="text-muted">Tidak ada siswa yang tersedia untuk periode ini.</p>
                        </div>
                    <?php else: ?>
                        <form method="POST">
                            <input type="hidden" name="action" value="assign">
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="selectAll">
                                    <label class="form-check-label" for="selectAll">
                                        <strong>Pilih Semua</strong>
                                    </label>
                                </div>
                            </div>
                            
                            <div style="max-height: 400px; overflow-y: auto;">
                                <?php foreach ($available_students as $student): ?>
                                    <div class="form-check mb-2">
                                        <input class="form-check-input student-checkbox" type="checkbox" 
                                               name="selected_students[]" value="<?= $student['id'] ?>" 
                                               id="student_<?= $student['id'] ?>">
                                        <label class="form-check-label" for="student_<?= $student['id'] ?>">
                                            <strong><?= htmlspecialchars($student['nis']) ?></strong> - 
                                            <?= htmlspecialchars($student['nama_siswa']) ?>
                                        </label>
                                    </div>
                                <?php endforeach; ?>
                            </div>
                            
                            <div class="mt-3">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-plus"></i> Tambahkan Siswa Terpilih
                                </button>
                            </div>
                        </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Student Modal -->
<div class="modal fade" id="removeStudentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Keluarkan Siswa dari Ruang Kelas</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <div class="modal-body">
                    <input type="hidden" name="action" value="remove">
                    <input type="hidden" name="siswa_id" id="remove_siswa_id">
                    
                    <p>Apakah Anda yakin ingin mengeluarkan siswa <strong id="student_name"></strong> dari ruang kelas ini?</p>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">Status</label>
                        <select class="form-select" name="status" required>
                            <option value="pindah">Pindah Kelas</option>
                            <option value="keluar">Keluar Sekolah</option>
                            <option value="lulus">Lulus</option>
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="keterangan" class="form-label">Keterangan</label>
                        <textarea class="form-control" name="keterangan" rows="3" placeholder="Keterangan tambahan (opsional)"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-danger">Keluarkan Siswa</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Select all functionality
document.getElementById('selectAll').addEventListener('change', function() {
    const checkboxes = document.querySelectorAll('.student-checkbox');
    checkboxes.forEach(checkbox => {
        checkbox.checked = this.checked;
    });
});

// Remove student function
function removeStudent(siswaId, studentName) {
    document.getElementById('remove_siswa_id').value = siswaId;
    document.getElementById('student_name').textContent = studentName;
    
    const modal = new bootstrap.Modal(document.getElementById('removeStudentModal'));
    modal.show();
}
</script>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
