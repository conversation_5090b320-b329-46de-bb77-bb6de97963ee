<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/RuangKelas.php';
require_once __DIR__ . '/../models/PeriodeAktif.php';
require_once __DIR__ . '/../models/TahunAjaran.php';
require_once __DIR__ . '/../models/Tingkat.php';
require_once __DIR__ . '/../models/Jurusan.php';
require_once __DIR__ . '/../models/Guru.php';

// Only admin can access this module
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

// Initialize models
$ruangKelasModel = new RuangKelas();
$periodeModel = new PeriodeAktif();
$tahunAjaranModel = new TahunAjaran();
$tingkatModel = new Tingkat();
$jurusanModel = new Jurusan();
$guruModel = new Guru();

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $ruangKelasModel->nama_ruang_kelas = $_POST['nama_ruang_kelas'];
    $ruangKelasModel->deskripsi = $_POST['deskripsi'];
    $ruangKelasModel->semester = $_POST['semester'];
    $ruangKelasModel->tahun_ajaran = $_POST['tahun_ajaran'];
    $ruangKelasModel->tingkat_id = !empty($_POST['tingkat_id']) ? $_POST['tingkat_id'] : null;
    $ruangKelasModel->jurusan_id = !empty($_POST['jurusan_id']) ? $_POST['jurusan_id'] : null;
    $ruangKelasModel->guru_wali_id = !empty($_POST['guru_wali_id']) ? $_POST['guru_wali_id'] : null;
    $ruangKelasModel->kapasitas_maksimal = $_POST['kapasitas_maksimal'];
    $ruangKelasModel->status = $_POST['status'];

    if ($ruangKelasModel->create()) {
        $_SESSION['success'] = "Ruang kelas berhasil ditambahkan.";
        header("Location: index.php");
        exit();
    } else {
        $error = "Gagal menambahkan ruang kelas.";
    }
}

// Get active period for default values
$activePeriod = $periodeModel->getActive();
$default_semester = '1';
$default_tahun_ajaran = '';

if ($activePeriod && is_array($activePeriod)) {
    $default_semester = $activePeriod['semester'];
    $default_tahun_ajaran = $activePeriod['tahun_ajaran'];
} else {
    // Fallback: get the latest academic year if no active period
    $stmt = $tahunAjaranModel->getAll();
    if ($stmt && $stmt->rowCount() > 0) {
        $latest_ta = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($latest_ta) {
            $default_tahun_ajaran = $latest_ta['tahun_ajaran'];
        }
    }
}

// Get data for dropdowns
$tahun_ajaran_list = [];
$stmt = $tahunAjaranModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $tahun_ajaran_list[] = $row;
}

$tingkat_list = [];
$stmt = $tingkatModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $tingkat_list[] = $row;
}

$jurusan_list = [];
$stmt = $jurusanModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $jurusan_list[] = $row;
}

$guru_list = [];
$stmt = $guruModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    if ($row['status'] == 'aktif') {
        $guru_list[] = $row;
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-plus"></i> Tambah Ruang Kelas</h2>
        <a href="index.php" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Kembali
        </a>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi Ruang Kelas</h5>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="nama_ruang_kelas" class="form-label">Nama Ruang Kelas <span class="text-danger">*</span></label>
                                    <input type="text" class="form-control" id="nama_ruang_kelas" name="nama_ruang_kelas" 
                                           value="<?= htmlspecialchars($_POST['nama_ruang_kelas'] ?? '') ?>" required>
                                    <div class="form-text">Contoh: X IPA 1, XI TKJ A, XII MM 2</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="kapasitas_maksimal" class="form-label">Kapasitas Maksimal</label>
                                    <input type="number" class="form-control" id="kapasitas_maksimal" name="kapasitas_maksimal" 
                                           value="<?= htmlspecialchars($_POST['kapasitas_maksimal'] ?? '40') ?>" min="1" max="100">
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="deskripsi" class="form-label">Deskripsi</label>
                            <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"><?= htmlspecialchars($_POST['deskripsi'] ?? '') ?></textarea>
                            <div class="form-text">Deskripsi tambahan tentang ruang kelas (opsional)</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="semester" class="form-label">Semester <span class="text-danger">*</span></label>
                                    <select class="form-select" id="semester" name="semester" required>
                                        <option value="1" <?= ($_POST['semester'] ?? $default_semester) == '1' ? 'selected' : '' ?>>Semester 1</option>
                                        <option value="2" <?= ($_POST['semester'] ?? $default_semester) == '2' ? 'selected' : '' ?>>Semester 2</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran <span class="text-danger">*</span></label>
                                    <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                                        <option value="">Pilih Tahun Ajaran</option>
                                        <?php foreach ($tahun_ajaran_list as $ta): ?>
                                            <option value="<?= htmlspecialchars($ta['tahun_ajaran']) ?>" 
                                                    <?= ($_POST['tahun_ajaran'] ?? $default_tahun_ajaran) == $ta['tahun_ajaran'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($ta['tahun_ajaran']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="tingkat_id" class="form-label">Tingkat</label>
                                    <select class="form-select" id="tingkat_id" name="tingkat_id">
                                        <option value="">Pilih Tingkat</option>
                                        <?php foreach ($tingkat_list as $tingkat): ?>
                                            <option value="<?= $tingkat['id'] ?>" 
                                                    <?= ($_POST['tingkat_id'] ?? '') == $tingkat['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($tingkat['nama_tingkat']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="jurusan_id" class="form-label">Jurusan</label>
                                    <select class="form-select" id="jurusan_id" name="jurusan_id">
                                        <option value="">Pilih Jurusan</option>
                                        <?php foreach ($jurusan_list as $jurusan): ?>
                                            <option value="<?= $jurusan['id'] ?>" 
                                                    <?= ($_POST['jurusan_id'] ?? '') == $jurusan['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($jurusan['nama_jurusan']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="guru_wali_id" class="form-label">Wali Kelas</label>
                                    <select class="form-select" id="guru_wali_id" name="guru_wali_id">
                                        <option value="">Pilih Wali Kelas</option>
                                        <?php foreach ($guru_list as $guru): ?>
                                            <option value="<?= $guru['id'] ?>" 
                                                    <?= ($_POST['guru_wali_id'] ?? '') == $guru['id'] ? 'selected' : '' ?>>
                                                <?= htmlspecialchars($guru['nama_lengkap']) ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="status" class="form-label">Status</label>
                                    <select class="form-select" id="status" name="status">
                                        <option value="aktif" <?= ($_POST['status'] ?? 'aktif') == 'aktif' ? 'selected' : '' ?>>Aktif</option>
                                        <option value="nonaktif" <?= ($_POST['status'] ?? 'aktif') == 'nonaktif' ? 'selected' : '' ?>>Non-aktif</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="d-flex justify-content-end">
                            <a href="index.php" class="btn btn-secondary me-2">Batal</a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> Simpan
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0"><i class="fas fa-info-circle"></i> Panduan</h5>
                </div>
                <div class="card-body">
                    <h6>Cara Menggunakan Ruang Kelas:</h6>
                    <ol class="small">
                        <li>Buat ruang kelas untuk setiap periode (semester & tahun ajaran)</li>
                        <li>Setiap ruang kelas terikat pada periode tertentu</li>
                        <li>Setelah membuat ruang kelas, assign siswa ke ruang kelas tersebut</li>
                        <li>Data absensi, tugas, dan nilai akan terkait dengan ruang kelas</li>
                        <li>Data historis tetap tersimpan untuk setiap periode</li>
                    </ol>

                    <hr>

                    <h6>Tips Penamaan:</h6>
                    <ul class="small">
                        <li><strong>X IPA 1</strong> - Kelas 10 IPA 1</li>
                        <li><strong>XI TKJ A</strong> - Kelas 11 Teknik Komputer Jaringan A</li>
                        <li><strong>XII MM 2</strong> - Kelas 12 Multimedia 2</li>
                    </ul>

                    <div class="alert alert-info small mt-3">
                        <i class="fas fa-lightbulb"></i>
                        <strong>Catatan:</strong> Ruang kelas yang sudah dibuat tidak dapat dihapus jika sudah memiliki siswa yang terdaftar.
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
