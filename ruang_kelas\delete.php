<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../models/RuangKelas.php';

// Only admin can access this module
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

// Check if classroom ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "ID ruang kelas tidak valid.";
    header("Location: index.php");
    exit();
}

$ruang_kelas_id = $_GET['id'];
$ruangKelasModel = new RuangKelas();

// Get classroom data
$classroom = $ruangKelasModel->getById($ruang_kelas_id);
if (!$classroom) {
    $_SESSION['error'] = "Ruang kelas tidak ditemukan.";
    header("Location: index.php");
    exit();
}

// Check if classroom has students
if ($classroom['jumlah_siswa'] > 0) {
    $_SESSION['error'] = "Tidak dapat menghapus ruang kelas yang masih memiliki siswa. Silakan pindahkan siswa terlebih dahulu.";
    header("Location: view.php?id=" . $ruang_kelas_id);
    exit();
}

// Delete classroom
$ruangKelasModel->id = $ruang_kelas_id;
if ($ruangKelasModel->delete()) {
    $_SESSION['success'] = "Ruang kelas berhasil dihapus.";
} else {
    $_SESSION['error'] = "Gagal menghapus ruang kelas.";
}

header("Location: index.php");
exit();
?>
