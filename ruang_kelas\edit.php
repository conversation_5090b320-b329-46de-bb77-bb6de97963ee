<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/RuangKelas.php';
require_once __DIR__ . '/../models/TahunAjaran.php';
require_once __DIR__ . '/../models/Tingkat.php';
require_once __DIR__ . '/../models/Jurusan.php';
require_once __DIR__ . '/../models/Guru.php';

// Only admin can access this module
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

// Check if classroom ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "ID ruang kelas tidak valid.";
    header("Location: index.php");
    exit();
}

$ruang_kelas_id = $_GET['id'];

// Initialize models
$ruangKelasModel = new RuangKelas();
$tahunAjaranModel = new TahunAjaran();
$tingkatModel = new Tingkat();
$jurusanModel = new Jurusan();
$guruModel = new Guru();

// Get classroom data
$classroom = $ruangKelasModel->getById($ruang_kelas_id);
if (!$classroom) {
    $_SESSION['error'] = "Ruang kelas tidak ditemukan.";
    header("Location: index.php");
    exit();
}

$message = '';
$error = '';

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $ruangKelasModel->id = $ruang_kelas_id;
    $ruangKelasModel->nama_ruang_kelas = $_POST['nama_ruang_kelas'];
    $ruangKelasModel->deskripsi = $_POST['deskripsi'];
    $ruangKelasModel->semester = $_POST['semester'];
    $ruangKelasModel->tahun_ajaran = $_POST['tahun_ajaran'];
    $ruangKelasModel->tingkat_id = !empty($_POST['tingkat_id']) ? $_POST['tingkat_id'] : null;
    $ruangKelasModel->jurusan_id = !empty($_POST['jurusan_id']) ? $_POST['jurusan_id'] : null;
    $ruangKelasModel->guru_wali_id = !empty($_POST['guru_wali_id']) ? $_POST['guru_wali_id'] : null;
    $ruangKelasModel->kapasitas_maksimal = $_POST['kapasitas_maksimal'];
    $ruangKelasModel->status = $_POST['status'];

    if ($ruangKelasModel->update()) {
        $_SESSION['success'] = "Ruang kelas berhasil diperbarui.";
        header("Location: view.php?id=" . $ruang_kelas_id);
        exit();
    } else {
        $error = "Gagal memperbarui ruang kelas.";
    }
}

// Get data for dropdowns
$tahun_ajaran_list = [];
$stmt = $tahunAjaranModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $tahun_ajaran_list[] = $row;
}

$tingkat_list = [];
$stmt = $tingkatModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $tingkat_list[] = $row;
}

$jurusan_list = [];
$stmt = $jurusanModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $jurusan_list[] = $row;
}

$guru_list = [];
$stmt = $guruModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    if ($row['status'] == 'aktif') {
        $guru_list[] = $row;
    }
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-edit"></i> Edit Ruang Kelas</h2>
        <div>
            <a href="view.php?id=<?= $ruang_kelas_id ?>" class="btn btn-info">
                <i class="fas fa-eye"></i> Lihat Detail
            </a>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <?php if ($error): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <?= htmlspecialchars($error) ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">Edit Informasi Ruang Kelas</h5>
        </div>
        <div class="card-body">
            <form method="POST">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="nama_ruang_kelas" class="form-label">Nama Ruang Kelas <span class="text-danger">*</span></label>
                            <input type="text" class="form-control" id="nama_ruang_kelas" name="nama_ruang_kelas" 
                                   value="<?= htmlspecialchars($_POST['nama_ruang_kelas'] ?? $classroom['nama_ruang_kelas']) ?>" required>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="kapasitas_maksimal" class="form-label">Kapasitas Maksimal</label>
                            <input type="number" class="form-control" id="kapasitas_maksimal" name="kapasitas_maksimal" 
                                   value="<?= htmlspecialchars($_POST['kapasitas_maksimal'] ?? $classroom['kapasitas_maksimal']) ?>" min="1" max="100">
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="deskripsi" class="form-label">Deskripsi</label>
                    <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"><?= htmlspecialchars($_POST['deskripsi'] ?? $classroom['deskripsi']) ?></textarea>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="semester" class="form-label">Semester <span class="text-danger">*</span></label>
                            <select class="form-select" id="semester" name="semester" required>
                                <option value="1" <?= ($_POST['semester'] ?? $classroom['semester']) == '1' ? 'selected' : '' ?>>Semester 1</option>
                                <option value="2" <?= ($_POST['semester'] ?? $classroom['semester']) == '2' ? 'selected' : '' ?>>Semester 2</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tahun_ajaran" class="form-label">Tahun Ajaran <span class="text-danger">*</span></label>
                            <select class="form-select" id="tahun_ajaran" name="tahun_ajaran" required>
                                <option value="">Pilih Tahun Ajaran</option>
                                <?php foreach ($tahun_ajaran_list as $ta): ?>
                                    <option value="<?= htmlspecialchars($ta['tahun_ajaran']) ?>" 
                                            <?= ($_POST['tahun_ajaran'] ?? $classroom['tahun_ajaran']) == $ta['tahun_ajaran'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($ta['tahun_ajaran']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="tingkat_id" class="form-label">Tingkat</label>
                            <select class="form-select" id="tingkat_id" name="tingkat_id">
                                <option value="">Pilih Tingkat</option>
                                <?php foreach ($tingkat_list as $tingkat): ?>
                                    <option value="<?= $tingkat['id'] ?>" 
                                            <?= ($_POST['tingkat_id'] ?? $classroom['tingkat_id']) == $tingkat['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($tingkat['nama_tingkat']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="jurusan_id" class="form-label">Jurusan</label>
                            <select class="form-select" id="jurusan_id" name="jurusan_id">
                                <option value="">Pilih Jurusan</option>
                                <?php foreach ($jurusan_list as $jurusan): ?>
                                    <option value="<?= $jurusan['id'] ?>" 
                                            <?= ($_POST['jurusan_id'] ?? $classroom['jurusan_id']) == $jurusan['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($jurusan['nama_jurusan']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="guru_wali_id" class="form-label">Wali Kelas</label>
                            <select class="form-select" id="guru_wali_id" name="guru_wali_id">
                                <option value="">Pilih Wali Kelas</option>
                                <?php foreach ($guru_list as $guru): ?>
                                    <option value="<?= $guru['id'] ?>" 
                                            <?= ($_POST['guru_wali_id'] ?? $classroom['guru_wali_id']) == $guru['id'] ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($guru['nama_lengkap']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="status" class="form-label">Status</label>
                            <select class="form-select" id="status" name="status">
                                <option value="aktif" <?= ($_POST['status'] ?? $classroom['status']) == 'aktif' ? 'selected' : '' ?>>Aktif</option>
                                <option value="nonaktif" <?= ($_POST['status'] ?? $classroom['status']) == 'nonaktif' ? 'selected' : '' ?>>Non-aktif</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-end">
                    <a href="view.php?id=<?= $ruang_kelas_id ?>" class="btn btn-secondary me-2">Batal</a>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Simpan Perubahan
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
