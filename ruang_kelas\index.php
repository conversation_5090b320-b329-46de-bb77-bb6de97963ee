<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/RuangKelas.php';
require_once __DIR__ . '/../models/PeriodeAktif.php';
require_once __DIR__ . '/../models/TahunAjaran.php';
require_once __DIR__ . '/../models/Tingkat.php';
require_once __DIR__ . '/../models/Jurusan.php';

// Only admin can access this module
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

// Initialize models
$ruangKelasModel = new RuangKelas();
$periodeModel = new PeriodeAktif();
$tahunAjaranModel = new TahunAjaran();
$tingkatModel = new Tingkat();
$jurusanModel = new Jurusan();

// Get active period
$activePeriod = $periodeModel->getActive();
$current_semester = '1';
$current_tahun_ajaran = '';

if ($activePeriod && is_array($activePeriod)) {
    $current_semester = $activePeriod['semester'];
    $current_tahun_ajaran = $activePeriod['tahun_ajaran'];
} else {
    // Fallback: get the latest academic year if no active period
    $stmt = $tahunAjaranModel->getAll();
    if ($stmt && $stmt->rowCount() > 0) {
        $latest_ta = $stmt->fetch(PDO::FETCH_ASSOC);
        if ($latest_ta) {
            $current_tahun_ajaran = $latest_ta['tahun_ajaran'];
        }
    }
}

// Get filter parameters
$filter_semester = $_GET['semester'] ?? $current_semester;
$filter_tahun_ajaran = $_GET['tahun_ajaran'] ?? $current_tahun_ajaran;

// Get all academic years for filter
$tahun_ajaran_list = [];
$stmt = $tahunAjaranModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $tahun_ajaran_list[] = $row;
}

// Get classrooms with filters
$stmt = $ruangKelasModel->getAll($filter_semester, $filter_tahun_ajaran);
$classrooms = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Check if tables exist
$tables_exist = true;
try {
    $check_stmt = $ruangKelasModel->getAll();
} catch (PDOException $e) {
    if (strpos($e->getMessage(), "doesn't exist") !== false) {
        $tables_exist = false;
    }
}

// Get statistics
$total_classrooms = $ruangKelasModel->getCount($filter_semester, $filter_tahun_ajaran);
$total_students = 0;
foreach ($classrooms as $classroom) {
    $total_students += $classroom['jumlah_siswa'];
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-door-open"></i> Manajemen Ruang Kelas</h2>
        <div>
            <a href="create.php" class="btn btn-primary">
                <i class="fas fa-plus"></i> Tambah Ruang Kelas
            </a>
            <a href="/absen/" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <!-- Filter Section -->
    <div class="card mb-4">
        <div class="card-header">
            <h5 class="card-title mb-0"><i class="fas fa-filter"></i> Filter Data</h5>
        </div>
        <div class="card-body">
            <form method="GET" class="row g-3">
                <div class="col-md-4">
                    <label for="semester" class="form-label">Semester</label>
                    <select name="semester" id="semester" class="form-select">
                        <option value="1" <?= $filter_semester == '1' ? 'selected' : '' ?>>Semester 1</option>
                        <option value="2" <?= $filter_semester == '2' ? 'selected' : '' ?>>Semester 2</option>
                    </select>
                </div>
                <div class="col-md-4">
                    <label for="tahun_ajaran" class="form-label">Tahun Ajaran</label>
                    <select name="tahun_ajaran" id="tahun_ajaran" class="form-select">
                        <option value="">Semua Tahun Ajaran</option>
                        <?php foreach ($tahun_ajaran_list as $ta): ?>
                            <option value="<?= htmlspecialchars($ta['tahun_ajaran']) ?>" 
                                    <?= $filter_tahun_ajaran == $ta['tahun_ajaran'] ? 'selected' : '' ?>>
                                <?= htmlspecialchars($ta['tahun_ajaran']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-4">
                    <label class="form-label">&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search"></i> Filter
                        </button>
                        <a href="index.php" class="btn btn-secondary">
                            <i class="fas fa-refresh"></i> Reset
                        </a>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="card bg-primary text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Ruang Kelas</h5>
                            <h2><?= $total_classrooms ?></h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-door-open fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-success text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Total Siswa</h5>
                            <h2><?= $total_students ?></h2>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-users fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-4">
            <div class="card bg-info text-white">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h5 class="card-title">Periode Aktif</h5>
                            <h6><?= $current_semester == '1' ? 'Semester 1' : 'Semester 2' ?></h6>
                            <small><?= htmlspecialchars($current_tahun_ajaran ?: 'Belum diatur') ?></small>
                        </div>
                        <div class="align-self-center">
                            <i class="fas fa-calendar fa-2x"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Classrooms Table -->
    <div class="card">
        <div class="card-header">
            <h5 class="card-title mb-0">
                <i class="fas fa-list"></i> Daftar Ruang Kelas
                <?php if ($filter_semester || $filter_tahun_ajaran): ?>
                    <small class="text-muted">
                        (Semester <?= $filter_semester ?> - <?= htmlspecialchars($filter_tahun_ajaran) ?>)
                    </small>
                <?php endif; ?>
            </h5>
        </div>
        <div class="card-body">
            <?php if (!$tables_exist): ?>
                <div class="alert alert-warning">
                    <h5><i class="fas fa-exclamation-triangle"></i> Tabel Ruang Kelas Belum Ada</h5>
                    <p>Sistem ruang kelas belum diinisialisasi. Silakan jalankan migration terlebih dahulu.</p>
                    <div class="mt-3">
                        <a href="/absen/maintenance/classroom_migration.php" class="btn btn-primary">
                            <i class="fas fa-rocket"></i> Jalankan Migration
                        </a>
                        <a href="/absen/maintenance/create_tables_manual.php" class="btn btn-secondary">
                            <i class="fas fa-tools"></i> Buat Tabel Manual
                        </a>
                    </div>
                </div>
            <?php elseif (empty($classrooms)): ?>
                <div class="text-center py-4">
                    <i class="fas fa-door-open fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">Belum ada ruang kelas</h5>
                    <p class="text-muted">Silakan tambah ruang kelas baru untuk periode yang dipilih.</p>
                    <a href="create.php" class="btn btn-primary">
                        <i class="fas fa-plus"></i> Tambah Ruang Kelas
                    </a>
                </div>
            <?php else: ?>
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead class="table-dark">
                            <tr>
                                <th>No</th>
                                <th>Nama Ruang Kelas</th>
                                <th>Tingkat</th>
                                <th>Jurusan</th>
                                <th>Wali Kelas</th>
                                <th>Jumlah Siswa</th>
                                <th>Kapasitas</th>
                                <th>Status</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($classrooms as $index => $classroom): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <strong><?= htmlspecialchars($classroom['nama_ruang_kelas']) ?></strong>
                                        <?php if ($classroom['deskripsi']): ?>
                                            <br><small class="text-muted"><?= htmlspecialchars($classroom['deskripsi']) ?></small>
                                        <?php endif; ?>
                                    </td>
                                    <td><?= htmlspecialchars($classroom['nama_tingkat'] ?? '-') ?></td>
                                    <td><?= htmlspecialchars($classroom['nama_jurusan'] ?? '-') ?></td>
                                    <td><?= htmlspecialchars($classroom['nama_guru_wali'] ?? '-') ?></td>
                                    <td>
                                        <span class="badge bg-primary"><?= $classroom['jumlah_siswa'] ?></span>
                                    </td>
                                    <td><?= $classroom['kapasitas_maksimal'] ?></td>
                                    <td>
                                        <?php if ($classroom['status'] == 'aktif'): ?>
                                            <span class="badge bg-success">Aktif</span>
                                        <?php else: ?>
                                            <span class="badge bg-secondary">Non-aktif</span>
                                        <?php endif; ?>
                                    </td>
                                    <td>
                                        <div class="btn-group" role="group">
                                            <a href="view.php?id=<?= $classroom['id'] ?>" class="btn btn-sm btn-info" title="Lihat Detail">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                            <a href="assign_students.php?id=<?= $classroom['id'] ?>" class="btn btn-sm btn-success" title="Kelola Siswa">
                                                <i class="fas fa-users"></i>
                                            </a>
                                            <a href="edit.php?id=<?= $classroom['id'] ?>" class="btn btn-sm btn-warning" title="Edit">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            <a href="delete.php?id=<?= $classroom['id'] ?>" class="btn btn-sm btn-danger" 
                                               onclick="return confirm('Apakah Anda yakin ingin menghapus ruang kelas ini?')" title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// Auto-submit form when filters change
document.getElementById('semester').addEventListener('change', function() {
    this.form.submit();
});

document.getElementById('tahun_ajaran').addEventListener('change', function() {
    this.form.submit();
});
</script>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
