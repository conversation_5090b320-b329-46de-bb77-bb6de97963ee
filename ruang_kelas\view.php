<?php
require_once __DIR__ . '/../middleware/auth.php';
require_once __DIR__ . '/../template/header.php';
require_once __DIR__ . '/../models/RuangKelas.php';

// Only admin can access this module
if ($_SESSION['role'] !== 'admin') {
    header("Location: /absen/");
    exit();
}

// Check if classroom ID is provided
if (!isset($_GET['id']) || empty($_GET['id'])) {
    $_SESSION['error'] = "ID ruang kelas tidak valid.";
    header("Location: index.php");
    exit();
}

$ruang_kelas_id = $_GET['id'];
$ruangKelasModel = new RuangKelas();

// Get classroom details
$classroom = $ruangKelasModel->getById($ruang_kelas_id);
if (!$classroom) {
    $_SESSION['error'] = "Ruang kelas tidak ditemukan.";
    header("Location: index.php");
    exit();
}

// Get students in classroom
$students = [];
$stmt = $ruangKelasModel->getStudents($ruang_kelas_id, 'aktif');
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $students[] = $row;
}
?>

<div class="container-fluid">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2><i class="fas fa-eye"></i> Detail Ruang Kelas</h2>
        <div>
            <a href="assign_students.php?id=<?= $classroom['id'] ?>" class="btn btn-success">
                <i class="fas fa-users"></i> Kelola Siswa
            </a>
            <a href="edit.php?id=<?= $classroom['id'] ?>" class="btn btn-warning">
                <i class="fas fa-edit"></i> Edit
            </a>
            <a href="index.php" class="btn btn-secondary">
                <i class="fas fa-arrow-left"></i> Kembali
            </a>
        </div>
    </div>

    <div class="row">
        <!-- Classroom Information -->
        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informasi Ruang Kelas</h5>
                </div>
                <div class="card-body">
                    <table class="table table-borderless">
                        <tr>
                            <td><strong>Nama:</strong></td>
                            <td><?= htmlspecialchars($classroom['nama_ruang_kelas']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Semester:</strong></td>
                            <td>Semester <?= $classroom['semester'] ?></td>
                        </tr>
                        <tr>
                            <td><strong>Tahun Ajaran:</strong></td>
                            <td><?= htmlspecialchars($classroom['tahun_ajaran']) ?></td>
                        </tr>
                        <tr>
                            <td><strong>Tingkat:</strong></td>
                            <td><?= htmlspecialchars($classroom['nama_tingkat'] ?? '-') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Jurusan:</strong></td>
                            <td><?= htmlspecialchars($classroom['nama_jurusan'] ?? '-') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Wali Kelas:</strong></td>
                            <td><?= htmlspecialchars($classroom['nama_guru_wali'] ?? '-') ?></td>
                        </tr>
                        <tr>
                            <td><strong>Kapasitas:</strong></td>
                            <td><?= $classroom['jumlah_siswa'] ?> / <?= $classroom['kapasitas_maksimal'] ?> siswa</td>
                        </tr>
                        <tr>
                            <td><strong>Status:</strong></td>
                            <td>
                                <?php if ($classroom['status'] == 'aktif'): ?>
                                    <span class="badge bg-success">Aktif</span>
                                <?php else: ?>
                                    <span class="badge bg-secondary">Non-aktif</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                    
                    <?php if ($classroom['deskripsi']): ?>
                        <hr>
                        <h6>Deskripsi:</h6>
                        <p class="text-muted"><?= htmlspecialchars($classroom['deskripsi']) ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Students List -->
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users"></i> Daftar Siswa (<?= count($students) ?>)
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($students)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h6 class="text-muted">Belum ada siswa di ruang kelas ini</h6>
                            <p class="text-muted">Silakan tambahkan siswa melalui menu "Kelola Siswa".</p>
                            <a href="assign_students.php?id=<?= $classroom['id'] ?>" class="btn btn-primary">
                                <i class="fas fa-plus"></i> Tambah Siswa
                            </a>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="table-dark">
                                    <tr>
                                        <th>No</th>
                                        <th>NIS</th>
                                        <th>Nama Siswa</th>
                                        <th>Jenis Kelamin</th>
                                        <th>Tanggal Masuk</th>
                                        <th>Status</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $index => $student): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td><?= htmlspecialchars($student['nis']) ?></td>
                                            <td><?= htmlspecialchars($student['nama_siswa']) ?></td>
                                            <td>
                                                <?php if ($student['jenis_kelamin'] == 'L'): ?>
                                                    <span class="badge bg-primary">Laki-laki</span>
                                                <?php else: ?>
                                                    <span class="badge bg-info">Perempuan</span>
                                                <?php endif; ?>
                                            </td>
                                            <td><?= date('d/m/Y', strtotime($student['tanggal_masuk'])) ?></td>
                                            <td>
                                                <span class="badge bg-success">Aktif</span>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?php require_once __DIR__ . '/../template/footer.php'; ?>
