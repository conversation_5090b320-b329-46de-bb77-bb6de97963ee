<?php
require_once 'config/database.php';

echo "<h1>Safe Database Setup</h1>";
echo "<p>Handles existing tables safely - no errors!</p>";

$database = new Database();
$conn = $database->getConnection();

if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🛡️ Safe Database Setup</h3>";
    echo "<p>This will safely setup your database:</p>";
    echo "<ul>";
    echo "<li>✅ Check existing tables</li>";
    echo "<li>✅ Create missing tables only</li>";
    echo "<li>✅ Add missing columns</li>";
    echo "<li>✅ Insert sample data safely</li>";
    echo "</ul>";
    echo "<p><strong>Safe for existing data!</strong></p>";
    echo "<p><a href='?confirm=yes' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Setup Database</a></p>";
    echo "</div>";
    exit;
}

function safe_exec($conn, $sql, $description) {
    try {
        $conn->exec($sql);
        echo "<div style='color: green;'>✅ $description</div>";
        return true;
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'already exists') !== false || 
            strpos($e->getMessage(), 'Duplicate column') !== false ||
            strpos($e->getMessage(), 'Duplicate entry') !== false) {
            echo "<div style='color: blue;'>ℹ️ $description (already exists)</div>";
            return true;
        } else {
            echo "<div style='color: red;'>❌ $description - Error: " . $e->getMessage() . "</div>";
            return false;
        }
    }
}

try {
    echo "<h2>Setting Up Database...</h2>";
    
    // Step 1: Create tables if not exist
    echo "<h3>Step 1: Creating Tables</h3>";
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `mata_pelajaran` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `kode_mapel` varchar(10) NOT NULL,
      `nama_mapel` varchar(100) NOT NULL,
      `kkm` decimal(5,2) DEFAULT 75.00,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating mata_pelajaran table");
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nama_ruang_kelas` varchar(100) NOT NULL,
      `semester` enum('1','2') NOT NULL DEFAULT '1',
      `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
      `status` enum('aktif','nonaktif') DEFAULT 'aktif',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating ruang_kelas table");
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `siswa` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nis` varchar(20) NOT NULL,
      `nama_siswa` varchar(100) NOT NULL,
      `jenis_kelamin` enum('L','P') NOT NULL,
      `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating siswa table");
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `siswa_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `tanggal_masuk` date NOT NULL,
      `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating siswa_ruang_kelas table");
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `mapel_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `judul` varchar(255) NOT NULL,
      `deskripsi` text DEFAULT NULL,
      `tanggal` date NOT NULL,
      `semester` enum('1','2') NOT NULL DEFAULT '1',
      `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating tugas table");
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tanggal` date NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `mapel_id` int(11) NOT NULL,
      `semester` enum('1','2') NOT NULL DEFAULT '1',
      `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating absensi table");
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `detail_absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `absensi_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `status` enum('hadir','sakit','izin','alpha') NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating detail_absensi table");
    
    safe_exec($conn, "
    CREATE TABLE IF NOT EXISTS `nilai_tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tugas_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `nilai` decimal(5,2) DEFAULT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ", "Creating nilai_tugas table");
    
    // Step 2: Add missing columns
    echo "<h3>Step 2: Adding Missing Columns</h3>";
    
    // Check and add ruang_kelas_id to tugas if missing
    safe_exec($conn, "ALTER TABLE tugas ADD COLUMN IF NOT EXISTS ruang_kelas_id int(11) DEFAULT NULL", "Adding ruang_kelas_id to tugas");
    safe_exec($conn, "ALTER TABLE absensi ADD COLUMN IF NOT EXISTS ruang_kelas_id int(11) DEFAULT NULL", "Adding ruang_kelas_id to absensi");
    
    // Step 3: Insert sample data
    echo "<h3>Step 3: Inserting Sample Data</h3>";
    
    // Mata pelajaran
    $subjects = [
        ['MTK001', 'Matematika', 75.00],
        ['BIN001', 'Bahasa Indonesia', 75.00],
        ['ING001', 'Bahasa Inggris', 75.00],
        ['FIS001', 'Fisika', 75.00],
        ['KIM001', 'Kimia', 75.00],
        ['BIO001', 'Biologi', 75.00]
    ];
    
    foreach ($subjects as $subject) {
        safe_exec($conn, "INSERT IGNORE INTO mata_pelajaran (kode_mapel, nama_mapel, kkm) VALUES ('{$subject[0]}', '{$subject[1]}', {$subject[2]})", "Inserting {$subject[1]}");
    }
    
    // Ruang kelas
    $classrooms = [
        ['X IPA 1', '1', '2024/2025', 'aktif'],
        ['X IPA 2', '1', '2024/2025', 'aktif'],
        ['X IPS 1', '1', '2024/2025', 'aktif'],
        ['XI IPA 1', '1', '2024/2025', 'aktif'],
        ['XI IPA 2', '1', '2024/2025', 'aktif'],
        ['XI IPS 1', '1', '2024/2025', 'aktif']
    ];
    
    foreach ($classrooms as $classroom) {
        safe_exec($conn, "INSERT IGNORE INTO ruang_kelas (nama_ruang_kelas, semester, tahun_ajaran, status) VALUES ('{$classroom[0]}', '{$classroom[1]}', '{$classroom[2]}', '{$classroom[3]}')", "Inserting classroom {$classroom[0]}");
    }
    
    // Siswa
    $students = [
        ['2024001', 'Andi Pratama', 'L', 'aktif'],
        ['2024002', 'Sari Dewi', 'P', 'aktif'],
        ['2024003', 'Budi Setiawan', 'L', 'aktif'],
        ['2024004', 'Rina Sari', 'P', 'aktif'],
        ['2024005', 'Doni Kurniawan', 'L', 'aktif'],
        ['2024006', 'Maya Putri', 'P', 'aktif']
    ];
    
    foreach ($students as $student) {
        safe_exec($conn, "INSERT IGNORE INTO siswa (nis, nama_siswa, jenis_kelamin, status) VALUES ('{$student[0]}', '{$student[1]}', '{$student[2]}', '{$student[3]}')", "Inserting student {$student[1]}");
    }
    
    // Student assignments (only if both students and classrooms exist)
    $assignments = [
        [1, 1, '2024-07-01', 'aktif'],
        [2, 1, '2024-07-01', 'aktif'],
        [3, 2, '2024-07-01', 'aktif'],
        [4, 2, '2024-07-01', 'aktif'],
        [5, 3, '2024-07-01', 'aktif'],
        [6, 3, '2024-07-01', 'aktif']
    ];
    
    foreach ($assignments as $assignment) {
        safe_exec($conn, "INSERT IGNORE INTO siswa_ruang_kelas (siswa_id, ruang_kelas_id, tanggal_masuk, status) VALUES ({$assignment[0]}, {$assignment[1]}, '{$assignment[2]}', '{$assignment[3]}')", "Assigning student {$assignment[0]} to classroom {$assignment[1]}");
    }
    
    echo "<h2>Verification</h2>";
    
    // Check what we have
    $tables = ['mata_pelajaran', 'ruang_kelas', 'siswa', 'siswa_ruang_kelas', 'tugas', 'absensi'];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Table</th><th style='border: 1px solid #ddd; padding: 10px;'>Records</th><th style='border: 1px solid #ddd; padding: 10px;'>Status</th></tr>";
    
    foreach ($tables as $table) {
        try {
            $stmt = $conn->query("SELECT COUNT(*) FROM $table");
            $count = $stmt->fetchColumn();
            $status = $count > 0 ? "<span style='color: green;'>✓ Has Data</span>" : "<span style='color: orange;'>⚠ Empty</span>";
            echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>$table</td><td style='border: 1px solid #ddd; padding: 10px;'>$count</td><td style='border: 1px solid #ddd; padding: 10px;'>$status</td></tr>";
        } catch (Exception $e) {
            echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>$table</td><td style='border: 1px solid #ddd; padding: 10px;'>Error</td><td style='border: 1px solid #ddd; padding: 10px;'><span style='color: red;'>✗ " . $e->getMessage() . "</span></td></tr>";
        }
    }
    echo "</table>";
    
    // Show sample data
    echo "<h3>Sample Student Assignments</h3>";
    try {
        $stmt = $conn->query("
            SELECT s.nama_siswa, rk.nama_ruang_kelas 
            FROM siswa s 
            JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id 
            JOIN ruang_kelas rk ON srk.ruang_kelas_id = rk.id 
            WHERE srk.status = 'aktif'
            ORDER BY rk.nama_ruang_kelas, s.nama_siswa
            LIMIT 10
        ");
        
        if ($stmt->rowCount() > 0) {
            echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
            echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Student</th><th style='border: 1px solid #ddd; padding: 10px;'>Classroom</th></tr>";
            
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>{$row['nama_siswa']}</td><td style='border: 1px solid #ddd; padding: 10px;'>{$row['nama_ruang_kelas']}</td></tr>";
            }
            echo "</table>";
        } else {
            echo "<p style='color: orange;'>No student assignments found. This is normal if you're starting fresh.</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: red;'>Could not display assignments: " . $e->getMessage() . "</p>";
    }
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 Database Setup Complete!</h3>";
    echo "<p><strong>Your classroom system is ready!</strong></p>";
    echo "<p><strong>What's available:</strong></p>";
    echo "<ul>";
    echo "<li>✅ All essential tables created</li>";
    echo "<li>✅ Sample data inserted (if not already present)</li>";
    echo "<li>✅ Classroom system structure ready</li>";
    echo "</ul>";
    echo "<p><strong>Test the system:</strong></p>";
    echo "<ol>";
    echo "<li><a href='tugas/index.php' style='color: #155724; font-weight: bold;'>Create Assignment (Tugas)</a></li>";
    echo "<li><a href='absensi/create.php' style='color: #155724; font-weight: bold;'>Take Attendance (Absensi)</a></li>";
    echo "<li><a href='debug_classroom_issue.php' style='color: #155724; font-weight: bold;'>Run System Diagnostics</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Setup Error</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Safe Setup:</strong> Handles existing tables, no data loss!</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
