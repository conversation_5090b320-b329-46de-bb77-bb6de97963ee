<?php
require_once 'config/database.php';

echo "<h1>Setup Fresh Database - Classroom System</h1>";
echo "<p>This will create all tables and sample data for the new classroom system.</p>";

$database = new Database();
$conn = $database->getConnection();

if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🚀 Fresh Database Setup</h3>";
    echo "<p>This will create:</p>";
    echo "<ul>";
    echo "<li><strong>Core Tables:</strong> siswa, guru, mata_pelajaran, tingkat, jurusan</li>";
    echo "<li><strong>Classroom System:</strong> ruang_kelas, siswa_ruang_kelas</li>";
    echo "<li><strong>Academic System:</strong> ta<PERSON>_ajaran, periode_aktif</li>";
    echo "<li><strong>Learning System:</strong> tugas, absensi, nilai</li>";
    echo "<li><strong>Sample Data:</strong> Ready-to-use test data</li>";
    echo "</ul>";
    echo "<p><strong>Perfect for:</strong> Clean start with modern classroom system</p>";
    echo "<p><a href='?confirm=yes' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Fresh Database</a></p>";
    echo "</div>";
    exit;
}

$steps = [];
$current_step = 0;

function add_step($title, $status, $message = '') {
    global $steps, $current_step;
    $current_step++;
    $steps[] = [
        'step' => $current_step,
        'title' => $title,
        'status' => $status,
        'message' => $message
    ];
    
    $color = $status === 'success' ? 'green' : ($status === 'error' ? 'red' : 'orange');
    $icon = $status === 'success' ? '✅' : ($status === 'error' ? '❌' : '⚠️');
    
    echo "<div style='color: $color; margin: 5px 0;'>$icon Step $current_step: $title";
    if ($message) echo " - $message";
    echo "</div>";
}

try {
    echo "<h2>Creating Database Structure...</h2>";
    
    // Step 1: Create core tables
    add_step("Creating core tables", "success");
    
    // Tingkat table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `tingkat` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nama_tingkat` varchar(50) NOT NULL,
      `keterangan` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Jurusan table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `jurusan` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nama_jurusan` varchar(100) NOT NULL,
      `kode_jurusan` varchar(10) NOT NULL,
      `keterangan` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `kode_jurusan` (`kode_jurusan`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Guru table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `guru` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nip` varchar(20) DEFAULT NULL,
      `nama_guru` varchar(100) NOT NULL,
      `email` varchar(100) DEFAULT NULL,
      `no_hp` varchar(15) DEFAULT NULL,
      `alamat` text DEFAULT NULL,
      `status` enum('aktif','nonaktif') DEFAULT 'aktif',
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `nip` (`nip`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Mata pelajaran table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `mata_pelajaran` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `kode_mapel` varchar(10) NOT NULL,
      `nama_mapel` varchar(100) NOT NULL,
      `kkm` decimal(5,2) DEFAULT 75.00,
      `keterangan` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `kode_mapel` (`kode_mapel`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Step 2: Create academic system tables
    add_step("Creating academic system tables", "success");
    
    // Tahun ajaran table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `tahun_ajaran` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tahun_ajaran` varchar(9) NOT NULL,
      `semester_1_mulai` date NOT NULL,
      `semester_1_selesai` date NOT NULL,
      `semester_2_mulai` date NOT NULL,
      `semester_2_selesai` date NOT NULL,
      `is_active` tinyint(1) DEFAULT 0,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `tahun_ajaran` (`tahun_ajaran`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Periode aktif table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `periode_aktif` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tahun_ajaran_id` int(11) NOT NULL,
      `semester` enum('1','2') NOT NULL,
      `is_active` tinyint(1) DEFAULT 1,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `tahun_ajaran_id` (`tahun_ajaran_id`),
      CONSTRAINT `periode_aktif_ibfk_1` FOREIGN KEY (`tahun_ajaran_id`) REFERENCES `tahun_ajaran` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Step 3: Create classroom system tables
    add_step("Creating classroom system tables", "success");
    
    // Ruang kelas table (NEW SYSTEM)
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nama_ruang_kelas` varchar(100) NOT NULL,
      `deskripsi` text DEFAULT NULL,
      `semester` enum('1','2') NOT NULL,
      `tahun_ajaran` varchar(9) NOT NULL,
      `tingkat_id` int(11) DEFAULT NULL,
      `jurusan_id` int(11) DEFAULT NULL,
      `guru_wali_id` int(11) DEFAULT NULL,
      `kapasitas_maksimal` int(11) DEFAULT 40,
      `status` enum('aktif','nonaktif') DEFAULT 'aktif',
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`),
      KEY `fk_ruang_kelas_tingkat` (`tingkat_id`),
      KEY `fk_ruang_kelas_jurusan` (`jurusan_id`),
      KEY `fk_ruang_kelas_guru_wali` (`guru_wali_id`),
      CONSTRAINT `fk_ruang_kelas_tingkat` FOREIGN KEY (`tingkat_id`) REFERENCES `tingkat` (`id`) ON DELETE SET NULL,
      CONSTRAINT `fk_ruang_kelas_jurusan` FOREIGN KEY (`jurusan_id`) REFERENCES `jurusan` (`id`) ON DELETE SET NULL,
      CONSTRAINT `fk_ruang_kelas_guru_wali` FOREIGN KEY (`guru_wali_id`) REFERENCES `guru` (`id`) ON DELETE SET NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Siswa table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `siswa` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nis` varchar(20) NOT NULL,
      `nama_siswa` varchar(100) NOT NULL,
      `jenis_kelamin` enum('L','P') NOT NULL,
      `tempat_lahir` varchar(50) DEFAULT NULL,
      `tanggal_lahir` date DEFAULT NULL,
      `alamat` text DEFAULT NULL,
      `no_hp` varchar(15) DEFAULT NULL,
      `email` varchar(100) DEFAULT NULL,
      `nama_ortu` varchar(100) DEFAULT NULL,
      `no_hp_ortu` varchar(15) DEFAULT NULL,
      `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `nis` (`nis`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Siswa ruang kelas junction table (NEW SYSTEM)
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `siswa_ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `siswa_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `tanggal_masuk` date NOT NULL,
      `tanggal_keluar` date DEFAULT NULL,
      `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
      `keterangan` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_siswa_ruang_aktif` (`siswa_id`, `ruang_kelas_id`),
      KEY `fk_siswa_ruang_siswa` (`siswa_id`),
      KEY `fk_siswa_ruang_kelas` (`ruang_kelas_id`),
      KEY `idx_status` (`status`),
      CONSTRAINT `fk_siswa_ruang_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE,
      CONSTRAINT `fk_siswa_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Step 4: Create learning system tables
    add_step("Creating learning system tables", "success");
    
    // Tugas table (UPDATED FOR CLASSROOM SYSTEM)
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `mapel_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `judul` varchar(255) NOT NULL,
      `deskripsi` text DEFAULT NULL,
      `tanggal` date NOT NULL,
      `semester` enum('1','2') NOT NULL,
      `tahun_ajaran` varchar(9) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `fk_tugas_mapel` (`mapel_id`),
      KEY `fk_tugas_ruang_kelas` (`ruang_kelas_id`),
      KEY `idx_semester_tahun` (`semester`, `tahun_ajaran`),
      CONSTRAINT `fk_tugas_mapel` FOREIGN KEY (`mapel_id`) REFERENCES `mata_pelajaran` (`id`) ON DELETE CASCADE,
      CONSTRAINT `fk_tugas_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Absensi table (UPDATED FOR CLASSROOM SYSTEM)
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tanggal` date NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `mapel_id` int(11) NOT NULL,
      `semester` enum('1','2') NOT NULL,
      `tahun_ajaran` varchar(9) NOT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      KEY `fk_absensi_ruang_kelas` (`ruang_kelas_id`),
      KEY `fk_absensi_mapel` (`mapel_id`),
      KEY `idx_tanggal` (`tanggal`),
      CONSTRAINT `fk_absensi_ruang_kelas` FOREIGN KEY (`ruang_kelas_id`) REFERENCES `ruang_kelas` (`id`) ON DELETE CASCADE,
      CONSTRAINT `fk_absensi_mapel` FOREIGN KEY (`mapel_id`) REFERENCES `mata_pelajaran` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Detail absensi table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `detail_absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `absensi_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `status` enum('hadir','sakit','izin','alpha') NOT NULL,
      `keterangan` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_absensi_siswa` (`absensi_id`, `siswa_id`),
      KEY `fk_detail_absensi_siswa` (`siswa_id`),
      CONSTRAINT `fk_detail_absensi_absensi` FOREIGN KEY (`absensi_id`) REFERENCES `absensi` (`id`) ON DELETE CASCADE,
      CONSTRAINT `fk_detail_absensi_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    // Nilai tugas table
    $conn->exec("
    CREATE TABLE IF NOT EXISTS `nilai_tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tugas_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `nilai` decimal(5,2) DEFAULT NULL,
      `keterangan` text DEFAULT NULL,
      `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
      `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
      PRIMARY KEY (`id`),
      UNIQUE KEY `unique_tugas_siswa` (`tugas_id`, `siswa_id`),
      KEY `fk_nilai_tugas_siswa` (`siswa_id`),
      CONSTRAINT `fk_nilai_tugas_tugas` FOREIGN KEY (`tugas_id`) REFERENCES `tugas` (`id`) ON DELETE CASCADE,
      CONSTRAINT `fk_nilai_tugas_siswa` FOREIGN KEY (`siswa_id`) REFERENCES `siswa` (`id`) ON DELETE CASCADE
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    ");
    
    echo "<h2>Creating Sample Data...</h2>";
    
    // Step 5: Insert sample data
    add_step("Inserting sample data", "success");
    
    // Insert tingkat
    $conn->exec("INSERT INTO tingkat (nama_tingkat, keterangan) VALUES 
        ('X', 'Kelas 10'),
        ('XI', 'Kelas 11'), 
        ('XII', 'Kelas 12')");
    
    // Insert jurusan
    $conn->exec("INSERT INTO jurusan (nama_jurusan, kode_jurusan, keterangan) VALUES 
        ('Ilmu Pengetahuan Alam', 'IPA', 'Jurusan IPA'),
        ('Ilmu Pengetahuan Sosial', 'IPS', 'Jurusan IPS')");
    
    // Insert guru
    $conn->exec("INSERT INTO guru (nip, nama_guru, email, status) VALUES 
        ('196801011990031001', 'Drs. Ahmad Wijaya', '<EMAIL>', 'aktif'),
        ('197205151995122002', 'Siti Nurhaliza, S.Pd', '<EMAIL>', 'aktif'),
        ('198003201999031003', 'Budi Santoso, M.Pd', '<EMAIL>', 'aktif')");
    
    // Insert mata pelajaran
    $conn->exec("INSERT INTO mata_pelajaran (kode_mapel, nama_mapel, kkm) VALUES 
        ('MTK001', 'Matematika', 75.00),
        ('BIN001', 'Bahasa Indonesia', 75.00),
        ('ING001', 'Bahasa Inggris', 75.00),
        ('FIS001', 'Fisika', 75.00),
        ('KIM001', 'Kimia', 75.00),
        ('BIO001', 'Biologi', 75.00),
        ('SEJ001', 'Sejarah', 75.00),
        ('GEO001', 'Geografi', 75.00),
        ('EKO001', 'Ekonomi', 75.00),
        ('SOC001', 'Sosiologi', 75.00)");
    
    // Insert tahun ajaran
    $conn->exec("INSERT INTO tahun_ajaran (tahun_ajaran, semester_1_mulai, semester_1_selesai, semester_2_mulai, semester_2_selesai, is_active) VALUES 
        ('2024/2025', '2024-07-01', '2024-12-31', '2025-01-01', '2025-06-30', 1)");
    
    // Get tahun ajaran ID
    $ta_id = $conn->lastInsertId();
    
    // Insert periode aktif
    $conn->exec("INSERT INTO periode_aktif (tahun_ajaran_id, semester, is_active) VALUES ($ta_id, '1', 1)");
    
    // Insert ruang kelas (NEW CLASSROOM SYSTEM)
    $conn->exec("INSERT INTO ruang_kelas (nama_ruang_kelas, semester, tahun_ajaran, tingkat_id, jurusan_id, guru_wali_id, status) VALUES 
        ('X IPA 1', '1', '2024/2025', 1, 1, 1, 'aktif'),
        ('X IPA 2', '1', '2024/2025', 1, 1, 2, 'aktif'),
        ('X IPS 1', '1', '2024/2025', 1, 2, 3, 'aktif'),
        ('XI IPA 1', '1', '2024/2025', 2, 1, 1, 'aktif'),
        ('XI IPA 2', '1', '2024/2025', 2, 1, 2, 'aktif'),
        ('XI IPS 1', '1', '2024/2025', 2, 2, 3, 'aktif'),
        ('XII IPA 1', '1', '2024/2025', 3, 1, 1, 'aktif'),
        ('XII IPA 2', '1', '2024/2025', 3, 1, 2, 'aktif'),
        ('XII IPS 1', '1', '2024/2025', 3, 2, 3, 'aktif')");
    
    // Insert sample siswa
    $conn->exec("INSERT INTO siswa (nis, nama_siswa, jenis_kelamin, status) VALUES 
        ('2024001', 'Andi Pratama', 'L', 'aktif'),
        ('2024002', 'Sari Dewi', 'P', 'aktif'),
        ('2024003', 'Budi Setiawan', 'L', 'aktif'),
        ('2024004', 'Rina Sari', 'P', 'aktif'),
        ('2024005', 'Doni Kurniawan', 'L', 'aktif'),
        ('2024006', 'Maya Putri', 'P', 'aktif'),
        ('2024007', 'Rudi Hartono', 'L', 'aktif'),
        ('2024008', 'Lina Marlina', 'P', 'aktif'),
        ('2024009', 'Agus Salim', 'L', 'aktif'),
        ('2024010', 'Fitri Handayani', 'P', 'aktif')");
    
    // Assign students to classrooms (NEW SYSTEM)
    $conn->exec("INSERT INTO siswa_ruang_kelas (siswa_id, ruang_kelas_id, tanggal_masuk, status) VALUES 
        (1, 1, '2024-07-01', 'aktif'),
        (2, 1, '2024-07-01', 'aktif'),
        (3, 1, '2024-07-01', 'aktif'),
        (4, 2, '2024-07-01', 'aktif'),
        (5, 2, '2024-07-01', 'aktif'),
        (6, 2, '2024-07-01', 'aktif'),
        (7, 3, '2024-07-01', 'aktif'),
        (8, 3, '2024-07-01', 'aktif'),
        (9, 4, '2024-07-01', 'aktif'),
        (10, 4, '2024-07-01', 'aktif')");
    
    // Step 6: Final verification
    add_step("Verifying database setup", "success");
    
    $verification = [
        'Tingkat' => "SELECT COUNT(*) as count FROM tingkat",
        'Jurusan' => "SELECT COUNT(*) as count FROM jurusan", 
        'Guru' => "SELECT COUNT(*) as count FROM guru",
        'Mata Pelajaran' => "SELECT COUNT(*) as count FROM mata_pelajaran",
        'Tahun Ajaran' => "SELECT COUNT(*) as count FROM tahun_ajaran",
        'Ruang Kelas' => "SELECT COUNT(*) as count FROM ruang_kelas",
        'Siswa' => "SELECT COUNT(*) as count FROM siswa",
        'Student Assignments' => "SELECT COUNT(*) as count FROM siswa_ruang_kelas"
    ];
    
    echo "<h3>Database Summary</h3>";
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Table</th><th style='border: 1px solid #ddd; padding: 10px;'>Records</th></tr>";
    
    foreach ($verification as $name => $query) {
        $stmt = $conn->query($query);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>$name</td><td style='border: 1px solid #ddd; padding: 10px;'>$count</td></tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 Fresh Database Setup Complete!</h3>";
    echo "<p><strong>Your new classroom system is ready to use!</strong></p>";
    echo "<p><strong>Features:</strong></p>";
    echo "<ul>";
    echo "<li>✅ Modern classroom-based system (no old kelas table)</li>";
    echo "<li>✅ Flexible student-classroom assignments</li>";
    echo "<li>✅ Semester and academic year management</li>";
    echo "<li>✅ Complete academic data structure</li>";
    echo "<li>✅ Sample data for immediate testing</li>";
    echo "</ul>";
    echo "<p><strong>Ready to test:</strong></p>";
    echo "<ol>";
    echo "<li><a href='tugas/index.php'>Create assignments (tugas)</a></li>";
    echo "<li><a href='absensi/create.php'>Take attendance (absensi)</a></li>";
    echo "<li><a href='test_classroom_migration.php'>Run system tests</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    add_step("Database setup", "error", $e->getMessage());
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Setup Failed</h3>";
    echo "<p>Error: " . $e->getMessage() . "</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Fresh Start:</strong> This database uses only the new classroom system - no legacy tables!</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
