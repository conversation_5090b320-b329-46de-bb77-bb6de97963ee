<?php
require_once 'config/database.php';

echo "<h1>Simple Classroom System Fix</h1>";
echo "<p>This script will safely fix the classroom system issues without complex transactions.</p>";

$database = new Database();
$conn = $database->getConnection();

$steps_completed = 0;
$total_steps = 6;

echo "<h2>Step-by-Step Fix Process</h2>";

// Step 1: Check current status
echo "<h3>Step 1: Checking Current Status</h3>";
try {
    // Check ruang_kelas
    $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'");
    $ruang_kelas_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div style='color: " . ($ruang_kelas_count > 0 ? 'green' : 'red') . ";'>Active ruang_kelas: $ruang_kelas_count</div>";
    
    // Check mata_pelajaran
    $stmt = $conn->query("SELECT COUNT(*) as count FROM mata_pelajaran");
    $mapel_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<div style='color: " . ($mapel_count > 0 ? 'green' : 'red') . ";'>Mata pelajaran: $mapel_count</div>";
    
    // Check tugas table structure
    $stmt = $conn->query("SHOW COLUMNS FROM tugas LIKE 'ruang_kelas_id'");
    $has_ruang_kelas_column = $stmt->rowCount() > 0;
    echo "<div style='color: " . ($has_ruang_kelas_column ? 'green' : 'red') . ";'>Tugas has ruang_kelas_id column: " . ($has_ruang_kelas_column ? 'Yes' : 'No') . "</div>";
    
    $steps_completed++;
} catch (Exception $e) {
    echo "<div style='color: red;'>Error in Step 1: " . $e->getMessage() . "</div>";
}

// Step 2: Add missing columns
echo "<h3>Step 2: Adding Missing Columns</h3>";
$columns_to_add = [
    'tugas' => 'ruang_kelas_id',
    'absensi' => 'ruang_kelas_id',
    'nilai' => 'ruang_kelas_id',
    'siswa' => 'kelas_id_backup'
];

foreach ($columns_to_add as $table => $column) {
    try {
        // Check if column exists
        $stmt = $conn->query("SHOW COLUMNS FROM `$table` LIKE '$column'");
        if ($stmt->rowCount() == 0) {
            // Add column
            $conn->exec("ALTER TABLE `$table` ADD COLUMN `$column` int(11) DEFAULT NULL");
            echo "<div style='color: green;'>✓ Added column $column to table $table</div>";
        } else {
            echo "<div style='color: blue;'>ℹ Column $column already exists in table $table</div>";
        }
    } catch (Exception $e) {
        if (strpos($e->getMessage(), "doesn't exist") !== false) {
            echo "<div style='color: orange;'>⚠ Table $table doesn't exist, skipping</div>";
        } else {
            echo "<div style='color: red;'>✗ Error with table $table: " . $e->getMessage() . "</div>";
        }
    }
}
$steps_completed++;

// Step 3: Add indexes
echo "<h3>Step 3: Adding Indexes</h3>";
$indexes = [
    'tugas' => 'ruang_kelas_id',
    'absensi' => 'ruang_kelas_id',
    'nilai' => 'ruang_kelas_id'
];

foreach ($indexes as $table => $column) {
    try {
        $conn->exec("ALTER TABLE `$table` ADD INDEX IF NOT EXISTS `idx_{$table}_ruang_kelas` (`$column`)");
        echo "<div style='color: green;'>✓ Added index for $column in table $table</div>";
    } catch (Exception $e) {
        if (strpos($e->getMessage(), 'Duplicate key') !== false) {
            echo "<div style='color: blue;'>ℹ Index already exists for $column in table $table</div>";
        } else {
            echo "<div style='color: orange;'>⚠ Could not add index to $table: " . $e->getMessage() . "</div>";
        }
    }
}
$steps_completed++;

// Step 4: Create sample data if needed
echo "<h3>Step 4: Creating Sample Data (if needed)</h3>";
try {
    // Check if we need sample ruang_kelas
    $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'");
    $ruang_kelas_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($ruang_kelas_count == 0) {
        echo "<div style='color: orange;'>No active ruang_kelas found. Creating sample data...</div>";
        $conn->exec("INSERT INTO ruang_kelas (nama_ruang_kelas, semester, tahun_ajaran, status, created_at) VALUES 
            ('X IPA 1', '1', '2024/2025', 'aktif', NOW()),
            ('X IPA 2', '1', '2024/2025', 'aktif', NOW()),
            ('XI IPA 1', '1', '2024/2025', 'aktif', NOW()),
            ('XII IPA 1', '1', '2024/2025', 'aktif', NOW())");
        echo "<div style='color: green;'>✓ Created sample ruang_kelas data</div>";
    } else {
        echo "<div style='color: blue;'>ℹ Ruang kelas data already exists ($ruang_kelas_count active classrooms)</div>";
    }
    
    // Check if we need sample mata_pelajaran
    $stmt = $conn->query("SELECT COUNT(*) as count FROM mata_pelajaran");
    $mapel_count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($mapel_count == 0) {
        echo "<div style='color: orange;'>No mata_pelajaran found. Creating sample data...</div>";
        $conn->exec("INSERT INTO mata_pelajaran (kode_mapel, nama_mapel, kkm, created_at) VALUES 
            ('MTK001', 'Matematika', 75, NOW()),
            ('BIN001', 'Bahasa Indonesia', 75, NOW()),
            ('ING001', 'Bahasa Inggris', 75, NOW()),
            ('FIS001', 'Fisika', 75, NOW()),
            ('KIM001', 'Kimia', 75, NOW())");
        echo "<div style='color: green;'>✓ Created sample mata_pelajaran data</div>";
    } else {
        echo "<div style='color: blue;'>ℹ Mata pelajaran data already exists ($mapel_count subjects)</div>";
    }
    
    $steps_completed++;
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error creating sample data: " . $e->getMessage() . "</div>";
}

// Step 5: Update existing records (safe approach)
echo "<h3>Step 5: Updating Existing Records</h3>";
try {
    // Backup kelas_id first
    $result = $conn->exec("UPDATE siswa SET kelas_id_backup = kelas_id WHERE kelas_id IS NOT NULL AND kelas_id_backup IS NULL");
    echo "<div style='color: green;'>✓ Backed up $result kelas_id values</div>";
    
    // Update tugas records (only if both tables exist and have matching data)
    $update_tugas = "
        UPDATE tugas t
        INNER JOIN kelas k ON t.kelas_id = k.id
        INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
            AND rk.tahun_ajaran = COALESCE(t.tahun_ajaran, '2024/2025')
            AND rk.semester = COALESCE(t.semester, '1')
            AND rk.status = 'aktif'
        SET t.ruang_kelas_id = rk.id
        WHERE t.ruang_kelas_id IS NULL AND t.kelas_id IS NOT NULL
    ";
    $result = $conn->exec($update_tugas);
    echo "<div style='color: green;'>✓ Updated $result tugas records with ruang_kelas_id</div>";
    
    // Update absensi records
    $update_absensi = "
        UPDATE absensi a
        INNER JOIN kelas k ON a.kelas_id = k.id
        INNER JOIN ruang_kelas rk ON rk.nama_ruang_kelas = k.nama_kelas 
            AND rk.tahun_ajaran = COALESCE(a.tahun_ajaran, '2024/2025')
            AND rk.semester = COALESCE(a.semester, '1')
            AND rk.status = 'aktif'
        SET a.ruang_kelas_id = rk.id
        WHERE a.ruang_kelas_id IS NULL AND a.kelas_id IS NOT NULL
    ";
    $result = $conn->exec($update_absensi);
    echo "<div style='color: green;'>✓ Updated $result absensi records with ruang_kelas_id</div>";
    
    $steps_completed++;
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error updating existing records: " . $e->getMessage() . "</div>";
}

// Step 6: Final verification
echo "<h3>Step 6: Final Verification</h3>";
try {
    $verification = [
        'Active Classrooms' => "SELECT COUNT(*) as count FROM ruang_kelas WHERE status = 'aktif'",
        'Subjects' => "SELECT COUNT(*) as count FROM mata_pelajaran",
        'Tugas with Classroom' => "SELECT COUNT(*) as count FROM tugas WHERE ruang_kelas_id IS NOT NULL",
        'Absensi with Classroom' => "SELECT COUNT(*) as count FROM absensi WHERE ruang_kelas_id IS NOT NULL"
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 10px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 8px;'>Item</th><th style='border: 1px solid #ddd; padding: 8px;'>Count</th></tr>";
    
    foreach ($verification as $name => $query) {
        $stmt = $conn->query($query);
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<tr><td style='border: 1px solid #ddd; padding: 8px;'>$name</td><td style='border: 1px solid #ddd; padding: 8px;'>$count</td></tr>";
    }
    echo "</table>";
    
    $steps_completed++;
} catch (Exception $e) {
    echo "<div style='color: red;'>✗ Error in verification: " . $e->getMessage() . "</div>";
}

// Summary
echo "<h2>Fix Summary</h2>";
$success_rate = ($steps_completed / $total_steps) * 100;

echo "<div style='background: " . ($success_rate >= 80 ? '#d4edda' : '#fff3cd') . "; border: 1px solid " . ($success_rate >= 80 ? '#c3e6cb' : '#ffeaa7') . "; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3>Progress: $steps_completed/$total_steps steps completed (" . round($success_rate) . "%)</h3>";

if ($success_rate >= 80) {
    echo "<p style='color: #155724;'><strong>✅ Fix completed successfully!</strong></p>";
    echo "<p>The classroom system should now work properly. Try creating a new tugas or absensi.</p>";
} else {
    echo "<p style='color: #856404;'><strong>⚠ Partial fix completed.</strong></p>";
    echo "<p>Some issues may remain. Check the error messages above and try running the debug script.</p>";
}

echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li><a href='tugas/index.php'>Test creating a new tugas</a></li>";
echo "<li><a href='absensi/create.php'>Test creating a new absensi</a></li>";
echo "<li><a href='debug_classroom_issue.php'>Run debug script if issues persist</a></li>";
echo "</ol>";
echo "</div>";

echo "<hr>";
echo "<p><strong>Safe Mode:</strong> This script doesn't use transactions, so partial fixes are preserved even if errors occur.</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
