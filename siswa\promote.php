<?php
session_start();
require_once '../config/database.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../models/Alumni.php';
require_once '../models/PeriodeAktif.php';
require_once '../models/RuangKelas.php';

// Check if user is logged in and has admin role
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../login.php');
    exit();
}

// Check if classroom system is available
$classroom_system_available = false;
try {
    $database = new Database();
    $conn = $database->getConnection();
    $stmt = $conn->query("SHOW TABLES LIKE 'ruang_kelas'");
    $classroom_system_available = $stmt->rowCount() > 0;
} catch (Exception $e) {
    $classroom_system_available = false;
}

$database = new Database();
$conn = $database->getConnection();

$siswaModel = new Siswa();
$kelasModel = new Kelas();
$alumniModel = new Alumni();
$periodeAktifModel = new PeriodeAktif();

// Get active period
$periodeAktifModel->getActive();
$tahun_ajaran = $periodeAktifModel->tahun_ajaran;
if (!$tahun_ajaran) {
    die("Periode aktif belum diatur. Silahkan atur periode aktif terlebih dahulu.");
}

$action = isset($_POST['action']) ? $_POST['action'] : '';
$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$classroom_system_available) {
    if ($action === 'promote_class') {
        $kelas_id = $_POST['kelas_id'];
        $target_kelas_id = $_POST['target_kelas_id'];
        $selected_siswa = isset($_POST['selected_siswa']) ? $_POST['selected_siswa'] : [];
        
        if (!empty($selected_siswa)) {
            try {
                foreach ($selected_siswa as $siswa_id) {
                    // Get current student data
                    $siswa = $siswaModel->getSiswaById($siswa_id);
                    
                    // Update siswa's class
                    $siswaModel->id = $siswa_id;
                    $siswaModel->nis = $siswa['nis'];
                    $siswaModel->nama_siswa = $siswa['nama_siswa'];
                    $siswaModel->jenis_kelamin = $siswa['jenis_kelamin'];
                    $siswaModel->kelas_id = $target_kelas_id;
                    $siswaModel->alamat = $siswa['alamat'];
                    $siswaModel->no_telp = $siswa['no_telp'];
                    $siswaModel->update();
                    
                    // Add to class history
                    $kelas = $kelasModel->getById($target_kelas_id);
                    
                    $query = "INSERT INTO riwayat_kelas (siswa_id, kelas_id, tahun_ajaran, status) 
                             VALUES (:siswa_id, :kelas_id, :tahun_ajaran, 'aktif')";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':siswa_id', $siswa_id);
                    $stmt->bindParam(':kelas_id', $target_kelas_id);
                    $stmt->bindParam(':tahun_ajaran', $tahun_ajaran);
                    $stmt->execute();
                }
                $message = "Siswa berhasil dipindahkan ke kelas baru.";
            } catch (Exception $e) {
                $error = "Terjadi kesalahan: " . $e->getMessage();
            }
        }
    } elseif ($action === 'graduate') {
        $kelas_id = $_POST['kelas_id'];
        $selected_siswa = isset($_POST['selected_siswa']) ? $_POST['selected_siswa'] : [];
        $tahun_lulus = $_POST['tahun_lulus'];
        
        if (!empty($selected_siswa)) {
            try {
                foreach ($selected_siswa as $siswa_id) {
                    $siswa = $siswaModel->getSiswaById($siswa_id);
                    $kelas = $kelasModel->getById($siswa['kelas_id']);
                    
                    // Add to alumni
                    $alumniData = [
                        'nis' => $siswa['nis'],
                        'nama_siswa' => $siswa['nama_siswa'],
                        'jenis_kelamin' => $siswa['jenis_kelamin'],
                        'alamat' => $siswa['alamat'],
                        'no_telp' => $siswa['no_telp'],
                        'tahun_lulus' => $tahun_lulus,
                        'kelas_terakhir' => $kelas['nama_kelas']
                    ];
                    $alumniModel->addAlumni($alumniData);
                    
                    // Update riwayat kelas
                    $query = "UPDATE riwayat_kelas SET status = 'lulus' 
                             WHERE siswa_id = :siswa_id AND kelas_id = :kelas_id";
                    $stmt = $conn->prepare($query);
                    $stmt->bindParam(':siswa_id', $siswa_id);
                    $stmt->bindParam(':kelas_id', $siswa['kelas_id']);
                    $stmt->execute();
                    
                    // Delete from siswa
                    $siswaModel->id = $siswa_id;
                    $siswaModel->delete();
                }
                $message = "Siswa berhasil diluluskan dan dipindahkan ke data alumni.";
            } catch (Exception $e) {
                $error = "Terjadi kesalahan: " . $e->getMessage();
            }
        }
    }
}

// Get all classes for the form
$kelas_list = [];
$stmt = $kelasModel->getAll();
while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
    $kelas_list[] = $row;
}

// Get current academic year
$tahun_ajaran_aktif = date('Y') . '/' . (date('Y') + 1);
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kenaikan Kelas & Kelulusan - SIHADIR</title>
    <?php include '../template/header.php'; ?>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">Kenaikan Kelas & Kelulusan</h5>
                    </div>
                    <div class="card-body">
                        <?php if ($classroom_system_available): ?>
                            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                                <h6><i class="fas fa-exclamation-triangle"></i> Fitur Ini Sudah Tidak Diperlukan</h6>
                                <p class="mb-2">Sistem ruang kelas baru telah diaktifkan. Fitur kenaikan kelas dan kelulusan manual tidak lagi diperlukan karena:</p>
                                <ul class="mb-2">
                                    <li>Data siswa tidak akan hilang saat periode berganti</li>
                                    <li>Sistem otomatis memisahkan data per semester dan tahun ajaran</li>
                                    <li>Tidak perlu memindahkan siswa secara manual</li>
                                </ul>
                                <p class="mb-0">
                                    <strong>Gunakan:</strong>
                                    <a href="../ruang_kelas/" class="btn btn-primary btn-sm">
                                        <i class="fas fa-school"></i> Kelola Ruang Kelas
                                    </a>
                                    untuk mengelola penempatan siswa.
                                </p>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($message): ?>
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <?php echo $message; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <?php if ($error): ?>
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <?php echo $error; ?>
                                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                            </div>
                        <?php endif; ?>

                        <!-- Class Selection Form -->
                        <form method="GET" class="mb-4">
                            <div class="row align-items-end">
                                <div class="col-md-6">
                                    <label for="kelas_id" class="form-label">Pilih Kelas</label>
                                    <select name="kelas_id" id="kelas_id" class="form-select" required>
                                        <option value="">Pilih Kelas</option>
                                        <?php foreach ($kelas_list as $kelas): ?>
                                            <option value="<?php echo $kelas['id']; ?>" 
                                                <?php echo (isset($_GET['kelas_id']) && $_GET['kelas_id'] == $kelas['id']) ? 'selected' : ''; ?>>
                                                <?php echo $kelas['nama_kelas']; ?>
                                            </option>
                                        <?php endforeach; ?>
                                    </select>
                                </div>
                                <div class="col-md-2">
                                    <button type="submit" class="btn btn-primary">Tampilkan Siswa</button>
                                </div>
                            </div>
                        </form>

                        <?php if (isset($_GET['kelas_id']) && !empty($_GET['kelas_id'])): 
                            $selected_kelas = $kelasModel->getById($_GET['kelas_id']);
                            $stmt = $siswaModel->getByKelas($_GET['kelas_id']);
                            $siswa_list = $stmt->fetchAll(PDO::FETCH_ASSOC);
                        ?>
                            <?php if (!$classroom_system_available): ?>
                            <div class="action-buttons mb-3">
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#promoteModal">
                                    <i class="fas fa-arrow-up"></i> Naik Kelas
                                </button>
                                <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#graduateModal">
                                    <i class="fas fa-graduation-cap"></i> Luluskan
                                </button>
                            </div>
                            <?php else: ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle"></i>
                                Fitur kenaikan kelas dan kelulusan tidak tersedia karena sistem ruang kelas sudah aktif.
                                Gunakan <a href="../ruang_kelas/">modul Ruang Kelas</a> untuk mengelola penempatan siswa.
                            </div>
                            <?php endif; ?>

                            <!-- Student List -->
                            <div class="table-responsive">
                                <table class="table table-bordered" id="dataTable">
                                    <thead>
                                        <tr>
                                            <th width="50">
                                                <input type="checkbox" id="checkAll">
                                            </th>
                                            <th>NIS</th>
                                            <th>Nama Siswa</th>
                                            <th>Jenis Kelamin</th>
                                            <th>Kelas</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($siswa_list as $siswa): ?>
                                            <tr>
                                                <td>
                                                    <input type="checkbox" class="student-checkbox" value="<?php echo $siswa['id']; ?>">
                                                </td>
                                                <td><?php echo $siswa['nis']; ?></td>
                                                <td><?php echo $siswa['nama_siswa']; ?></td>
                                                <td><?php echo $siswa['jenis_kelamin']; ?></td>
                                                <td><?php echo $selected_kelas['nama_kelas']; ?></td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>

                            <!-- Promote Modal -->
                            <div class="modal fade" id="promoteModal" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form method="POST">
                                            <input type="hidden" name="action" value="promote_class">
                                            <input type="hidden" name="kelas_id" value="<?php echo $_GET['kelas_id']; ?>">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Naik Kelas</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label for="target_kelas_id" class="form-label">Pilih Kelas Tujuan</label>
                                                    <select name="target_kelas_id" id="target_kelas_id" class="form-select" required>
                                                        <option value="">Pilih Kelas</option>
                                                        <?php foreach ($kelas_list as $kelas): ?>
                                                            <?php if ($kelas['id'] != $_GET['kelas_id']): ?>
                                                                <option value="<?php echo $kelas['id']; ?>">
                                                                    <?php echo $kelas['nama_kelas']; ?>
                                                                </option>
                                                            <?php endif; ?>
                                                        <?php endforeach; ?>
                                                    </select>
                                                </div>
                                                <div id="selected-students"></div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                <button type="submit" class="btn btn-primary">Naik Kelas</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>

                            <!-- Graduate Modal -->
                            <div class="modal fade" id="graduateModal" tabindex="-1">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <form method="POST">
                                            <input type="hidden" name="action" value="graduate">
                                            <input type="hidden" name="kelas_id" value="<?php echo $_GET['kelas_id']; ?>">
                                            <div class="modal-header">
                                                <h5 class="modal-title">Luluskan Siswa</h5>
                                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                            </div>
                                            <div class="modal-body">
                                                <div class="mb-3">
                                                    <label for="tahun_lulus" class="form-label">Tahun Lulus</label>
                                                    <input type="text" class="form-control" name="tahun_lulus" 
                                                           value="<?php echo $tahun_ajaran; ?>" readonly>
                                                    <small class="text-muted">*Tahun kelulusan mengikuti periode aktif</small>
                                                </div>
                                                <div id="selected-graduate-students"></div>
                                            </div>
                                            <div class="modal-footer">
                                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                                                <button type="submit" class="btn btn-success">Luluskan</button>
                                            </div>
                                        </form>
                                    </div>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?php include '../template/footer.php'; ?>
    <script>
        $(document).ready(function() {
            $('#dataTable').DataTable({
                pageLength: 100,  // Menampilkan 100 data per halaman
                lengthMenu: [[10, 25, 50, 100, -1], [10, 25, 50, 100, "Semua"]], // Opsi jumlah data per halaman
                language: {
                    lengthMenu: "Tampilkan _MENU_ data per halaman",
                    zeroRecords: "Data tidak ditemukan",
                    info: "Menampilkan halaman _PAGE_ dari _PAGES_",
                    infoEmpty: "Tidak ada data yang tersedia",
                    infoFiltered: "(difilter dari _MAX_ total data)",
                    search: "Cari:",
                    paginate: {
                        first: "Pertama",
                        last: "Terakhir",
                        next: "Selanjutnya",
                        previous: "Sebelumnya"
                    }
                }
            });

            const checkAll = document.getElementById('checkAll');
            const studentCheckboxes = document.querySelectorAll('.student-checkbox');
            
            // Handle check all functionality
            if (checkAll) {
                checkAll.addEventListener('change', function() {
                    const allCheckboxes = document.querySelectorAll('.student-checkbox');
                    allCheckboxes.forEach(checkbox => {
                        checkbox.checked = checkAll.checked;
                    });
                });

                // Update checkAll state when individual checkboxes change
                studentCheckboxes.forEach(checkbox => {
                    checkbox.addEventListener('change', function() {
                        const allChecked = Array.from(studentCheckboxes).every(cb => cb.checked);
                        checkAll.checked = allChecked;
                    });
                });
            }

            // Handle modals
            const promoteModal = document.getElementById('promoteModal');
            const graduateModal = document.getElementById('graduateModal');

            if (promoteModal) {
                promoteModal.addEventListener('show.bs.modal', function() {
                    const selectedStudents = Array.from(document.querySelectorAll('.student-checkbox'))
                        .filter(cb => cb.checked)
                        .map(cb => cb.value);
                    
                    document.getElementById('selected-students').innerHTML = '';
                    selectedStudents.forEach(studentId => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'selected_siswa[]';
                        input.value = studentId;
                        document.getElementById('selected-students').appendChild(input);
                    });
                });
            }

            if (graduateModal) {
                graduateModal.addEventListener('show.bs.modal', function() {
                    const selectedStudents = Array.from(document.querySelectorAll('.student-checkbox'))
                        .filter(cb => cb.checked)
                        .map(cb => cb.value);
                    
                    document.getElementById('selected-graduate-students').innerHTML = '';
                    selectedStudents.forEach(studentId => {
                        const input = document.createElement('input');
                        input.type = 'hidden';
                        input.name = 'selected_siswa[]';
                        input.value = studentId;
                        document.getElementById('selected-graduate-students').appendChild(input);
                    });
                });
            }
        });
    </script>
</body>
</html>
