<?php
require_once 'config/database.php';

echo "<h1>Super Simple Database Setup</h1>";
echo "<p>Minimal setup - just the essentials that work!</p>";

$database = new Database();
$conn = $database->getConnection();

if (!isset($_GET['confirm']) || $_GET['confirm'] !== 'yes') {
    echo "<div style='background: #e3f2fd; border: 1px solid #2196f3; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3>🚀 Super Simple Setup</h3>";
    echo "<p>This creates only the essential tables with matching data structure.</p>";
    echo "<p><strong>100% Safe - No complex constraints!</strong></p>";
    echo "<p><a href='?confirm=yes' style='background: #4caf50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px;'>Create Database</a></p>";
    echo "</div>";
    exit;
}

try {
    echo "<h2>Creating Tables...</h2>";
    
    // Step 1: Drop existing tables to start fresh
    echo "<div style='color: blue;'>🔄 Cleaning existing tables...</div>";
    $tables = ['nilai_tugas', 'detail_absensi', 'absensi', 'tugas', 'siswa_ruang_kelas', 'siswa', 'ruang_kelas', 'mata_pelajaran'];
    foreach ($tables as $table) {
        try {
            $conn->exec("DROP TABLE IF EXISTS `$table`");
        } catch (Exception $e) {
            // Ignore errors
        }
    }
    
    // Step 2: Create tables with exact structure needed
    echo "<div style='color: green;'>✅ Creating mata_pelajaran table...</div>";
    $conn->exec("
    CREATE TABLE `mata_pelajaran` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `kode_mapel` varchar(10) NOT NULL,
      `nama_mapel` varchar(100) NOT NULL,
      `kkm` decimal(5,2) DEFAULT 75.00,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<div style='color: green;'>✅ Creating ruang_kelas table...</div>";
    $conn->exec("
    CREATE TABLE `ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nama_ruang_kelas` varchar(100) NOT NULL,
      `semester` enum('1','2') NOT NULL DEFAULT '1',
      `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
      `status` enum('aktif','nonaktif') DEFAULT 'aktif',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<div style='color: green;'>✅ Creating siswa table...</div>";
    $conn->exec("
    CREATE TABLE `siswa` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `nis` varchar(20) NOT NULL,
      `nama_siswa` varchar(100) NOT NULL,
      `jenis_kelamin` enum('L','P') NOT NULL,
      `status` enum('aktif','lulus','pindah','keluar') DEFAULT 'aktif',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<div style='color: green;'>✅ Creating siswa_ruang_kelas table...</div>";
    $conn->exec("
    CREATE TABLE `siswa_ruang_kelas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `siswa_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `tanggal_masuk` date NOT NULL,
      `status` enum('aktif','pindah','lulus','keluar') DEFAULT 'aktif',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<div style='color: green;'>✅ Creating tugas table...</div>";
    $conn->exec("
    CREATE TABLE `tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `mapel_id` int(11) NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `judul` varchar(255) NOT NULL,
      `deskripsi` text DEFAULT NULL,
      `tanggal` date NOT NULL,
      `semester` enum('1','2') NOT NULL DEFAULT '1',
      `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<div style='color: green;'>✅ Creating absensi table...</div>";
    $conn->exec("
    CREATE TABLE `absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tanggal` date NOT NULL,
      `ruang_kelas_id` int(11) NOT NULL,
      `mapel_id` int(11) NOT NULL,
      `semester` enum('1','2') NOT NULL DEFAULT '1',
      `tahun_ajaran` varchar(9) NOT NULL DEFAULT '2024/2025',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<div style='color: green;'>✅ Creating detail_absensi table...</div>";
    $conn->exec("
    CREATE TABLE `detail_absensi` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `absensi_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `status` enum('hadir','sakit','izin','alpha') NOT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<div style='color: green;'>✅ Creating nilai_tugas table...</div>";
    $conn->exec("
    CREATE TABLE `nilai_tugas` (
      `id` int(11) NOT NULL AUTO_INCREMENT,
      `tugas_id` int(11) NOT NULL,
      `siswa_id` int(11) NOT NULL,
      `nilai` decimal(5,2) DEFAULT NULL,
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
    ");
    
    echo "<h2>Inserting Sample Data...</h2>";
    
    // Insert mata pelajaran
    echo "<div style='color: green;'>✅ Inserting mata pelajaran...</div>";
    $conn->exec("INSERT INTO mata_pelajaran (kode_mapel, nama_mapel, kkm) VALUES 
        ('MTK001', 'Matematika', 75.00),
        ('BIN001', 'Bahasa Indonesia', 75.00),
        ('ING001', 'Bahasa Inggris', 75.00),
        ('FIS001', 'Fisika', 75.00),
        ('KIM001', 'Kimia', 75.00),
        ('BIO001', 'Biologi', 75.00)");
    
    // Insert ruang kelas
    echo "<div style='color: green;'>✅ Inserting ruang kelas...</div>";
    $conn->exec("INSERT INTO ruang_kelas (nama_ruang_kelas, semester, tahun_ajaran, status) VALUES 
        ('X IPA 1', '1', '2024/2025', 'aktif'),
        ('X IPA 2', '1', '2024/2025', 'aktif'),
        ('X IPS 1', '1', '2024/2025', 'aktif'),
        ('XI IPA 1', '1', '2024/2025', 'aktif'),
        ('XI IPA 2', '1', '2024/2025', 'aktif'),
        ('XI IPS 1', '1', '2024/2025', 'aktif')");
    
    // Insert siswa
    echo "<div style='color: green;'>✅ Inserting siswa...</div>";
    $conn->exec("INSERT INTO siswa (nis, nama_siswa, jenis_kelamin, status) VALUES 
        ('2024001', 'Andi Pratama', 'L', 'aktif'),
        ('2024002', 'Sari Dewi', 'P', 'aktif'),
        ('2024003', 'Budi Setiawan', 'L', 'aktif'),
        ('2024004', 'Rina Sari', 'P', 'aktif'),
        ('2024005', 'Doni Kurniawan', 'L', 'aktif'),
        ('2024006', 'Maya Putri', 'P', 'aktif'),
        ('2024007', 'Rudi Hartono', 'L', 'aktif'),
        ('2024008', 'Lina Marlina', 'P', 'aktif')");
    
    // Insert student assignments
    echo "<div style='color: green;'>✅ Assigning students to classrooms...</div>";
    $conn->exec("INSERT INTO siswa_ruang_kelas (siswa_id, ruang_kelas_id, tanggal_masuk, status) VALUES 
        (1, 1, '2024-07-01', 'aktif'),
        (2, 1, '2024-07-01', 'aktif'),
        (3, 2, '2024-07-01', 'aktif'),
        (4, 2, '2024-07-01', 'aktif'),
        (5, 3, '2024-07-01', 'aktif'),
        (6, 3, '2024-07-01', 'aktif'),
        (7, 4, '2024-07-01', 'aktif'),
        (8, 4, '2024-07-01', 'aktif')");
    
    echo "<h2>Verification</h2>";
    
    // Verify data
    $verification = [
        'mata_pelajaran' => $conn->query("SELECT COUNT(*) FROM mata_pelajaran")->fetchColumn(),
        'ruang_kelas' => $conn->query("SELECT COUNT(*) FROM ruang_kelas WHERE status = 'aktif'")->fetchColumn(),
        'siswa' => $conn->query("SELECT COUNT(*) FROM siswa WHERE status = 'aktif'")->fetchColumn(),
        'siswa_ruang_kelas' => $conn->query("SELECT COUNT(*) FROM siswa_ruang_kelas WHERE status = 'aktif'")->fetchColumn()
    ];
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Table</th><th style='border: 1px solid #ddd; padding: 10px;'>Records</th></tr>";
    
    foreach ($verification as $table => $count) {
        $color = $count > 0 ? 'green' : 'red';
        echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>$table</td><td style='border: 1px solid #ddd; padding: 10px; color: $color;'>$count</td></tr>";
    }
    echo "</table>";
    
    // Show sample assignments
    echo "<h3>Sample Student Assignments</h3>";
    $stmt = $conn->query("
        SELECT s.nama_siswa, rk.nama_ruang_kelas 
        FROM siswa s 
        JOIN siswa_ruang_kelas srk ON s.id = srk.siswa_id 
        JOIN ruang_kelas rk ON srk.ruang_kelas_id = rk.id 
        WHERE srk.status = 'aktif'
        ORDER BY rk.nama_ruang_kelas, s.nama_siswa
    ");
    
    echo "<table style='border-collapse: collapse; width: 100%; margin: 20px 0;'>";
    echo "<tr style='background: #f8f9fa;'><th style='border: 1px solid #ddd; padding: 10px;'>Student</th><th style='border: 1px solid #ddd; padding: 10px;'>Classroom</th></tr>";
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<tr><td style='border: 1px solid #ddd; padding: 10px;'>{$row['nama_siswa']}</td><td style='border: 1px solid #ddd; padding: 10px;'>{$row['nama_ruang_kelas']}</td></tr>";
    }
    echo "</table>";
    
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #155724;'>🎉 Database Setup Complete!</h3>";
    echo "<p><strong>Your classroom system is ready!</strong></p>";
    echo "<p><strong>What's created:</strong></p>";
    echo "<ul>";
    echo "<li>✅ 6 mata pelajaran</li>";
    echo "<li>✅ 6 ruang kelas aktif</li>";
    echo "<li>✅ 8 siswa sample</li>";
    echo "<li>✅ Student-classroom assignments</li>";
    echo "<li>✅ All tables for tugas and absensi</li>";
    echo "</ul>";
    echo "<p><strong>Test now:</strong></p>";
    echo "<ol>";
    echo "<li><a href='tugas/index.php' style='color: #155724; font-weight: bold;'>Create Assignment (Tugas)</a></li>";
    echo "<li><a href='absensi/create.php' style='color: #155724; font-weight: bold;'>Take Attendance (Absensi)</a></li>";
    echo "<li><a href='debug_classroom_issue.php' style='color: #155724; font-weight: bold;'>Run Diagnostics</a></li>";
    echo "</ol>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div style='background: #f8d7da; border: 1px solid #f5c6cb; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
    echo "<h3 style='color: #721c24;'>❌ Setup Failed</h3>";
    echo "<p><strong>Error:</strong> " . $e->getMessage() . "</p>";
    echo "<p><strong>Line:</strong> " . $e->getLine() . "</p>";
    echo "<p>Please try the manual SQL approach instead.</p>";
    echo "</div>";
}

echo "<hr>";
echo "<p><strong>Super Simple:</strong> Clean tables, exact structure match, no foreign keys!</p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
table { border-collapse: collapse; }
th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
th { background-color: #f8f9fa; }
</style>
