<?php
require_once 'config/database.php';
require_once 'models/Tugas.php';
require_once 'models/Absensi.php';

echo "<h1>Classroom System Migration Test</h1>";

$database = new Database();
$conn = $database->getConnection();

$tests_passed = 0;
$tests_failed = 0;

function test_result($test_name, $passed, $message = '') {
    global $tests_passed, $tests_failed;
    
    if ($passed) {
        $tests_passed++;
        echo "<div style='color: green; margin: 10px 0;'>✓ $test_name: PASSED</div>";
    } else {
        $tests_failed++;
        echo "<div style='color: red; margin: 10px 0;'>✗ $test_name: FAILED - $message</div>";
    }
}

echo "<h2>1. Database Structure Tests</h2>";

// Test 1: Check if ruang_kelas table exists
try {
    $stmt = $conn->query("SHOW TABLES LIKE 'ruang_kelas'");
    test_result("Ruang Kelas Table Exists", $stmt->rowCount() > 0, "ruang_kelas table not found");
} catch (Exception $e) {
    test_result("Ruang Kelas Table Exists", false, $e->getMessage());
}

// Test 2: Check if siswa_ruang_kelas table exists
try {
    $stmt = $conn->query("SHOW TABLES LIKE 'siswa_ruang_kelas'");
    test_result("Siswa Ruang Kelas Table Exists", $stmt->rowCount() > 0, "siswa_ruang_kelas table not found");
} catch (Exception $e) {
    test_result("Siswa Ruang Kelas Table Exists", false, $e->getMessage());
}

// Test 3: Check if tugas table has ruang_kelas_id column
try {
    $stmt = $conn->query("SHOW COLUMNS FROM tugas LIKE 'ruang_kelas_id'");
    test_result("Tugas Table Has ruang_kelas_id", $stmt->rowCount() > 0, "ruang_kelas_id column not found in tugas table");
} catch (Exception $e) {
    test_result("Tugas Table Has ruang_kelas_id", false, $e->getMessage());
}

// Test 4: Check if absensi table has ruang_kelas_id column
try {
    $stmt = $conn->query("SHOW COLUMNS FROM absensi LIKE 'ruang_kelas_id'");
    test_result("Absensi Table Has ruang_kelas_id", $stmt->rowCount() > 0, "ruang_kelas_id column not found in absensi table");
} catch (Exception $e) {
    test_result("Absensi Table Has ruang_kelas_id", false, $e->getMessage());
}

echo "<h2>2. Data Migration Tests</h2>";

// Test 5: Check if ruang_kelas has data
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM ruang_kelas");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    test_result("Ruang Kelas Has Data", $result['count'] > 0, "No data found in ruang_kelas table");
    if ($result['count'] > 0) {
        echo "<div style='margin-left: 20px; color: blue;'>Found {$result['count']} classrooms</div>";
    }
} catch (Exception $e) {
    test_result("Ruang Kelas Has Data", false, $e->getMessage());
}

// Test 6: Check if students are assigned to classrooms
try {
    $stmt = $conn->query("SELECT COUNT(*) as count FROM siswa_ruang_kelas WHERE status = 'aktif'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    test_result("Students Assigned to Classrooms", $result['count'] > 0, "No active student assignments found");
    if ($result['count'] > 0) {
        echo "<div style='margin-left: 20px; color: blue;'>Found {$result['count']} active student assignments</div>";
    }
} catch (Exception $e) {
    test_result("Students Assigned to Classrooms", false, $e->getMessage());
}

// Test 7: Check if existing tugas records have ruang_kelas_id
try {
    $stmt = $conn->query("SELECT COUNT(*) as total, COUNT(ruang_kelas_id) as with_classroom FROM tugas");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $percentage = $result['total'] > 0 ? ($result['with_classroom'] / $result['total']) * 100 : 0;
    test_result("Tugas Records Migrated", $percentage >= 80, "Only {$result['with_classroom']}/{$result['total']} tugas records have ruang_kelas_id");
    if ($result['total'] > 0) {
        echo "<div style='margin-left: 20px; color: blue;'>Migration: {$result['with_classroom']}/{$result['total']} records (" . round($percentage, 1) . "%)</div>";
    }
} catch (Exception $e) {
    test_result("Tugas Records Migrated", false, $e->getMessage());
}

echo "<h2>3. Model Functionality Tests</h2>";

// Test 8: Test Tugas model getActiveClassrooms
try {
    $tugas = new Tugas();
    $classrooms = $tugas->getActiveClassrooms();
    test_result("Tugas Model getActiveClassrooms", $classrooms !== false, "getActiveClassrooms returned false");
    
    if ($classrooms) {
        $count = 0;
        while ($classrooms->fetch(PDO::FETCH_ASSOC)) {
            $count++;
        }
        echo "<div style='margin-left: 20px; color: blue;'>Found $count active classrooms via Tugas model</div>";
    }
} catch (Exception $e) {
    test_result("Tugas Model getActiveClassrooms", false, $e->getMessage());
}

// Test 9: Test Absensi model getActiveClassrooms
try {
    $absensi = new Absensi();
    $classrooms = $absensi->getActiveClassrooms();
    test_result("Absensi Model getActiveClassrooms", $classrooms !== false, "getActiveClassrooms returned false");
    
    if ($classrooms) {
        $count = 0;
        while ($classrooms->fetch(PDO::FETCH_ASSOC)) {
            $count++;
        }
        echo "<div style='margin-left: 20px; color: blue;'>Found $count active classrooms via Absensi model</div>";
    }
} catch (Exception $e) {
    test_result("Absensi Model getActiveClassrooms", false, $e->getMessage());
}

echo "<h2>4. CRUD Operation Tests</h2>";

// Test 10: Test creating a new tugas with classroom system
try {
    // Get a sample classroom and subject
    $stmt = $conn->query("SELECT id FROM ruang_kelas WHERE status = 'aktif' LIMIT 1");
    $classroom = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $stmt = $conn->query("SELECT id FROM mata_pelajaran LIMIT 1");
    $subject = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($classroom && $subject) {
        $tugas = new Tugas();
        $tugas->mapel_id = $subject['id'];
        $tugas->ruang_kelas_id = $classroom['id'];
        $tugas->judul = "Test Migration Tugas - " . date('Y-m-d H:i:s');
        $tugas->deskripsi = "Test tugas for migration verification";
        $tugas->tanggal = date('Y-m-d');
        $tugas->semester = "1";
        $tugas->tahun_ajaran = "2024/2025";
        
        $created = $tugas->create();
        test_result("Create Tugas with Classroom", $created, "Failed to create tugas with classroom system");
        
        if ($created) {
            echo "<div style='margin-left: 20px; color: blue;'>Created tugas with ID: {$tugas->id}</div>";
            
            // Clean up
            $tugas->delete();
            echo "<div style='margin-left: 20px; color: gray;'>Test tugas deleted</div>";
        }
    } else {
        test_result("Create Tugas with Classroom", false, "No classroom or subject data available for testing");
    }
} catch (Exception $e) {
    test_result("Create Tugas with Classroom", false, $e->getMessage());
}

echo "<h2>5. Data Integrity Tests</h2>";

// Test 11: Check for orphaned records
try {
    $stmt = $conn->query("
        SELECT COUNT(*) as count 
        FROM tugas t 
        LEFT JOIN ruang_kelas rk ON t.ruang_kelas_id = rk.id 
        WHERE t.ruang_kelas_id IS NOT NULL AND rk.id IS NULL
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    test_result("No Orphaned Tugas Records", $result['count'] == 0, "Found {$result['count']} tugas records with invalid ruang_kelas_id");
} catch (Exception $e) {
    test_result("No Orphaned Tugas Records", false, $e->getMessage());
}

// Test 12: Check classroom-student consistency
try {
    $stmt = $conn->query("
        SELECT COUNT(*) as count 
        FROM siswa_ruang_kelas srk 
        LEFT JOIN ruang_kelas rk ON srk.ruang_kelas_id = rk.id 
        LEFT JOIN siswa s ON srk.siswa_id = s.id
        WHERE srk.status = 'aktif' AND (rk.id IS NULL OR s.id IS NULL)
    ");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    test_result("Student-Classroom Consistency", $result['count'] == 0, "Found {$result['count']} inconsistent student-classroom assignments");
} catch (Exception $e) {
    test_result("Student-Classroom Consistency", false, $e->getMessage());
}

echo "<h2>Migration Test Summary</h2>";

$total_tests = $tests_passed + $tests_failed;
$success_rate = $total_tests > 0 ? ($tests_passed / $total_tests) * 100 : 0;

echo "<div style='background: " . ($success_rate >= 90 ? '#d4edda' : ($success_rate >= 70 ? '#fff3cd' : '#f8d7da')) . "; padding: 20px; margin: 20px 0; border-radius: 5px;'>";
echo "<h3>Results:</h3>";
echo "<p><strong>Tests Passed:</strong> $tests_passed</p>";
echo "<p><strong>Tests Failed:</strong> $tests_failed</p>";
echo "<p><strong>Success Rate:</strong> " . round($success_rate, 1) . "%</p>";

if ($success_rate >= 90) {
    echo "<p style='color: green; font-weight: bold;'>✅ Migration appears to be successful! The classroom system is ready for use.</p>";
} elseif ($success_rate >= 70) {
    echo "<p style='color: orange; font-weight: bold;'>⚠️ Migration partially successful. Some issues need to be addressed.</p>";
} else {
    echo "<p style='color: red; font-weight: bold;'>❌ Migration has significant issues. Please review and fix the problems.</p>";
}
echo "</div>";

echo "<hr>";
echo "<p><a href='tugas/index.php'>Test Tugas System</a> | <a href='absensi/create.php'>Test Absensi System</a> | <a href='index.php'>Return to Dashboard</a></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
h1, h2, h3 { color: #333; }
</style>
