<?php
session_start();
require_once '../config/database.php';
require_once '../models/Tugas.php';

if (!isset($_GET['id'])) {
    $_SESSION['error'] = "ID tugas tidak ditemukan!";
    header("Location: index.php");
    exit();
}

$tugas_id = $_GET['id'];
$tugas = new Tugas();
$tugas->id = $tugas_id;

// Get tugas details before deleting for redirect
if ($tugas->getOne()) {
    $mapel_id = $tugas->mapel_id;
    $semester = $tugas->semester;
    $tahun_ajaran = $tugas->tahun_ajaran;

    if ($tugas->delete()) {
        $_SESSION['success'] = "Tugas berhasil dihapus!";
        header("Location: tugas.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
    } else {
        $_SESSION['error'] = "Gagal menghapus tugas!";
        header("Location: tugas.php?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
    }
} else {
    $_SESSION['error'] = "Data tugas tidak ditemukan!";
    header("Location: index.php");
}
exit();
?>
