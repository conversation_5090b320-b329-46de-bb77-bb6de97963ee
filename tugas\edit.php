<?php
require_once '../config/database.php';
require_once '../models/Tugas.php';
require_once '../template/header.php';

if(!isset($_GET['id'])) {
    $_SESSION['error'] = "ID tugas tidak ditemukan!";
    header("Location: index.php");
    exit();
}

$tugas = new Tugas();
$tugas->id = $_GET['id'];
if(!$tugas->getOne()) {
    $_SESSION['error'] = "Data tugas tidak ditemukan!";
    header("Location: index.php");
    exit();
}

if($_SERVER['REQUEST_METHOD'] == 'POST') {
    // Validate required fields
    $errors = [];

    if (empty($_POST['judul'])) {
        $errors[] = "Judul tugas harus diisi";
    }

    if (empty($_POST['tanggal'])) {
        $errors[] = "Tanggal harus diisi";
    }

    if (empty($errors)) {
        $tugas->judul = $_POST['judul'];
        $tugas->deskripsi = $_POST['deskripsi'];
        $tugas->tanggal = $_POST['tanggal'];

        if($tugas->update()) {
            $_SESSION['success'] = "Tugas berhasil diperbarui!";
            header("Location: tugas.php?mapel_id=" . $tugas->mapel_id . "&semester=" . $tugas->semester . "&tahun_ajaran=" . $tugas->tahun_ajaran);
            exit();
        } else {
            $_SESSION['error'] = "Gagal memperbarui tugas!";
        }
    } else {
        $_SESSION['error'] = implode("<br>", $errors);
    }
}

// Get messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Edit Tugas</h5>
                    <small class="text-muted">Semester <?php echo $tugas->semester; ?> - Tahun Ajaran <?php echo $tugas->tahun_ajaran; ?></small>
                </div>
                <a href="tugas.php?mapel_id=<?php echo $tugas->mapel_id; ?>&semester=<?php echo $tugas->semester; ?>&tahun_ajaran=<?php echo $tugas->tahun_ajaran; ?>" 
                   class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali
                </a>
            </div>
        </div>

        <?php if ($success_msg): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_msg): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-body">
                <form method="POST" action="" id="formEditTugas">
                    <div class="mb-3">
                        <label for="judul" class="form-label">Judul Tugas <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="judul" name="judul"
                               value="<?php echo htmlspecialchars($tugas->judul); ?>" required maxlength="255">
                    </div>

                    <div class="mb-3">
                        <label for="deskripsi" class="form-label">Deskripsi Tugas</label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi"
                                  rows="4" maxlength="1000"><?php echo htmlspecialchars($tugas->deskripsi); ?></textarea>
                        <small class="form-text text-muted">Maksimal 1000 karakter</small>
                    </div>

                    <div class="mb-3">
                        <label for="tanggal" class="form-label">Tanggal Pemberian <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal"
                               value="<?php echo $tugas->tanggal; ?>" required>
                    </div>

                    <div class="text-end mt-3">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan Perubahan
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Form validation for edit task
    $('#formEditTugas').on('submit', function(e) {
        let isValid = true;
        let errors = [];

        // Validate judul
        const judul = $('#judul').val().trim();
        if (!judul) {
            errors.push('Judul tugas harus diisi');
            isValid = false;
        }

        // Validate tanggal
        const tanggal = $('#tanggal').val();
        if (!tanggal) {
            errors.push('Tanggal harus diisi');
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            alert('Mohon perbaiki kesalahan berikut:\n• ' + errors.join('\n• '));
            return false;
        }

        return true;
    });

    // Character counter for description
    $('#deskripsi').on('input', function() {
        const maxLength = 1000;
        const currentLength = $(this).val().length;
        const remaining = maxLength - currentLength;

        let counterElement = $(this).siblings('.char-counter');
        if (counterElement.length === 0) {
            counterElement = $('<small class="char-counter text-muted"></small>');
            $(this).after(counterElement);
        }

        counterElement.text(currentLength + '/' + maxLength + ' karakter');

        if (remaining < 50) {
            counterElement.removeClass('text-muted').addClass('text-warning');
        } else {
            counterElement.removeClass('text-warning').addClass('text-muted');
        }
    });

    // Trigger character counter on page load
    $('#deskripsi').trigger('input');
});
</script>

<?php require_once '../template/footer.php'; ?>
