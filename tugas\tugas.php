<?php
require_once '../config/database.php';
require_once '../models/MataPelajaran.php';
require_once '../models/Tugas.php';
require_once '../models/Siswa.php';
require_once '../models/Kelas.php';
require_once '../template/header.php';

if(!isset($_GET['mapel_id']) || !isset($_GET['semester']) || !isset($_GET['tahun_ajaran'])) {
    header("Location: index.php");
    exit();
}

$mapel_id = $_GET['mapel_id'];
$semester = $_GET['semester'];
$tahun_ajaran = $_GET['tahun_ajaran'];

$mapel = new MataPelajaran();
$mapel->id = $mapel_id;
$mapel->getOne();

$kelas = new Kelas();
$kelas_list = $kelas->getAll();

$tugas = new Tugas();
$result_tugas = $tugas->getTugasMapel($mapel_id, $semester, $tahun_ajaran);

// Handle form submission
if($_SERVER['REQUEST_METHOD'] == 'POST') {
    if(isset($_POST['add_tugas'])) {
        $tugas->mapel_id = $mapel_id;

        // Support both classroom and legacy class system
        if (isset($_POST['ruang_kelas_id']) && !empty($_POST['ruang_kelas_id'])) {
            $tugas->ruang_kelas_id = $_POST['ruang_kelas_id'];
        } else {
            $tugas->kelas_id = $_POST['kelas_id'];
        }

        $tugas->judul = $_POST['judul'];
        $tugas->deskripsi = $_POST['deskripsi'];
        $tugas->tanggal = $_POST['tanggal'];
        $tugas->semester = $semester;
        $tugas->tahun_ajaran = $tahun_ajaran;

        if($tugas->create()) {
            $_SESSION['success'] = "Tugas berhasil ditambahkan!";
            header("Location: ?mapel_id=$mapel_id&semester=$semester&tahun_ajaran=$tahun_ajaran");
            exit();
        } else {
            $_SESSION['error'] = "Gagal menambahkan tugas!";
        }
    }
}

// Get messages
$success_msg = isset($_SESSION['success']) ? $_SESSION['success'] : '';
$error_msg = isset($_SESSION['error']) ? $_SESSION['error'] : '';
unset($_SESSION['success'], $_SESSION['error']);
?>

<div class="row">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <h5 class="card-title mb-0">Data Tugas - <?php echo $mapel->nama_mapel; ?></h5>
                    <small class="text-muted">Semester <?php echo $semester; ?> - Tahun Ajaran <?php echo $tahun_ajaran; ?></small>
                </div>
                <a href="index.php" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Kembali ke Daftar Mata Pelajaran
                </a>
            </div>
        </div>

        <?php if ($success_msg): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo $success_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if ($error_msg): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo $error_msg; ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>
        
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Tambah Tugas Baru</h5>
                <button type="button" class="btn btn-primary" data-bs-toggle="collapse" data-bs-target="#formTugas">
                    <i class="fas fa-plus"></i> Tambah Tugas
                </button>
            </div>
            <div class="card-body collapse" id="formTugas">
                <form method="post" action="">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <?php if ($tugas->isClassroomSystemAvailable()): ?>
                                <label for="ruang_kelas_id" class="form-label">Ruang Kelas</label>
                                <select name="ruang_kelas_id" id="ruang_kelas_id" class="form-select" required>
                                    <option value="">Pilih Ruang Kelas</option>
                                    <?php
                                    $classroom_list = $tugas->getActiveClassrooms();
                                    if ($classroom_list):
                                        while ($row = $classroom_list->fetch(PDO::FETCH_ASSOC)):
                                    ?>
                                        <option value="<?php echo $row['id']; ?>">
                                            <?php echo htmlspecialchars($row['nama_ruang_kelas']); ?>
                                            (<?php echo $row['jumlah_siswa']; ?> siswa)
                                        </option>
                                    <?php
                                        endwhile;
                                    endif;
                                    ?>
                                </select>
                                <small class="form-text text-muted">Sistem ruang kelas baru - data terpisah per semester/tahun ajaran</small>
                            <?php else: ?>
                                <label for="kelas_id" class="form-label">Kelas</label>
                                <select name="kelas_id" id="kelas_id" class="form-select" required>
                                    <option value="">Pilih Kelas</option>
                                    <?php while ($row = $kelas_list->fetch(PDO::FETCH_ASSOC)): ?>
                                        <option value="<?php echo $row['id']; ?>">
                                            <?php echo htmlspecialchars($row['nama_kelas']); ?>
                                        </option>
                                    <?php endwhile; ?>
                                </select>
                                <small class="form-text text-muted">Sistem kelas lama - <a href="/absen/maintenance/classroom_migration.php">upgrade ke sistem ruang kelas</a></small>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="judul" class="form-label">Judul Tugas</label>
                            <input type="text" class="form-control" id="judul" name="judul" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="deskripsi" class="form-label">Deskripsi</label>
                        <textarea class="form-control" id="deskripsi" name="deskripsi" rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label for="tanggal" class="form-label">Tanggal</label>
                        <input type="date" class="form-control" id="tanggal" name="tanggal" required>
                    </div>
                    <div class="text-end">
                        <button type="submit" name="add_tugas" class="btn btn-primary">
                            <i class="fas fa-save"></i> Simpan
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">Daftar Tugas</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="tableTugas">
                        <thead>
                            <tr>
                                <th>No</th>
                                <th>Tanggal</th>
                                <th>Kelas</th>
                                <th>Judul Tugas</th>
                                <th>Deskripsi</th>
                                <th>Aksi</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php
                            $no = 1;
                            while($row = $result_tugas->fetch(PDO::FETCH_ASSOC)) {
                                echo "<tr>";
                                echo "<td>" . $no++ . "</td>";
                                echo "<td>" . date('d/m/Y', strtotime($row['tanggal'])) . "</td>";
                                echo "<td>" . htmlspecialchars($row['nama_kelas']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['judul']) . "</td>";
                                echo "<td>" . htmlspecialchars($row['deskripsi']) . "</td>";
                                echo "<td>
                                        <div class='btn-group'>
                                            <a href='nilai_tugas.php?tugas_id=" . $row['id'] . "' class='btn btn-primary btn-sm'>
                                                <i class='fas fa-edit'></i> Input Nilai
                                            </a>
                                            <a href='edit.php?id=" . $row['id'] . "' class='btn btn-warning btn-sm'>
                                                <i class='fas fa-pencil-alt'></i> Edit
                                            </a>
                                            <a href='delete.php?id=" . $row['id'] . "' class='btn btn-danger btn-sm' 
                                               onclick='return confirm(\"Apakah Anda yakin ingin menghapus tugas ini?\");'>
                                                <i class='fas fa-trash'></i> Hapus
                                            </a>
                                        </div>
                                      </td>";
                                echo "</tr>";
                            }
                            ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#tableTugas').DataTable({
        "pageLength": 25,
        "language": {
            "url": "//cdn.datatables.net/plug-ins/1.13.7/i18n/id.json"
        },
        "order": [[1, "desc"]] // Sort by date descending
    });
});
</script>

<?php require_once '../template/footer.php'; ?>
